{"name": "server", "version": "0.1.0", "description": "", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsup src/index.ts", "start": "node dist/index.js", "postinstall": "test -f .env || cp .env.example .env", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:seed": "prisma db seed", "pm2": "pm2 start ecosystem.config.cjs", "pm2:stop": "pm2 stop proto-api-server", "pm2:restart": "pm2 restart proto-api-server", "pm2:delete": "pm2 delete proto-api-server", "pm2:logs": "pm2 logs proto-api-server", "pm2:monit": "pm2 monit", "zip": "pnpm build && zip -r proto-api-server.zip dist package.json prisma ecosystem.config.cjs tsconfig.json .env.example public"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@prisma/client": "6.6.0", "@trpc/server": "^11.1.1", "@types/babel__core": "^7.20.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.2", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dayjs": "^1.11.13", "esbuild": "^0.25.3", "express": "^5.1.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "multer": "1.4.5-lts.2", "node-cron": "^4.0.5", "openai": "^4.98.0", "prisma": "^6.6.0", "protobufjs": "^7.5.0", "trpc-ui": "^1.0.15", "tsup": "^8.4.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node-cron": "^3.0.11"}}