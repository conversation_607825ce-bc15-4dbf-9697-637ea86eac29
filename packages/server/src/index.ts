import express from 'express'
import cors from 'cors'
import { createExpressMiddleware } from '@trpc/server/adapters/express'
import { appRouter } from './routes'
// import { uploadRouter } from './routes/upload'
// import { getFileRouter } from './routes/upload'
import { createContext } from './trpc'
import './schedule'

const app = express()

app.use(cors())
app.use(
  '/trpc',
  createExpressMiddleware({
    router: appRouter,
    createContext,
  }),
)
// 静态文件
app.use(express.static('public'))

// app.use('/api/upload', uploadRouter)
// app.use('/api/file', getFileRouter)

const port = 9700
app.listen(port, () => {
  console.log(`Server listening on port ${port}`)
})
