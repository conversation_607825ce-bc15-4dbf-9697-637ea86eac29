import express from 'express'
import cors from 'cors'
import { createExpressMiddleware } from '@trpc/server/adapters/express'
import { appRouter } from './routes'
// import { uploadRouter } from './routes/upload'
// import { getFileRouter } from './routes/upload'
import { createContext } from './trpc'
import './schedule'
import { renderTrpcPanel } from 'trpc-ui'
import { safeJsonParse } from './utils/common'

const app = express()

app.use(cors())

app.use('/trpc/docs', (req, res) => {
  const html = renderTrpcPanel(appRouter, {
    url: '/api/trpc/',
    meta: {
      title: 'ProtoAPI',
      description: 'ProtoAPI API Docs',
    },
  })
  const jsonContent = '{' + html.split('rootRouter:{')[1].split(',options:_H')[0]
  const json = safeJsonParse(jsonContent)
  // 暂时先返回一点数据，便于分析 json 结构
  // json.children = { user: json.children.user }
  res.json(json)
})

// 原有的trpc路由（用于网关认证）
app.use(
  '/trpc',
  createExpressMiddleware({
    router: appRouter,
    createContext,
  }),
)

// 新的API路由（用于token认证）
app.use(
  '/api/trpc',
  createExpressMiddleware({
    router: appRouter,
    createContext,
  }),
)
// 静态文件
app.use(express.static('public'))

// app.use('/api/upload', uploadRouter)
// app.use('/api/file', getFileRouter)

const port = 9700
app.listen(port, () => {
  console.log(`Server listening on port ${port}`)
})
