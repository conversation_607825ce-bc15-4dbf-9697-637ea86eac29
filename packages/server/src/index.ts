import express from 'express'
import cors from 'cors'
import { createExpressMiddleware } from '@trpc/server/adapters/express'
import { appRouter } from './routes'
// import { uploadRouter } from './routes/upload'
// import { getFileRouter } from './routes/upload'
import { createContext } from './trpc'
import './schedule'

const app = express()

app.use(cors())

// 原有的trpc路由（用于网关认证）
app.use(
  '/trpc',
  createExpressMiddleware({
    router: appRouter,
    createContext,
  }),
)

// 新的API路由（用于token认证）
app.use(
  '/api/trpc',
  createExpressMiddleware({
    router: appRouter,
    createContext,
  }),
)
// 静态文件
app.use(express.static('public'))

// app.use('/api/upload', uploadRouter)
// app.use('/api/file', getFileRouter)

const port = 9700
app.listen(port, () => {
  console.log(`Server listening on port ${port}`)
})
