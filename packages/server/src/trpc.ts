import { initTRPC, TRPCError } from '@trpc/server'
import type { CreateExpressContextOptions } from '@trpc/server/adapters/express'
import { isDev } from './utils/env'
import * as jose from 'jose'
import crypto from 'crypto'
import { prisma } from './prisma'
import { z } from 'zod'
import { ProjectMemberRole } from '../prisma/prisma'

// 太湖应用 token
const TAI_KEY = process.env.TAI_KEY || '' // 使用示例中提供的密钥
// 安全模式，生产环境应设置为 true
const IDENTITY_SAFE_MODE = process.env.IDENTITY_SAFE_MODE === 'true' || true // 默认为 false，兼容模式

// 定义用户类型
// interface JwtUser {
//   username: string
//   is_admin?: boolean
//   staffId?: number
//   chineseName?: string | null
// }

// 定义太湖身份信息类型
interface TaiIdentity {
  Expiration?: string
  StaffId: number
  LoginName: string
  ChineseName: string
  DeptId: number
  DeptName: string
  // [key: string]: any // 允许其他字段
}

// 解密太湖身份信息
async function decodeAuthorizationHeader(
  authorizationHeader: string,
  keyBytes: Buffer,
): Promise<TaiIdentity> {
  try {
    const dec = await jose.compactDecrypt(authorizationHeader, keyBytes)
    const plaintext = dec.plaintext

    // 如果是字节数组格式（逗号分隔的ASCII码），则转换为字符串
    if (plaintext.toString().match(/^[\d,]+$/)) {
      // 将逗号分隔的ASCII码转换为字符串
      const byteArray = plaintext.toString().split(',').map(Number)

      // 如果包含非ASCII字符，尝试使用UTF-8解码
      if (byteArray.some(b => b < 0 || b > 127)) {
        const buffer = Buffer.from(byteArray)
        const decodedString = buffer.toString('utf8')

        try {
          // 解析JSON
          const payload = JSON.parse(decodedString) as TaiIdentity
          return payload
        } catch (error) {
          // 解析失败，继续尝试其他方法
        }
      }

      // 尝试直接使用String.fromCharCode
      const decodedString = String.fromCharCode(...byteArray)
      const payload = JSON.parse(decodedString) as TaiIdentity
      return payload
    } else {
      // 正常的JSON字符串处理
      const cleanedText = plaintext
        .toString()
        .replace(/^\uFEFF/, '')
        .trim()

      try {
        return JSON.parse(cleanedText) as TaiIdentity
      } catch (error) {
        throw new Error('Invalid JSON format')
      }
    }
  } catch (error) {
    throw new Error('Failed to decode authorization header')
  }
}

// 验证签名
function checkSignature(
  key: string,
  timestampSeconds: string,
  signature: string,
  extHeaders: string[],
): boolean {
  if (!timestampSeconds || isNaN(Number(timestampSeconds))) {
    return false
  }

  // 验证时间戳是否在有效期内（3分钟）
  const isTimestampValid = Math.abs(parseInt(timestampSeconds, 10) * 1000 - Date.now()) <= 180000
  if (!isTimestampValid && !isDev) {
    return false
  }

  const hash = crypto.createHash('sha256')
  hash.update(timestampSeconds + key + extHeaders.join(',') + timestampSeconds)
  return signature.toLowerCase() === hash.digest('hex').toLowerCase()
}

// 获取用户身份信息
async function getIdentity(headers: Record<string, string>): Promise<TaiIdentity> {
  // 开发环境下返回默认用户
  if (isDev) {
    return {
      StaffId: 0,
      LoginName: 'dev_user',
      DeptId: 0,
      DeptName: '',
      ChineseName: '',
    }
  }

  // 检查关键头信息是否存在
  const hasTaiIdentity = !!headers['x-tai-identity']
  const hasStaffInfo = !!(headers['staffid'] && headers['staffname'])

  if (!hasTaiIdentity && !hasStaffInfo) {
    throw new Error('Missing required authentication headers')
  }

  // 验证签名
  let extHeaders = [headers['x-rio-seq'] || '', '', '', '']
  if (!IDENTITY_SAFE_MODE) {
    extHeaders = [
      headers['x-rio-seq'] || '',
      headers['staffid'] || '',
      headers['staffname'] || '',
      headers['x-ext-data'] || '',
    ]
  }

  const hasValidSignature = checkSignature(
    TAI_KEY,
    headers.timestamp || '',
    headers.signature || '',
    extHeaders,
  )

  if (!hasValidSignature && !isDev) {
    throw new Error('Invalid signature')
  }

  // 尝试从 x-tai-identity 获取用户信息
  if (hasTaiIdentity) {
    try {
      const keyBytes = Buffer.from(TAI_KEY)
      const payload = await decodeAuthorizationHeader(headers['x-tai-identity'], keyBytes)

      if (payload && payload.LoginName && payload.StaffId) {
        return payload
      }
    } catch (error) {
      // 解密失败，继续尝试其他方法
    }
  }

  // 如果无法从 x-tai-identity 获取，尝试从 headers 中获取
  if (hasStaffInfo) {
    return {
      StaffId: parseInt(headers['staffid'], 10) || 0,
      LoginName: headers['staffname'],
      DeptId: 0,
      DeptName: '',
      ChineseName: '',
    }
  }

  // 如果所有方法都失败，抛出错误
  throw new Error('Could not extract user identity')
}

export async function createContext({ req, res }: CreateExpressContextOptions) {
  let taiIdentity: TaiIdentity | null = null
  let tokenUser: any = null

  // 首先尝试Bearer token认证
  const authHeader = req.headers.authorization
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7)
    try {
      // 查找使用该token的用户
      const user = await prisma.user.findUnique({
        where: { apiToken: token },
        select: {
          username: true,
          chineseName: true,
          staffId: true,
          deptId: true,
          deptName: true,
        },
      })

      if (user) {
        tokenUser = user
        // 为token用户创建taiIdentity格式
        taiIdentity = {
          StaffId: user.staffId || 0,
          LoginName: user.username,
          ChineseName: user.chineseName || '',
          DeptId: user.deptId || 0,
          DeptName: user.deptName || '',
        }
      }
    } catch (error) {
      // token认证失败，继续尝试其他认证方式
    }
  }

  // 如果token认证失败，尝试原有的太湖认证
  if (!taiIdentity) {
    try {
      if (req.headers) {
        const headers = req.headers as Record<string, string>
        const identity = await getIdentity(headers)

        if (identity.LoginName) {
          taiIdentity = identity
        }
      }
    } catch (error) {
      // 认证失败，不设置用户信息
    }
  }

  // 开发环境中设置默认用户
  if (isDev && !taiIdentity) {
    taiIdentity = {
      StaffId: 0,
      LoginName: 'dev_user',
      ChineseName: '',
      DeptId: 0,
      DeptName: '',
    }
  }

  return { req, res, taiIdentity, tokenUser }
}

// 导出上下文类型，供其他文件使用
export type Context = Awaited<ReturnType<typeof createContext>>

export const t = initTRPC.context<Context>().create({
  errorFormatter: ({ shape }) => ({
    ...shape,
    data: isDev ? shape.data : undefined,
  }),
  // 添加SSE选项
  sse: {
    // 启用ping，保持连接活跃
    ping: {
      enabled: true,
      intervalMs: 15000, // 每15秒发送一次ping
    },
    // 客户端选项
    client: {
      // 如果20秒内没有收到消息或ping，重新连接
      reconnectAfterInactivityMs: 20000,
    },
  },
})

export const router = t.router
export const publicProcedure = t.procedure

// 创建一个需要认证的中间件
export const protectedProcedure = t.procedure.use(async ({ ctx, next }) => {
  if (!ctx.taiIdentity) {
    throw new TRPCError({ code: 'UNAUTHORIZED', message: '您没有权限访问' })
  }

  try {
    // 检查用户是否存在，如果不存在则创建
    let existingUser = await prisma.user.findUnique({
      where: { username: ctx.taiIdentity.LoginName },
    })

    if (!existingUser) {
      // 创建新用户
      existingUser = await prisma.user.create({
        data: {
          username: ctx.taiIdentity.LoginName,
          chineseName: ctx.taiIdentity.ChineseName,
          is_admin: false,
          staffId: ctx.taiIdentity.StaffId,
          deptId: ctx.taiIdentity.DeptId,
          deptName: ctx.taiIdentity.DeptName,
        },
      })
    }
    const needUpdate =
      existingUser.staffId !== ctx.taiIdentity.StaffId ||
      existingUser.deptId !== ctx.taiIdentity.DeptId ||
      existingUser.deptName !== ctx.taiIdentity.DeptName ||
      existingUser.chineseName !== ctx.taiIdentity.ChineseName

    if (needUpdate) {
      await prisma.user.update({
        where: { username: existingUser.username },
        data: {
          staffId: ctx.taiIdentity.StaffId,
          deptId: ctx.taiIdentity.DeptId,
          deptName: ctx.taiIdentity.DeptName,
          chineseName: ctx.taiIdentity.ChineseName,
        },
      })
    }

    return next({
      ctx: {
        ...ctx,
        user: existingUser,
      },
    })
  } catch (error) {
    throw new TRPCError({ code: 'UNAUTHORIZED', message: '您没有权限访问' })
  }
})

/**
 * 管理员权限
 */
export const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  // if (!ctx.user?.is_admin) {
  //   throw new TRPCError({ code: 'FORBIDDEN', message: '您没有管理员权限' })
  // }
  return next({ ctx })
})

export const projectProcedure = protectedProcedure
  .input(
    z.object({
      projectId: z.number(),
    }),
  )
  .use(async ({ ctx, next, input }) => {
    const project = await prisma.project.findUnique({
      where: { id: input.projectId },
      include: {
        createdByUser: { select: { username: true, chineseName: true, gitAccessToken: true } },
      },
    })
    const member = await prisma.projectMember.findUnique({
      where: {
        projectId_username: { projectId: input.projectId, username: ctx.user.username },
      },
    })
    if (!project) {
      throw new TRPCError({ code: 'NOT_FOUND', message: '项目不存在' })
    } else if (!member) {
      throw new TRPCError({ code: 'FORBIDDEN', message: '您没有权限访问' })
    }
    return next({ ctx: { ...ctx, project, member } })
  })

export const projectOwnerProcedure = projectProcedure.use(async ({ ctx, next }) => {
  if (ctx.member?.role !== ProjectMemberRole.OWNER) {
    throw new TRPCError({ code: 'FORBIDDEN', message: '您没有管理员权限' })
  }
  return next({ ctx })
})

export const projectWriteProcedure = projectProcedure.use(async ({ ctx, next }) => {
  if (
    ctx.member?.role !== ProjectMemberRole.OWNER &&
    ctx.member?.role !== ProjectMemberRole.WRITE
  ) {
    throw new TRPCError({ code: 'FORBIDDEN', message: '您没有编辑权限' })
  }
  return next({ ctx })
})
