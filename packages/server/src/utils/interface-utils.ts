/**
 * 生成接口路径的工具函数
 */
export function generateInterfacePath(
  pattern: string,
  data: {
    serviceName: string
    methodName: string
    protoFileName: string
    projectName: string
  },
) {
  const { serviceName, methodName, protoFileName, projectName } = data

  // 替换占位符
  return pattern
    .replace(/\${SERVICE_NAME}/g, serviceName)
    .replace(/\${METHOD_NAME}/g, methodName)
    .replace(/\${PROTO_FILE_NAME}/g, protoFileName)
    .replace(/\${PROJECT_NAME}/g, projectName)
    .replace(/\${SERVICE_NAME_LOWERCASE}/g, serviceName.toLowerCase())
    .replace(/\${METHOD_NAME_LOWERCASE}/g, methodName.toLowerCase())
    .replace(/\${PROTO_FILE_NAME_LOWERCASE}/g, protoFileName.toLowerCase())
    .replace(/\${PROJECT_NAME_LOWERCASE}/g, projectName.toLowerCase())
}
