import chalk from 'chalk'

export class Logger {
  private static formatTime(): string {
    return new Date().toLocaleTimeString('zh-CN')
  }

  static info(message: string): void {
    console.log(chalk.blue(`[${this.formatTime()}] ℹ ${message}`))
  }

  static success(message: string): void {
    console.log(chalk.green(`[${this.formatTime()}] ✅ ${message}`))
  }

  static warning(message: string): void {
    console.log(chalk.yellow(`[${this.formatTime()}] ⚠️ ${message}`))
  }

  static error(message: string): void {
    console.log(chalk.red(`[${this.formatTime()}] ❌ ${message}`))
  }

  static step(message: string): void {
    console.log(chalk.cyan(`[${this.formatTime()}] 🚀 ${message}`))
  }

  static debug(message: string): void {
    if (process.env.DEBUG) {
      console.log(chalk.gray(`[${this.formatTime()}] 🐛 ${message}`))
    }
  }

  static separator(): void {
    console.log(chalk.gray('─'.repeat(60)))
  }

  static title(message: string): void {
    console.log()
    console.log(chalk.bold.magenta(`🌟 ${message}`))
    this.separator()
  }
}
