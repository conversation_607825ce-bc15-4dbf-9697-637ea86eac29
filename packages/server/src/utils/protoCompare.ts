import { safeJsonParse } from './common'

/**
 * 比较两个 Proto 文件版本，识别变更
 * @param oldProto 旧版本的 Proto 文件解析结果
 * @param newProto 新版本的 Proto 文件解析结果
 * @returns 包含变更信息的对象
 */
export function compareProtoVersions(oldProto: any, newProto: any) {
  if (!oldProto || !newProto) {
    return {
      services: {},
      methods: {},
      messages: {},
      enums: {},
      changeType: 'unknown',
    }
  }

  const oldRoot = oldProto.root?.nested || {}
  const newRoot = newProto.root?.nested || {}

  // 提取组件
  const oldComponents = extractComponents(oldRoot)
  const newComponents = extractComponents(newRoot)

  // 比较服务
  const serviceChanges = compareServices(oldComponents, newComponents)
  
  // 比较消息
  const messageChanges = compareMessages(oldComponents.messages, newComponents.messages)
  
  // 比较枚举
  const enumChanges = compareEnums(oldComponents.enums, newComponents.enums)

  return {
    services: serviceChanges.services,
    methods: serviceChanges.methods,
    messages: messageChanges,
    enums: enumChanges,
    changeType: determineOverallChangeType(serviceChanges, messageChanges, enumChanges),
  }
}

/**
 * 递归提取所有消息、枚举和服务
 */
function extractComponents(nested: Record<string, any>, prefix = '') {
  let messages: Record<string, any> = {}
  let enums: Record<string, any> = {}
  let services: Record<string, any> = {}
  let shortServices: Record<string, any> = {}
  let serviceNames: string[] = []
  let shortServiceNames: string[] = []

  for (const key in nested) {
    const fullName = prefix ? `${prefix}.${key}` : key
    const item = nested[key]

    if (item.fields) {
      // 这是一个消息
      messages[fullName] = item
    } else if (item.values) {
      // 这是一个枚举
      enums[fullName] = item
    } else if (item.methods) {
      // 这是一个服务
      services[fullName] = item
      serviceNames.push(fullName)
      shortServiceNames.push(key)
      shortServices[key] = item
    }

    // 递归处理嵌套项
    if (item.nested) {
      const {
        messages: nestedMessages,
        enums: nestedEnums,
        services: nestedServices,
        serviceNames: nestedServiceNames,
        shortServices: nestedShortServices,
        shortServiceNames: nestedShortServiceNames,
      } = extractComponents(item.nested, fullName)

      messages = { ...messages, ...nestedMessages }
      enums = { ...enums, ...nestedEnums }
      services = { ...services, ...nestedServices }
      serviceNames = [...serviceNames, ...nestedServiceNames]
      shortServices = { ...shortServices, ...nestedShortServices }
      shortServiceNames = [...shortServiceNames, ...nestedShortServiceNames]
    }
  }

  return { messages, enums, services, serviceNames, shortServices, shortServiceNames }
}

/**
 * 比较服务和方法
 */
function compareServices(oldComponents: any, newComponents: any) {
  const serviceChanges: Record<string, { changeType: string; comment?: string }> = {}
  const methodChanges: Record<string, Record<string, { changeType: string; comment?: string; requestType?: string; responseType?: string }>> = {}

  // 检查新增和修改的服务
  for (const serviceName of newComponents.serviceNames) {
    const shortName = serviceName.split('.').pop() || serviceName
    
    if (!oldComponents.services[serviceName]) {
      // 新增的服务
      serviceChanges[shortName] = { changeType: 'added' }
      
      // 所有方法都标记为新增
      const methods = newComponents.services[serviceName].methods || {}
      methodChanges[shortName] = {}
      
      for (const methodName in methods) {
        methodChanges[shortName][methodName] = { 
          changeType: 'added',
          comment: methods[methodName].comment,
          requestType: methods[methodName].requestType,
          responseType: methods[methodName].responseType
        }
      }
    } else {
      // 比较服务的方法
      const oldMethods = oldComponents.services[serviceName].methods || {}
      const newMethods = newComponents.services[serviceName].methods || {}
      
      let hasMethodChanges = false
      methodChanges[shortName] = {}
      
      // 检查新增和修改的方法
      for (const methodName in newMethods) {
        const newMethod = newMethods[methodName]
        const oldMethod = oldMethods[methodName]
        
        if (!oldMethod) {
          // 新增的方法
          methodChanges[shortName][methodName] = { 
            changeType: 'added',
            comment: newMethod.comment,
            requestType: newMethod.requestType,
            responseType: newMethod.responseType
          }
          hasMethodChanges = true
        } else {
          // 检查方法是否有变化
          const isCommentChanged = newMethod.comment !== oldMethod.comment
          const isRequestTypeChanged = newMethod.requestType !== oldMethod.requestType
          const isResponseTypeChanged = newMethod.responseType !== oldMethod.responseType
          const isOptionsChanged = JSON.stringify(newMethod.options) !== JSON.stringify(oldMethod.options)
          
          if (isCommentChanged || isRequestTypeChanged || isResponseTypeChanged || isOptionsChanged) {
            methodChanges[shortName][methodName] = { 
              changeType: 'modified',
              comment: newMethod.comment,
              requestType: newMethod.requestType,
              responseType: newMethod.responseType
            }
            hasMethodChanges = true
          } else {
            methodChanges[shortName][methodName] = { 
              changeType: 'unchanged',
              comment: newMethod.comment,
              requestType: newMethod.requestType,
              responseType: newMethod.responseType
            }
          }
        }
      }
      
      // 检查删除的方法
      for (const methodName in oldMethods) {
        if (!newMethods[methodName]) {
          methodChanges[shortName][methodName] = { 
            changeType: 'deleted',
            comment: oldMethods[methodName].comment,
            requestType: oldMethods[methodName].requestType,
            responseType: oldMethods[methodName].responseType
          }
          hasMethodChanges = true
        }
      }
      
      // 如果有方法变化，则服务也标记为修改
      if (hasMethodChanges) {
        serviceChanges[shortName] = { changeType: 'modified' }
      } else {
        serviceChanges[shortName] = { changeType: 'unchanged' }
      }
    }
  }
  
  // 检查删除的服务
  for (const serviceName of oldComponents.serviceNames) {
    const shortName = serviceName.split('.').pop() || serviceName
    
    if (!newComponents.services[serviceName]) {
      // 删除的服务
      serviceChanges[shortName] = { changeType: 'deleted' }
      
      // 所有方法都标记为删除
      const methods = oldComponents.services[serviceName].methods || {}
      methodChanges[shortName] = {}
      
      for (const methodName in methods) {
        methodChanges[shortName][methodName] = { 
          changeType: 'deleted',
          comment: methods[methodName].comment,
          requestType: methods[methodName].requestType,
          responseType: methods[methodName].responseType
        }
      }
    }
  }
  
  return { services: serviceChanges, methods: methodChanges }
}

/**
 * 比较消息
 */
function compareMessages(oldMessages: Record<string, any>, newMessages: Record<string, any>) {
  const messageChanges: Record<string, { 
    changeType: string; 
    fields?: Record<string, { 
      changeType: string; 
      type?: string; 
      comment?: string;
      oldType?: string;
      oldComment?: string;
    }> 
  }> = {}
  
  // 检查新增和修改的消息
  for (const messageName in newMessages) {
    const shortName = messageName.split('.').pop() || messageName
    
    if (!oldMessages[messageName]) {
      // 新增的消息
      messageChanges[messageName] = { 
        changeType: 'added',
        fields: {}
      }
      
      // 所有字段都标记为新增
      const fields = newMessages[messageName].fields || {}
      
      for (const fieldName in fields) {
        messageChanges[messageName].fields![fieldName] = { 
          changeType: 'added',
          type: fields[fieldName].type,
          comment: fields[fieldName].comment
        }
      }
    } else {
      // 比较消息的字段
      const oldFields = oldMessages[messageName].fields || {}
      const newFields = newMessages[messageName].fields || {}
      
      let hasFieldChanges = false
      messageChanges[messageName] = { 
        changeType: 'unchanged',
        fields: {}
      }
      
      // 检查新增和修改的字段
      for (const fieldName in newFields) {
        const newField = newFields[fieldName]
        const oldField = oldFields[fieldName]
        
        if (!oldField) {
          // 新增的字段
          messageChanges[messageName].fields![fieldName] = { 
            changeType: 'added',
            type: newField.type,
            comment: newField.comment
          }
          hasFieldChanges = true
        } else {
          // 检查字段是否有变化
          const isTypeChanged = newField.type !== oldField.type
          const isCommentChanged = newField.comment !== oldField.comment
          const isRuleChanged = newField.rule !== oldField.rule
          
          if (isTypeChanged || isCommentChanged || isRuleChanged) {
            messageChanges[messageName].fields![fieldName] = { 
              changeType: 'modified',
              type: newField.type,
              comment: newField.comment,
              oldType: isTypeChanged ? oldField.type : undefined,
              oldComment: isCommentChanged ? oldField.comment : undefined
            }
            hasFieldChanges = true
          } else {
            messageChanges[messageName].fields![fieldName] = { 
              changeType: 'unchanged',
              type: newField.type,
              comment: newField.comment
            }
          }
        }
      }
      
      // 检查删除的字段
      for (const fieldName in oldFields) {
        if (!newFields[fieldName]) {
          messageChanges[messageName].fields![fieldName] = { 
            changeType: 'deleted',
            type: oldFields[fieldName].type,
            comment: oldFields[fieldName].comment
          }
          hasFieldChanges = true
        }
      }
      
      // 如果有字段变化，则消息也标记为修改
      if (hasFieldChanges) {
        messageChanges[messageName].changeType = 'modified'
      }
    }
  }
  
  // 检查删除的消息
  for (const messageName in oldMessages) {
    if (!newMessages[messageName]) {
      // 删除的消息
      messageChanges[messageName] = { 
        changeType: 'deleted',
        fields: {}
      }
      
      // 所有字段都标记为删除
      const fields = oldMessages[messageName].fields || {}
      
      for (const fieldName in fields) {
        messageChanges[messageName].fields![fieldName] = { 
          changeType: 'deleted',
          type: fields[fieldName].type,
          comment: fields[fieldName].comment
        }
      }
    }
  }
  
  return messageChanges
}

/**
 * 比较枚举
 */
function compareEnums(oldEnums: Record<string, any>, newEnums: Record<string, any>) {
  const enumChanges: Record<string, { 
    changeType: string; 
    values?: Record<string, { 
      changeType: string; 
      value?: number | string; 
      comment?: string;
    }> 
  }> = {}
  
  // 检查新增和修改的枚举
  for (const enumName in newEnums) {
    if (!oldEnums[enumName]) {
      // 新增的枚举
      enumChanges[enumName] = { 
        changeType: 'added',
        values: {}
      }
      
      // 所有值都标记为新增
      const values = newEnums[enumName].values || {}
      
      for (const valueName in values) {
        enumChanges[enumName].values![valueName] = { 
          changeType: 'added',
          value: values[valueName],
          comment: newEnums[enumName].comments?.[valueName]
        }
      }
    } else {
      // 比较枚举的值
      const oldValues = oldEnums[enumName].values || {}
      const newValues = newEnums[enumName].values || {}
      
      let hasValueChanges = false
      enumChanges[enumName] = { 
        changeType: 'unchanged',
        values: {}
      }
      
      // 检查新增和修改的值
      for (const valueName in newValues) {
        const newValue = newValues[valueName]
        const oldValue = oldValues[valueName]
        
        if (oldValue === undefined) {
          // 新增的值
          enumChanges[enumName].values![valueName] = { 
            changeType: 'added',
            value: newValue,
            comment: newEnums[enumName].comments?.[valueName]
          }
          hasValueChanges = true
        } else {
          // 检查值是否有变化
          const isValueChanged = newValue !== oldValue
          const isCommentChanged = 
            newEnums[enumName].comments?.[valueName] !== oldEnums[enumName].comments?.[valueName]
          
          if (isValueChanged || isCommentChanged) {
            enumChanges[enumName].values![valueName] = { 
              changeType: 'modified',
              value: newValue,
              comment: newEnums[enumName].comments?.[valueName]
            }
            hasValueChanges = true
          } else {
            enumChanges[enumName].values![valueName] = { 
              changeType: 'unchanged',
              value: newValue,
              comment: newEnums[enumName].comments?.[valueName]
            }
          }
        }
      }
      
      // 检查删除的值
      for (const valueName in oldValues) {
        if (newValues[valueName] === undefined) {
          enumChanges[enumName].values![valueName] = { 
            changeType: 'deleted',
            value: oldValues[valueName],
            comment: oldEnums[enumName].comments?.[valueName]
          }
          hasValueChanges = true
        }
      }
      
      // 如果有值变化，则枚举也标记为修改
      if (hasValueChanges) {
        enumChanges[enumName].changeType = 'modified'
      }
    }
  }
  
  // 检查删除的枚举
  for (const enumName in oldEnums) {
    if (!newEnums[enumName]) {
      // 删除的枚举
      enumChanges[enumName] = { 
        changeType: 'deleted',
        values: {}
      }
      
      // 所有值都标记为删除
      const values = oldEnums[enumName].values || {}
      
      for (const valueName in values) {
        enumChanges[enumName].values![valueName] = { 
          changeType: 'deleted',
          value: values[valueName],
          comment: oldEnums[enumName].comments?.[valueName]
        }
      }
    }
  }
  
  return enumChanges
}

/**
 * 确定整体变更类型
 */
function determineOverallChangeType(
  serviceChanges: { services: Record<string, { changeType: string }>, methods: Record<string, any> },
  messageChanges: Record<string, { changeType: string }>,
  enumChanges: Record<string, { changeType: string }>
) {
  // 检查是否有新增或删除
  const hasAddedOrDeleted = 
    Object.values(serviceChanges.services).some(s => s.changeType === 'added' || s.changeType === 'deleted') ||
    Object.values(messageChanges).some(m => m.changeType === 'added' || m.changeType === 'deleted') ||
    Object.values(enumChanges).some(e => e.changeType === 'added' || e.changeType === 'deleted')
  
  if (hasAddedOrDeleted) {
    return 'major'
  }
  
  // 检查是否有修改
  const hasModified = 
    Object.values(serviceChanges.services).some(s => s.changeType === 'modified') ||
    Object.values(messageChanges).some(m => m.changeType === 'modified') ||
    Object.values(enumChanges).some(e => e.changeType === 'modified')
  
  if (hasModified) {
    return 'minor'
  }
  
  return 'unchanged'
}
