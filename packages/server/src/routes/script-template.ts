// import { z } from 'zod'
// import { publicProcedure, router } from '../trpc'
// import { prisma } from '../prisma'
// import { TRPCError } from '@trpc/server'
// import { covertScriptToEs5 } from '../services/script'

// // 模板可见性枚举
// const TemplateVisibilityEnum = z.enum(['PUBLIC', 'PRIVATE', 'OPEN'])
// type TemplateVisibility = z.infer<typeof TemplateVisibilityEnum>

// // 脚本语言枚举
// const ScriptLangEnum = z.enum(['JS', 'TS'])
// type ScriptLang = z.infer<typeof ScriptLangEnum>

// // 检查用户是否有模板访问权限
// const hasTemplateAccess = async (templateId: number, username: string): Promise<boolean> => {
//   const template = await prisma.scriptTemplate.findUnique({
//     where: { id: templateId },
//     include: {
//       projects: {
//         include: {
//           members: {
//             where: {
//               username,
//             },
//           },
//         },
//       },
//     },
//   })

//   if (!template) {
//     return false
//   }

//   // 如果用户是模板创建者，则有权限
//   if (template.createdBy === username) {
//     return true
//   }

//   // 如果模板是 PUBLIC，则所有人都有权限
//   if (template.auth === 'PUBLIC') {
//     return true
//   }

//   // 如果模板是 OPEN，并且用户是项目的成员，则有权限
//   if (
//     template.auth === 'OPEN' &&
//     template.projects.some(project => project.members.some(member => member.username === username))
//   ) {
//     return true
//   }

//   // 如果是 PRIVATE 但用户不是创建者，则无权限
//   return false
// }

// export const scriptTemplateRouter = router({
//   // 列出用户可访问的模板
//   list: publicProcedure
//     .input(
//       z
//         .object({
//           projectId: z.number().optional(),
//         })
//         .optional(),
//     )
//     .query(async ({ ctx, input }) => {
//       const username = ctx.user?.username || 'anonymous'
//       const projectId = input?.projectId

//       // 基本查询条件：用户创建的 PRIVATE 模板或所有 PUBLIC 模板
//       const baseWhere = {
//         OR: [
//           { creator: username }, // 用户创建的模板
//           { auth: 'PUBLIC' }, // 公共模板
//         ],
//       } as const

//       // 如果指定了项目 ID，则增加过滤条件
//       if (projectId !== undefined) {
//         // 检查用户是否为项目成员
//         const isMember = await prisma.projectMember.findUnique({
//           where: {
//             projectId_username: {
//               projectId,
//               username,
//             },
//           },
//         })

//         // 项目创建者检查
//         const project = await prisma.project.findUnique({
//           where: { id: projectId },
//           select: { createdBy: true },
//         })

//         const isProjectMember = isMember !== null || project?.createdBy === username

//         // 用户不是项目成员，只能看到该项目的 PUBLIC 模板
//         if (!isProjectMember) {
//           return await prisma.scriptTemplate.findMany({
//             where: {
//               auth: 'PUBLIC',
//             },
//           })
//         }

//         // 用户是项目成员，可以看到该项目的 PUBLIC、OPEN 模板，以及自己创建的 PRIVATE 模板
//         return await prisma.scriptTemplate.findMany({
//           where: {
//             projects: {
//               some: {
//                 id: projectId,
//               },
//             },
//             OR: [{ auth: 'PUBLIC' }, { auth: 'OPEN' }, { auth: 'PRIVATE', createdBy: username }],
//           },
//         })
//       }

//       // 没有指定项目 ID，返回所有符合条件的模板
//       const templates = await prisma.scriptTemplate.findMany({
//         // where: baseWhere,
//       })

//       return templates.filter(template => {
//         if (template.auth === 'PUBLIC') {
//           return true
//         }

//         if (template.auth === 'OPEN') {
//           return true
//         }

//         if (template.auth === 'PRIVATE' && template.createdBy === username) {
//           return true
//         }
//       })

//       // return templates
//     }),

//   // 获取模板详情
//   getById: publicProcedure
//     .input(
//       z.object({
//         id: z.number(),
//       }),
//     )
//     .query(async ({ ctx, input }) => {
//       const username = ctx.user?.username || 'anonymous'
//       const { id } = input

//       // 检查用户是否有权限访问此模板
//       const hasAccess = await hasTemplateAccess(id, username)
//       if (!hasAccess) {
//         throw new TRPCError({
//           code: 'FORBIDDEN',
//           message: '您没有权限访问此模板',
//         })
//       }

//       // 获取模板详情
//       const template = await prisma.scriptTemplate.findUnique({
//         where: { id },
//       })

//       if (!template) {
//         throw new TRPCError({
//           code: 'NOT_FOUND',
//           message: '模板不存在',
//         })
//       }

//       return template
//     }),

//   // 创建模板
//   create: publicProcedure
//     .input(
//       z.object({
//         projectId: z.number(),
//         script: z.string().min(1),
//         lang: ScriptLangEnum,
//         auth: TemplateVisibilityEnum,
//         title: z.string().min(1),
//         desc: z.string().optional(),
//       }),
//     )
//     .mutation(async ({ ctx, input }) => {
//       const username = ctx.user?.username || 'anonymous'
//       const { projectId, script, lang, auth, title, desc } = input

//       // 检查项目是否存在
//       const project = await prisma.project.findUnique({
//         where: { id: projectId },
//       })

//       if (!project) {
//         throw new TRPCError({
//           code: 'NOT_FOUND',
//           message: '项目不存在',
//         })
//       }

//       // 检查用户是否有项目的访问权限
//       const projectMember = await prisma.projectMember.findUnique({
//         where: {
//           projectId_username: {
//             projectId,
//             username,
//           },
//         },
//       })

//       const isCreator = project.createdBy === username

//       if (!isCreator && !projectMember) {
//         throw new TRPCError({
//           code: 'FORBIDDEN',
//           message: '您没有权限在此项目中创建模板',
//         })
//       }

//       // 创建模板
//       const template = await prisma.scriptTemplate.create({
//         data: {
//           script,
//           scriptEs5: await covertScriptToEs5(script, lang),
//           lang,
//           auth,
//           createdBy: username,
//           title,
//           desc: desc || '',
//           projects: {
//             connect: {
//               id: projectId,
//             },
//           },
//         },
//       })

//       return template
//     }),

//   // 更新模板
//   update: publicProcedure
//     .input(
//       z.object({
//         id: z.number(),
//         script: z.string().min(1),
//         lang: ScriptLangEnum.optional(),
//         auth: TemplateVisibilityEnum.optional(),
//       }),
//     )
//     .mutation(async ({ ctx, input }) => {
//       const username = ctx.user?.username || 'anonymous'
//       const { id, script, lang, auth } = input

//       // 获取模板详情，检查是否是创建者
//       const template = await prisma.scriptTemplate.findUnique({
//         where: { id },
//         include: {
//           projects: {
//             select: { createdBy: true },
//           },
//         },
//       })

//       if (!template) {
//         throw new TRPCError({
//           code: 'NOT_FOUND',
//           message: '模板不存在',
//         })
//       }

//       // 只有创建者或项目创建者可以更新模板
//       if (
//         template.createdBy !== username &&
//         template.projects.some(project => project.createdBy !== username)
//       ) {
//         throw new TRPCError({
//           code: 'FORBIDDEN',
//           message: '您没有权限更新此模板',
//         })
//       }

//       // 更新模板
//       const updatedTemplate = await prisma.scriptTemplate.update({
//         where: { id },
//         data: {
//           ...(script && { script }),
//           ...(lang && { lang }),
//           ...(auth && { auth }),
//           scriptEs5: await covertScriptToEs5(script, lang || template.lang),
//         },
//       })

//       return updatedTemplate
//     }),

//   // 删除模板
//   delete: publicProcedure
//     .input(
//       z.object({
//         id: z.number(),
//       }),
//     )
//     .mutation(async ({ ctx, input }) => {
//       const username = ctx.user?.username || 'anonymous'
//       const { id } = input

//       // 获取模板详情，检查是否是创建者
//       const template = await prisma.scriptTemplate.findUnique({
//         where: { id },
//         include: {
//           projects: {
//             select: { createdBy: true },
//           },
//         },
//       })

//       if (!template) {
//         throw new TRPCError({
//           code: 'NOT_FOUND',
//           message: '模板不存在',
//         })
//       }

//       // 只有创建者或项目创建者可以删除模板
//       if (
//         template.createdBy !== username &&
//         template.projects.some(project => project.createdBy !== username)
//       ) {
//         throw new TRPCError({
//           code: 'FORBIDDEN',
//           message: '您没有权限删除此模板',
//         })
//       }

//       // 删除模板
//       await prisma.scriptTemplate.delete({
//         where: { id },
//       })

//       return { success: true }
//     }),

//   // 执行模板
//   execute: publicProcedure
//     .input(
//       z.object({
//         id: z.number(),
//         apiData: z.any(), // API 数据，用于模板执行
//       }),
//     )
//     .mutation(async ({ ctx, input }) => {
//       const username = ctx.user?.username || 'anonymous'
//       const { id, apiData } = input

//       // 检查用户是否有权限访问此模板
//       const hasAccess = await hasTemplateAccess(id, username)
//       if (!hasAccess) {
//         throw new TRPCError({
//           code: 'FORBIDDEN',
//           message: '您没有权限访问此模板',
//         })
//       }

//       // 获取模板
//       const template = await prisma.scriptTemplate.findUnique({
//         where: { id },
//       })

//       if (!template) {
//         throw new TRPCError({
//           code: 'NOT_FOUND',
//           message: '模板不存在',
//         })
//       }

//       try {
//         // 在服务器端执行脚本模板
//         // 注意：这里我们不直接执行脚本，而是返回脚本内容，让前端执行
//         // 这样可以避免服务器端执行不安全的代码
//         return {
//           script: template.script,
//           lang: template.lang,
//         }
//       } catch (error) {
//         console.error('模板执行失败:', error)
//         throw new TRPCError({
//           code: 'INTERNAL_SERVER_ERROR',
//           message: '模板执行失败',
//         })
//       }
//     }),
// })
