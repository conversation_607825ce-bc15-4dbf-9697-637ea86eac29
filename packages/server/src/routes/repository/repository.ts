import { z } from 'zod'
import { publicProcedure, protectedProcedure, router } from '../../trpc'
import { prisma } from '../../prisma'
import { TRPCError } from '@trpc/server'
import { getProjectsInGroup } from '../../services/repository'

export const repositoryRouter = router({
  list: protectedProcedure.query(async () => {
    const repositories = await prisma.repository.findMany({
      include: { projects: true },
    })
    return repositories
  }),

  // 获取项目组详情及其下所有项目
  getAvailableRepositories: protectedProcedure.query(async ({ input }) => {
    return await getProjectsInGroup('rick_proto')
  }),
})
