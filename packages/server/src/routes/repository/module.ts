// import { z } from 'zod'
// import { publicProcedure, protectedProcedure, router } from '../../trpc'
// import { prisma } from '../../prisma'
// import { TRPCError } from '@trpc/server'
// import {
//   getRepositoryFileContent,
//   getRepositoryFileHistory,
//   getRepositoryFileHistoryBatch,
//   getRepositoryModuleFiles,
//   getRepositoryModules,
// } from '../../services/repository'
// import { parseProtoFile } from '../../services/parser'

// export const moduleRouter = router({
//   // 获取仓库模块列表
//   getModules: protectedProcedure
//     .input(z.object({ repository: z.string() }))
//     .query(async ({ input }) => {
//       const { repository } = input
//       return await getRepositoryModules(repository)
//     }),
//   list: protectedProcedure.query(async () => {
//     const repositories = await prisma.repositoryModule.findMany({
//       include: { files: true, repositoryInfo: true },
//     })
//     return repositories
//   }),

//   delete: protectedProcedure
//     .input(z.object({ repositoryFullName: z.string(), module: z.string() }))
//     .mutation(async ({ input }) => {
//       const { repositoryFullName, module } = input
//       await prisma.repositoryModule.delete({
//         where: { repositoryFullName_module: { repositoryFullName, module } },
//       })
//     }),

//   addOrUpdate: protectedProcedure
//     .input(
//       z.object({
//         repository: z.string(),
//         module: z.string(),
//         mode: z.union([z.literal('add'), z.literal('update')]),
//       }),
//     )
//     .mutation(async ({ input }) => {
//       const { repository, module, mode } = input
//       const repositoryFullName = repository.startsWith('rick_proto/')
//         ? repository
//         : `rick_proto/${repository}`

//       const start = Date.now()

//       let moduleInfo: { repositoryFullName: string; module: string; id: number }

//       if (mode === 'add') {
//         const repositoryInfo = await getRepositoryModules(repository)
//         console.log(`getRepositoryModules: ${Date.now() - start}ms`)
//         if (!repositoryInfo.includes(module))
//           throw new TRPCError({ code: 'BAD_REQUEST', message: 'Repository module not found' })
//         const currentModuleInfo = await prisma.repositoryModule.findUnique({
//           where: { repositoryFullName_module: { repositoryFullName, module } },
//         })
//         if (currentModuleInfo) {
//           throw new TRPCError({ code: 'BAD_REQUEST', message: 'Repository module already exists' })
//         } else {
//           console.log('创建 repository + module')
//           moduleInfo = await prisma.repositoryModule.create({
//             data: {
//               module,
//               repositoryInfo: {
//                 connectOrCreate: {
//                   where: { fullName: repositoryFullName },
//                   create: {
//                     fullName: repositoryFullName,
//                     name: repositoryFullName.split('/')[1],
//                     group: repositoryFullName.split('/')[0],
//                   },
//                 },
//               },
//             },
//           })
//         }
//       } else {
//         const currentModuleInfo = await prisma.repositoryModule.findUnique({
//           where: { repositoryFullName_module: { repositoryFullName, module } },
//         })
//         if (!currentModuleInfo) {
//           throw new TRPCError({ code: 'BAD_REQUEST', message: 'Repository module not found' })
//         } else {
//           moduleInfo = currentModuleInfo
//         }
//       }

//       const startGetFiles = Date.now()

//       // 创建所有文件
//       const files = await getRepositoryModuleFiles(repositoryFullName, module)
//       console.log('查询并创建文件', `${Date.now() - startGetFiles}ms`)
//       const savedFiles = await prisma.repositoryFile.findMany({
//         where: { repositoryFullName, moduleId: moduleInfo.id },
//       })
//       const newFiles = files.filter(file => !savedFiles.find(f => f.file === file))
//       if (newFiles.length > 0) {
//         await prisma.repositoryFile.createMany({
//           data: newFiles.map(file => ({
//             file,
//             repositoryFullName,
//             moduleId: moduleInfo.id,
//             refreshAt: new Date(),
//           })),
//         })
//       }

//       const allFiles = await prisma.repositoryFile.findMany({
//         where: { repositoryFullName, moduleId: moduleInfo.id },
//       })

//       // 批量获取所有文件历史
//       if (allFiles.length > 0) {
//         const batchStart = Date.now()

//         // 由于API限制每次最多20个文件，需要分批处理
//         const batchSize = 20
//         const batches = []

//         for (let i = 0; i < allFiles.length; i += batchSize) {
//           batches.push(allFiles.slice(i, i + batchSize))
//         }

//         // 处理每一批文件
//         for (const [batchIndex, batchFilePaths] of batches.entries()) {
//           console.log(`处理第${batchIndex + 1}批文件，共${batchFilePaths.length}个`)

//           const batchHistories = await getRepositoryFileHistoryBatch({
//             repositoryFullName,
//             module,
//             files: allFiles.map(f => f.file),
//           })

//           // 处理每个文件的历史记录
//           for (const fileHistory of batchHistories) {
//             const file = allFiles.find(f => f.file === fileHistory.file)
//             // console.log(`处理文件 ${file} ${JSON.stringify(fileHistory)}`)
//             if (!file) continue

//             // 获取已保存的历史记录
//             const savedHistory = await prisma.repositoryFileHistory.findMany({
//               where: { fileId: file.id },
//               select: { commitId: true },
//             })

//             // 过滤出新的历史记录
//             const newHistory = fileHistory.history.filter(
//               i => !savedHistory.some(sh => sh.commitId === i.commitId),
//             )

//             console.log(
//               `文件 ${file.file} 共${fileHistory.history.length}条历史，新增${newHistory.length}条`,
//             )

//             // 创建新的历史记录
//             if (newHistory.length > 0) {
//               await prisma.repositoryFileHistory.createMany({
//                 data: newHistory.map(h => ({
//                   fileId: file.id,
//                   commitId: h.commitId,
//                   content: '',
//                   parseResult: '',
//                   updatedBy: h.username,
//                   updatedAt: h.updatedAt,
//                 })),
//               })
//             }
//             console.log(`更新文件 ${file.file} 的 refreshAt`)
//             // 更新文件的refreshAt
//             await prisma.repositoryFile.update({
//               where: { id: allFiles.find(f => f.file === fileHistory.file)?.id },
//               data: { refreshAt: new Date() },
//             })
//           }

//           const batchEnd = Date.now()
//           console.log(`批量获取文件历史完成: ${batchEnd - batchStart}ms`)
//         }

//         const end = Date.now()
//         console.log(`创建/更新完成: ${end - start}ms`)

//         return {}
//       }
//     }),

//   info: protectedProcedure.input(z.object({ moduleId: z.number() })).query(async ({ input }) => {
//     const { moduleId } = input

//     const module = await prisma.repositoryModule.findUnique({
//       where: { id: moduleId },
//       include: {
//         repositoryInfo: true,
//         files: {
//           include: {
//             histories: { select: { commitId: true, updatedAt: true, updatedBy: true, id: true } },
//           },
//         },
//       },
//     })

//     return module
//   }),

//   getParsedBatch: protectedProcedure
//     .input(z.object({ historyIds: z.array(z.number()) }))
//     .query(async ({ input }) => {
//       const { historyIds } = input

//       const start = Date.now()

//       const parsedList = await prisma.repositoryFileHistory.findMany({
//         where: { id: { in: historyIds } },
//         include: { fileInfo: { include: { moduleInfo: true } } },
//       })

//       // 如果文件 content 不存在，则获取文件内容
//       for (const h of parsedList) {
//         if (!h.content) {
//           console.log(`获取文件内容: ${h.fileInfo.file} ${h.commitId}`)
//           const content = await getRepositoryFileContent(
//             h.fileInfo.repositoryFullName,
//             h.fileInfo.moduleInfo.module,
//             h.fileInfo.file,
//             h.commitId,
//           )
//           let parseResult = null as any
//           try {
//             parseResult = await parseProtoFile(content)
//           } catch (error) {
//             console.error(`解析错误: ${h.fileInfo.file} ${h.commitId}`, (error as Error)?.message)
//           }
//           await prisma.repositoryFileHistory.update({
//             where: { id: h.id },
//             data: { content, parseResult: parseResult ? JSON.stringify(parseResult) : '' },
//           })
//           h.content = content
//           h.parseResult = parseResult
//         }
//       }

//       const end = Date.now()
//       console.log(`获取文件内容: ${end - start}ms`)

//       return parsedList
//     }),

//   // // 批量获取文件历史
//   // getFileHistoryBatch: protectedProcedure
//   //   .input(
//   //     z.object({
//   //       repositoryFullName: z.string(),
//   //       filePaths: z.array(z.string()),
//   //       ref: z.string().optional(),
//   //       regionMap: z
//   //         .record(
//   //           z.string(),
//   //           z.array(
//   //             z.object({
//   //               start: z.number(),
//   //               length: z.number(),
//   //             }),
//   //           ),
//   //         )
//   //         .optional(),
//   //     }),
//   //   )
//   //   .query(async ({ input }) => {
//   //     const { repositoryFullName, filePaths, ref, regionMap } = input

//   //     const start = Date.now()

//   //     // 处理仓库名称
//   //     const name = repositoryFullName.startsWith('rick_proto/')
//   //       ? repositoryFullName
//   //       : `rick_proto/${repositoryFullName}`

//   //     try {
//   //       const result = await getRepositoryFileHistoryBatch({
//   //         id: name,
//   //         file_path: filePaths,
//   //         ref,
//   //         region_map: regionMap,
//   //       })

//   //       const end = Date.now()
//   //       console.log(`批量获取文件历史完成: ${end - start}ms, 共${filePaths.length}个文件`)

//   //       return result
//   //     } catch (error) {
//   //       console.error('批量获取文件历史失败:', error)
//   //       throw new TRPCError({
//   //         code: 'INTERNAL_SERVER_ERROR',
//   //         message: '批量获取文件历史失败',
//   //       })
//   //     }
//   //   }),
// })
