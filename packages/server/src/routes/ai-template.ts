// import { z } from 'zod'
// import { projectProcedure, protectedProcedure, router } from '../trpc'
// import { prisma } from '../prisma'
// import { TRPCError } from '@trpc/server'
// import { OpenAI } from 'openai'
// import { tracked } from '@trpc/server'

// // 模板可见性枚举
// const TemplateVisibilityEnum = z.enum(['PUBLIC', 'PRIVATE', 'OPEN'])

// // OpenAI 客户端实例
// const openai = new OpenAI({
//   apiKey: process.env.OPENAI_API_KEY || '',
//   baseURL: process.env.OPENAI_API_BASE_URL,
// })

// export const aiTemplateRouter = router({
//   listAll: protectedProcedure.query(async ({ ctx }) => {
//     const username = ctx.user.username
//     return await prisma.aiTemplate.findMany({
//       where: {
//         OR: [
//           { createdBy: username }, // 用户创建的模板
//           { auth: 'PUBLIC' }, // 公共模板
//           { auth: 'OPEN' }, // 开放模板
//         ],
//       },
//     })
//   }),

//   // 列出用户可访问的 AI 模板
//   listByProject: projectProcedure.query(async ({ ctx, input }) => {
//     const projectId = input.projectId
//     return await prisma.aiTemplate.findMany({
//       where: {
//         projects: { some: { id: projectId } },
//       },
//     })
//   }),

//   // 获取模板详情
//   getById: protectedProcedure
//     .input(
//       z.object({
//         id: z.number(),
//       }),
//     )
//     .query(async ({ ctx, input }) => {
//       const data = await prisma.aiTemplate.findFirst({
//         where: {
//           OR: [
//             { createdBy: ctx.user.username }, // 用户创建的模板
//             { auth: 'PUBLIC' }, // 公共模板
//             { auth: 'OPEN' }, // 开放模板
//           ],
//           id: input.id,
//         },
//       })
//       if (!data) {
//         throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在或者您没有权限访问' })
//       }
//       return data
//     }),

//   // 创建 AI 模板
//   create: protectedProcedure
//     .input(
//       z.object({
//         prompt: z.string().min(1),
//         auth: TemplateVisibilityEnum,
//         title: z.string().min(1),
//         desc: z.string().optional(),
//       }),
//     )
//     .mutation(async ({ ctx, input }) => {
//       const username = ctx.user.username
//       const { prompt, auth, title, desc } = input

//       // 创建模板
//       return await prisma.aiTemplate.create({
//         data: { prompt, auth, createdBy: username, title, desc: desc || '' },
//       })
//     }),

//   // 更新 AI 模板
//   update: protectedProcedure
//     .input(
//       z.object({
//         id: z.number(),
//         prompt: z.string().min(1).optional(),
//         auth: TemplateVisibilityEnum.optional(),
//         title: z.string().min(1).optional(),
//         desc: z.string().optional(),
//       }),
//     )
//     .mutation(async ({ ctx, input }) => {
//       const username = ctx.user.username
//       const { id, prompt, auth, title, desc } = input

//       // 获取模板详情，检查是否是创建者
//       const template = await prisma.aiTemplate.findUnique({
//         where: { id },
//       })

//       if (!template) {
//         throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在' })
//       }

//       // 只有创建者或项目创建者可以更新模板
//       if (template.createdBy !== username && template.auth !== 'OPEN') {
//         throw new TRPCError({ code: 'FORBIDDEN', message: '您没有权限更新此模板' })
//       }

//       // 更新模板
//       return await prisma.aiTemplate.update({
//         where: { id },
//         data: {
//           ...(prompt && { prompt }),
//           ...(auth && { auth }),
//           ...(title && { title }),
//           ...(desc !== undefined && { desc }),
//         },
//       })
//     }),

//   bindProject: projectProcedure
//     .input(z.object({ templateIds: z.array(z.number()).min(1) }))
//     .mutation(async ({ ctx, input }) => {
//       const { templateIds } = input
//       const projectId = ctx.project.id
//       const username = ctx.user.username

//       // 检查模板是否存在以及权限
//       const templates = await prisma.aiTemplate.findMany({
//         where: { id: { in: templateIds } },
//       })

//       // 检查是否所有请求的模板都存在
//       if (templates.length !== templateIds.length) {
//         const foundIds = templates.map(t => t.id)
//         const missingIds = templateIds.filter(id => !foundIds.includes(id))
//         throw new TRPCError({
//           code: 'NOT_FOUND',
//           message: `以下模板不存在: ${missingIds.join(', ')}`,
//         })
//       }

//       // 检查权限：PRIVATE 模板只能由创建者绑定
//       const privateTemplates = templates.filter(
//         t => t.auth === 'PRIVATE' && t.createdBy !== username,
//       )
//       if (privateTemplates.length > 0) {
//         throw new TRPCError({
//           code: 'FORBIDDEN',
//           message: `您没有权限绑定以下私有模板: ${privateTemplates.map(t => t.id).join(', ')}`,
//         })
//       }

//       // 过滤出尚未绑定的模板
//       const existingTemplateIds = await prisma.aiTemplate.findMany({
//         where: { projects: { some: { id: projectId } } },
//       })
//       const newTemplateIds = templateIds.filter(id => !existingTemplateIds.some(t => t.id === id))

//       // 执行绑定操作
//       await prisma.project.update({
//         where: { id: projectId },
//         data: {
//           aiTemplates: { connect: newTemplateIds.map(id => ({ id })) },
//         },
//       })

//       return { success: true }
//     }),

//   unbindProject: projectProcedure
//     .input(z.object({ templateIds: z.array(z.number()).min(1) }))
//     .mutation(async ({ ctx, input }) => {
//       const { templateIds } = input
//       const projectId = ctx.project.id

//       // 检查要解绑的模板是否存在于当前项目中
//       const existingTemplates = await prisma.aiTemplate.findMany({
//         where: { id: { in: templateIds }, projects: { some: { id: projectId } } },
//       })

//       // 执行解绑操作
//       await prisma.project.update({
//         where: { id: projectId },
//         data: {
//           aiTemplates: {
//             disconnect: existingTemplates.map(template => ({ id: template.id })),
//           },
//         },
//       })

//       return { success: true }
//     }),

//   // 删除 AI 模板
//   delete: protectedProcedure
//     .input(
//       z.object({
//         id: z.number(),
//       }),
//     )
//     .mutation(async ({ ctx, input }) => {
//       const username = ctx.user.username

//       // 获取模板详情，检查是否是创建者
//       const template = await prisma.aiTemplate.findUnique({
//         where: { id: input.id },
//       })

//       if (!template) {
//         throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在' })
//       }

//       // 只有创建者或项目创建者可以更新模板
//       if (template.createdBy !== username && template.auth !== 'OPEN') {
//         throw new TRPCError({ code: 'FORBIDDEN', message: '您没有权限删除此模板' })
//       }

//       // 删除模板
//       return await prisma.aiTemplate.delete({
//         where: { id: input.id },
//       })
//     }),

//   // 执行 AI 模板（流式响应）
//   execute: protectedProcedure
//     .input(
//       z.object({
//         id: z.number(),
//         apiData: z.any(), // API 数据，用于模板执行
//         lastEventId: z.string().optional(), // 添加lastEventId支持重新连接
//       }),
//     )
//     .subscription(async function* ({ ctx, input }) {
//       const { apiData, lastEventId, id: templateId } = input
//       const username = ctx.user.username

//       // 获取模板详情，检查是否是创建者
//       const template = await prisma.aiTemplate.findUnique({
//         where: { id: templateId },
//       })

//       if (!template) {
//         throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在' })
//       }

//       // 只有创建者或项目创建者可以更新模板
//       if (template.createdBy !== username && template.auth === 'PRIVATE') {
//         throw new TRPCError({ code: 'FORBIDDEN', message: '您没有权限访问此模板' })
//       }

//       try {
//         // 准备发送给 OpenAI 的提示
//         const prompt = `${template.prompt}\n\n以下是API数据:\n${JSON.stringify(apiData, null, 2)}`

//         // 调用 OpenAI API 进行流式响应
//         const stream = await openai.chat.completions.create({
//           model: process.env.OPENAI_API_MODEL || 'gpt-4-turbo',
//           messages: [{ role: 'user', content: prompt }],
//           stream: true,
//         })

//         // 用于跟踪内容和分配ID
//         let contentIndex = parseInt(lastEventId || '0', 10) || 0
//         let fullContent = ''

//         try {
//           // 处理流式响应
//           for await (const chunk of stream) {
//             const content = chunk.choices[0]?.delta?.content || ''
//             if (content) {
//               contentIndex++
//               fullContent += content
//               yield tracked(contentIndex.toString(), content)
//             }
//           }
//         } catch (error) {
//           console.error('AI 模板执行失败:')
//           yield tracked(
//             'error',
//             (((error as any)?.error?.message ?? (error as any)?.message) as string) ??
//               'AI 模板执行失败',
//           )
//         } finally {
//           // 确保流被正确关闭
//           console.log(`AI模板(ID: ${templateId})执行完成，共生成${contentIndex}个内容片段`)
//           return
//         }
//       } catch (error) {
//         yield tracked(
//           'error',
//           (((error as any)?.error?.message ?? (error as any)?.message) as string) ??
//             'AI 模板执行失败',
//         )
//         console.error('AI 模板执行失败')
//         return
//       }
//     }),
// })
