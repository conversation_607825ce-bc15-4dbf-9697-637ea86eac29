import { z } from 'zod'
import {
  protectedProcedure,
  router,
  projectProcedure,
  projectWriteProcedure,
  projectOwnerProcedure,
} from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'

// 定义路径规则输入验证
const pathRuleSchema = z.object({
  name: z.string().min(1, '规则名称不能为空'),
  pattern: z.string().min(1, '路径模板不能为空'),
  description: z.string().optional(),
})

export const pathRuleRouter = router({
  // 获取项目下所有路径规则
  getPathRules: projectProcedure.query(async ({ ctx: { project } }) => {
    return await prisma.pathRule.findMany({
      where: { projectId: project.id },
      orderBy: { name: 'asc' },
    })
  }),

  // 创建路径规则
  createPathRule: projectWriteProcedure
    .input(pathRuleSchema)
    .mutation(async ({ ctx: { project }, input }) => {
      const { name, pattern, description } = input

      // 检查同名规则是否已存在
      const existingRule = await prisma.pathRule.findUnique({
        where: {
          projectId_name: {
            projectId: project.id,
            name,
          },
        },
      })

      if (existingRule) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: `规则 "${name}" 已存在`,
        })
      }

      // 创建新规则
      return await prisma.pathRule.create({
        data: {
          name,
          pattern,
          description,
          projectId: project.id,
        },
      })
    }),

  // 更新路径规则
  updatePathRule: projectWriteProcedure
    .input(
      z.object({
        id: z.number(),
        ...pathRuleSchema.shape,
      }),
    )
    .mutation(async ({ ctx: { project }, input }) => {
      const { id, name, pattern, description } = input

      // 检查规则是否存在
      const existingRule = await prisma.pathRule.findUnique({
        where: { id },
      })

      if (!existingRule || existingRule.projectId !== project.id) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '规则不存在或不属于当前项目',
        })
      }

      // 如果更改了名称，检查新名称是否已存在
      if (name !== existingRule.name) {
        const duplicateRule = await prisma.pathRule.findUnique({
          where: {
            projectId_name: {
              projectId: project.id,
              name,
            },
          },
        })

        if (duplicateRule) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: `规则 "${name}" 已存在`,
          })
        }
      }

      // 更新规则
      return await prisma.pathRule.update({
        where: { id },
        data: {
          name,
          pattern,
          description,
        },
      })
    }),

  // 删除路径规则
  deletePathRule: projectWriteProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx: { project }, input }) => {
      const { id } = input

      // 检查规则是否存在
      const existingRule = await prisma.pathRule.findUnique({
        where: { id },
      })

      if (!existingRule || existingRule.projectId !== project.id) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '规则不存在或不属于当前项目',
        })
      }

      // 删除规则 (关联的 InterfaceMetadata 会自动设为 null)
      await prisma.pathRule.delete({
        where: { id },
      })

      return { success: true }
    }),

  // 设置项目默认路径规则
  setDefaultPathRule: projectWriteProcedure
    .input(
      z.object({
        pathRuleId: z.number().nullable(),
      }),
    )
    .mutation(async ({ ctx: { project }, input }) => {
      const { pathRuleId } = input

      // 如果设置为 null，直接清除默认规则
      if (pathRuleId === null) {
        await prisma.pathRule.updateMany({
          where: { projectId: project.id },
          data: { default: false },
        })
        return { success: true }
      }

      // 检查规则是否存在
      const existingRule = await prisma.pathRule.findUnique({
        where: { id: pathRuleId },
      })

      if (!existingRule || existingRule.projectId !== project.id) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '规则不存在或不属于当前项目' })
      }

      // 设置为默认规则
      await prisma.pathRule.updateMany({
        where: { projectId: project.id },
        data: { default: false },
      })
      await prisma.pathRule.update({
        where: { id: pathRuleId },
        data: { default: true },
      })

      return { success: true }
    }),
})
