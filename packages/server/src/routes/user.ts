import { z } from 'zod'
import { publicProcedure, protectedProcedure, router } from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'
import axios from 'axios'
import { getGitUserAccessToken } from '../services/repository'
import { omit } from 'lodash-es'
import crypto from 'crypto'

export const userRouter = router({
  hello: publicProcedure.query(async () => {
    return 'hello'
  }),

  // 获取当前用户信息
  getCurrentUser: publicProcedure.query(async ({ ctx }) => {
    const username = ctx.taiIdentity?.LoginName

    if (!username) {
      return null
    }

    // 查找用户，如果不存在则创建
    const user = await prisma.user.findUnique({
      where: { username },
      select: {
        username: true,
        chineseName: true,
        is_admin: true,
        gitAccessToken: true,
        deptId: true,
        deptName: true,
        staffId: true,
      },
    })

    return user ? { ...omit(user, ['gitAccessToken']), isBindGit: !!user.gitAccessToken } : null
  }),

  // 获取用户列表
  list: protectedProcedure.query(async () => {
    return await prisma.user.findMany({
      select: {
        username: true,
        chineseName: true,
        deptId: true,
        deptName: true,
        staffId: true,
      },
    })
  }),

  // 根据用户名获取用户
  getByUsername: publicProcedure
    .input(z.object({ username: z.string() }))
    .query(async ({ input }) => {
      const user = await prisma.user.findUnique({
        where: { username: input.username },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '用户不存在',
        })
      }

      return user
    }),

  // 删除用户
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 只有管理员或用户本人可以删除用户
      if (!ctx.user.is_admin && ctx.user.username !== input.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '您没有权限删除此用户',
        })
      }

      return await prisma.user.delete({ where: { username: input.id } })
    }),

  setUserGitAccessToken: protectedProcedure
    .input(z.object({ code: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const data = await getGitUserAccessToken(input.code)
      await prisma.user.update({
        where: { username: ctx.user.username },
        data: {
          gitAccessToken: data.access_token,
          gitRefreshToken: data.refresh_token,
          gitAccessTokenExpiresAt: new Date(Date.now() + data.expires_in * 1000),
        },
      })
      return {}
    }),

  // 解绑工蜂 Git 账号
  unbindGitAccount: protectedProcedure.mutation(async ({ ctx }) => {
    // 检查用户是否已绑定工蜂 Git 账号
    const user = await prisma.user.findUnique({
      where: { username: ctx.user.username },
      select: { gitAccessToken: true },
    })

    if (!user?.gitAccessToken) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '您还未绑定工蜂 Git 账号',
      })
    }

    // 清除所有Git相关数据
    await prisma.user.update({
      where: { username: ctx.user.username },
      data: {
        gitAccessToken: null,
        gitRefreshToken: null,
        gitAccessTokenExpiresAt: null,
      },
    })

    return { success: true }
  }),

  // 生成API Token
  generateApiToken: protectedProcedure.mutation(async ({ ctx }) => {
    // 生成随机token
    const token = crypto.randomBytes(32).toString('hex')

    // 更新用户的API token
    await prisma.user.update({
      where: { username: ctx.user.username },
      data: {
        apiToken: token,
        apiTokenCreatedAt: new Date(),
      },
    })

    return { token }
  }),

  // 获取当前用户的API Token信息
  getApiToken: protectedProcedure.query(async ({ ctx }) => {
    const user = await prisma.user.findUnique({
      where: { username: ctx.user.username },
      select: {
        apiToken: true,
        apiTokenCreatedAt: true,
      },
    })

    return {
      hasToken: !!user?.apiToken,
      token: user?.apiToken || null,
      createdAt: user?.apiTokenCreatedAt,
    }
  }),

  // 删除API Token
  deleteApiToken: protectedProcedure.mutation(async ({ ctx }) => {
    await prisma.user.update({
      where: { username: ctx.user.username },
      data: {
        apiToken: null,
        apiTokenCreatedAt: null,
      },
    })

    return { success: true }
  }),
})
