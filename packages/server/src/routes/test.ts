import { z } from 'zod'
import { publicProcedure, router } from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'
import { testProto } from '../services/test.proto'
import { parseProtoFile } from '../services/parser'
// import { getProto } from '../services/getProto'

export const testRouter = router({
  // 获取当前用户信息
  parse: publicProcedure.query(async ({ ctx }) => {
    return parseProtoFile(testProto)
  }),
  // getProto: publicProcedure.query(async ({ ctx }) => {
  //   return getProto(69062, 'dev_user')
  // }),
})
