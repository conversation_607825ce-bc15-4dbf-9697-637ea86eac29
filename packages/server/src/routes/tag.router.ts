import { z } from 'zod'
import { protectedProcedure, router, projectProcedure, projectWriteProcedure } from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'

// 定义标签输入验证
const tagSchema = z.object({
  name: z.string().min(1, '标签名称不能为空'),
  color: z.string().optional(),
  icon: z.string().optional(),
})

export const tagRouter = router({
  // 获取项目下所有标签
  getTags: projectProcedure.query(async ({ ctx: { project } }) => {
    return await prisma.tag.findMany({
      where: { projectId: project.id },
      orderBy: { name: 'asc' },
    })
  }),

  // 创建标签
  createTag: projectWriteProcedure
    .input(tagSchema)
    .mutation(async ({ ctx: { project }, input }) => {
      const { name, color, icon } = input

      // 检查同名标签是否已存在
      const existingTag = await prisma.tag.findUnique({
        where: {
          projectId_name: {
            projectId: project.id,
            name,
          },
        },
      })

      if (existingTag) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: `标签 "${name}" 已存在`,
        })
      }

      // 创建新标签
      return await prisma.tag.create({
        data: {
          name,
          color,
          icon,
          projectId: project.id,
        },
      })
    }),

  // 更新标签
  updateTag: projectWriteProcedure
    .input(
      z.object({
        id: z.number(),
        ...tagSchema.shape,
      }),
    )
    .mutation(async ({ ctx: { project }, input }) => {
      const { id, name, color, icon } = input

      // 检查标签是否存在
      const existingTag = await prisma.tag.findUnique({
        where: { id },
      })

      if (!existingTag || existingTag.projectId !== project.id) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '标签不存在或不属于当前项目',
        })
      }

      // 如果更改了名称，检查新名称是否已存在
      if (name !== existingTag.name) {
        const duplicateTag = await prisma.tag.findUnique({
          where: {
            projectId_name: {
              projectId: project.id,
              name,
            },
          },
        })

        if (duplicateTag) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: `标签 "${name}" 已存在`,
          })
        }
      }

      // 更新标签
      return await prisma.tag.update({
        where: { id },
        data: {
          name,
          color,
          icon,
        },
      })
    }),

  // 删除标签
  deleteTag: projectWriteProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx: { project }, input }) => {
      const { id } = input

      // 检查标签是否存在
      const existingTag = await prisma.tag.findUnique({
        where: { id },
      })

      if (!existingTag || existingTag.projectId !== project.id) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '标签不存在或不属于当前项目',
        })
      }

      // 删除标签 (关联的 InterfaceTagAssignment 会自动删除)
      await prisma.tag.delete({
        where: { id },
      })

      return { success: true }
    }),
})
