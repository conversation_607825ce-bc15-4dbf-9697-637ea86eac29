import { z } from 'zod'
import { projectProcedure, protectedProcedure, router, t } from '../../trpc'
import { prisma } from '../../prisma'
import { TRPCError } from '@trpc/server'
import { tracked } from '@trpc/server'
import { ScriptLang, Prisma } from '../../../prisma/prisma'
import { covertScriptToEs5 } from '../../services/script'
import { Interpreter } from 'eval5'
import { protoRouter } from '../proto'
import {
  getServiceMethodContent,
  mergeParsedProtoJSON,
  ParsedProtoJSON,
} from '../../services/parser'
import { safeJsonParse } from '../../utils/common'
import { modelChatCompletions } from '../../services/chat'

// 模板可见性枚举
const TemplateVisibilityEnum = z.enum(['PUBLIC', 'PRIVATE', 'OPEN'])
// 脚本语言枚举
const ScriptLangEnum = z.enum(['JS', 'TS'])

/**
 * 使用 eval5 执行编译后的脚本
 * @param code 编译后的 ES5 代码
 * @param apiData API 数据
 * @returns 生成的代码
 */
async function executeScript(code: string, apiData: any): Promise<string> {
  try {
    // 创建 eval5 解释器
    const interpreter = new Interpreter(undefined, {
      timeout: 10000, // 10秒超时
      rootContext: {
        console: console, // 允许使用控制台
        Promise: Promise, // 允许使用 Promise
      },
      globalContextInFunction: {}, // 禁止访问全局上下文
    })

    // 执行脚本代码
    interpreter.evaluate(code)

    // 获取执行函数
    const executeScriptFn = (apiData: any) => {
      const code = `executeScript(${JSON.stringify(apiData)})`
      return interpreter.evaluate(code)
    }

    // 调用执行函数
    const result = executeScriptFn(apiData)

    // 处理可能的 Promise 结果
    if (result && typeof result.then === 'function') {
      return await result
    }

    return result
  } catch (error) {
    console.error('脚本执行错误:', error)
    throw error
  }
}

export const scriptTemplateRouter = router({
  listAll: protectedProcedure.query(async ({ ctx }) => {
    const username = ctx.user.username
    return await prisma.scriptTemplate.findMany({
      where: {
        OR: [
          { createdBy: username }, // 用户创建的模板
          { auth: 'PUBLIC' }, // 公共模板
          { auth: 'OPEN' }, // 开放模板
        ],
      },
    })
  }),

  // 列出用户可访问的脚本模板
  listByProject: projectProcedure.query(async ({ ctx, input }) => {
    const projectId = input.projectId
    return await prisma.scriptTemplate.findMany({
      where: {
        projects: { some: { id: projectId } },
        OR: [
          { createdBy: ctx.user.username }, // 用户创建的模板
          { auth: 'PUBLIC' }, // 公共模板
          { auth: 'OPEN' }, // 开放模板
        ],
      },
    })
  }),

  // 获取模板详情
  getById: protectedProcedure
    .input(
      z.object({
        id: z.number(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await prisma.scriptTemplate.findFirst({
        where: {
          OR: [
            { createdBy: ctx.user.username }, // 用户创建的模板
            { auth: 'PUBLIC' }, // 公共模板
            { auth: 'OPEN' }, // 开放模板
          ],
          id: input.id,
        },
      })
      if (!data) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在或者您没有权限访问' })
      }
      return data
    }),

  // 创建脚本模板
  create: protectedProcedure
    .input(
      z.object({
        script: z.string().min(1),
        lang: ScriptLangEnum,
        auth: TemplateVisibilityEnum,
        title: z.string().min(1),
        desc: z.string().optional(),
        optimizePrompt: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const username = ctx.user.username
      const { script, lang, auth, title, desc, optimizePrompt } = input

      // 将脚本转换为 ES5
      let scriptEs5 = null
      try {
        scriptEs5 = await covertScriptToEs5(script, lang as ScriptLang)
      } catch (error) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `脚本编译失败: ${(error as Error).message}`,
        })
      }

      // 创建模板数据对象
      const templateData: Prisma.ScriptTemplateCreateInput = {
        script,
        scriptEs5,
        lang: lang as ScriptLang,
        auth,
        title,
        desc: desc || '',
        createdByUser: {
          connect: { username },
        },
      }

      // 如果有优化提示词，添加到数据中
      if (optimizePrompt) {
        templateData.optimizePrompt = optimizePrompt
      }

      // 创建模板
      return await prisma.scriptTemplate.create({
        data: templateData,
      })
    }),

  // 更新脚本模板
  update: protectedProcedure
    .input(
      z.object({
        id: z.number(),
        script: z.string().min(1).optional(),
        lang: ScriptLangEnum.optional(),
        auth: TemplateVisibilityEnum.optional(),
        title: z.string().min(1).optional(),
        desc: z.string().optional(),
        optimizePrompt: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const username = ctx.user.username
      const { id, script, lang, auth, title, desc, optimizePrompt } = input

      // 获取模板详情，检查是否是创建者
      const template = await prisma.scriptTemplate.findUnique({
        where: { id },
      })

      if (!template) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在' })
      }

      // 只有创建者或项目创建者可以更新模板
      if (template.createdBy !== username && template.auth !== 'OPEN') {
        throw new TRPCError({ code: 'FORBIDDEN', message: '您没有权限更新此模板' })
      }

      // 如果更新脚本内容或语言，则需要重新编译
      let scriptEs5 = template.scriptEs5
      if (script || lang) {
        try {
          scriptEs5 = await covertScriptToEs5(
            script || template.script,
            (lang as ScriptLang) || template.lang,
          )
        } catch (error) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: `脚本编译失败: ${(error as Error).message}`,
          })
        }
      }

      // 创建更新数据对象
      const updateData: Prisma.ScriptTemplateUpdateInput = {}

      if (script) updateData.script = script
      if (lang) updateData.lang = lang as ScriptLang
      if (scriptEs5) updateData.scriptEs5 = scriptEs5
      if (auth) updateData.auth = auth
      if (title) updateData.title = title
      if (desc !== undefined) updateData.desc = desc
      if (optimizePrompt !== undefined) updateData.optimizePrompt = optimizePrompt

      // 更新模板
      return await prisma.scriptTemplate.update({
        where: { id },
        data: updateData,
      })
    }),

  getBoundProjects: protectedProcedure
    .input(z.object({ templateId: z.number() }))
    .query(async ({ ctx, input }) => {
      const { templateId } = input
      const info = await prisma.scriptTemplate.findUnique({
        where: {
          id: templateId,
          OR: [
            { createdBy: ctx.user.username }, // 用户创建的模板
            { auth: 'PUBLIC' }, // 公共模板
            { auth: 'OPEN' }, // 开放模板
          ],
        },
        include: {
          projects: {
            where: { members: { some: { username: ctx.user.username } } },
          },
        },
      })
      return info?.projects || []
    }),

  bindProject: projectProcedure
    .input(z.object({ templateIds: z.array(z.number()).min(1) }))
    .mutation(async ({ ctx, input }) => {
      const { templateIds } = input
      const projectId = ctx.project.id
      const username = ctx.user.username

      // 检查模板是否存在以及权限
      const templates = await prisma.scriptTemplate.findMany({
        where: { id: { in: templateIds } },
      })

      // 检查是否所有请求的模板都存在
      if (templates.length !== templateIds.length) {
        const foundIds = templates.map(t => t.id)
        const missingIds = templateIds.filter(id => !foundIds.includes(id))
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `以下模板不存在: ${missingIds.join(', ')}`,
        })
      }

      // 检查权限：PRIVATE 模板只能由创建者绑定
      const privateTemplates = templates.filter(
        t => t.auth === 'PRIVATE' && t.createdBy !== username,
      )
      if (privateTemplates.length > 0) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: `您没有权限绑定以下私有模板: ${privateTemplates.map(t => t.id).join(', ')}`,
        })
      }

      // 过滤出尚未绑定的模板
      const existingTemplateIds = await prisma.scriptTemplate.findMany({
        where: { projects: { some: { id: projectId } } },
      })
      const newTemplateIds = templateIds.filter(id => !existingTemplateIds.some(t => t.id === id))

      // 执行绑定操作
      await prisma.project.update({
        where: { id: projectId },
        data: {
          scriptTemplates: { connect: newTemplateIds.map(id => ({ id })) },
        },
      })

      return { success: true }
    }),

  unbindProject: projectProcedure
    .input(z.object({ templateIds: z.array(z.number()).min(1) }))
    .mutation(async ({ ctx, input }) => {
      const { templateIds } = input
      const projectId = ctx.project.id

      // 检查要解绑的模板是否存在于当前项目中
      const existingTemplates = await prisma.scriptTemplate.findMany({
        where: { id: { in: templateIds }, projects: { some: { id: projectId } } },
      })

      // 执行解绑操作
      await prisma.project.update({
        where: { id: projectId },
        data: {
          scriptTemplates: {
            disconnect: existingTemplates.map(template => ({ id: template.id })),
          },
        },
      })

      return { success: true }
    }),

  // 删除脚本模板
  delete: protectedProcedure
    .input(
      z.object({
        id: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const username = ctx.user.username

      // 获取模板详情，检查是否是创建者
      const template = await prisma.scriptTemplate.findUnique({
        where: { id: input.id },
      })

      if (!template) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在' })
      }

      // 只有创建者或项目创建者可以更新模板
      if (template.createdBy !== username && template.auth !== 'OPEN') {
        throw new TRPCError({ code: 'FORBIDDEN', message: '您没有权限删除此模板' })
      }

      // 删除模板
      return await prisma.scriptTemplate.delete({
        where: { id: input.id },
      })
    }),

  // 执行脚本模板（流式响应）
  execute: projectProcedure
    .input(
      z.object({
        id: z.number(),
        serviceName: z.string(),
        methodName: z.string(),
      }),
    )
    .subscription(async function* ({ ctx, input }) {
      const { methodName, serviceName, projectId, id: templateId } = input
      const username = ctx.user.username

      // 获取模板详情，检查是否是创建者
      const template = await prisma.scriptTemplate.findUnique({
        where: { id: templateId },
      })

      if (!template) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在' })
      }

      // 只有创建者或项目创建者可以更新模板
      if (template.createdBy !== username && template.auth === 'PRIVATE') {
        throw new TRPCError({ code: 'FORBIDDEN', message: '您没有权限访问此模板' })
      }

      // 验证模板有编译后的ES5代码
      if (!template.scriptEs5) {
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: '模板脚本未编译' })
      }

      const start = Date.now()

      // 直接调用trpc 的方法
      const caller = t.createCallerFactory(protoRouter)(ctx)
      const protoData = await caller.getParsedContentByProject({ projectId })
      console.log(`获取proto数据耗时: ${Date.now() - start}ms`)
      const protoInfo = mergeParsedProtoJSON(
        [
          ...protoData.list.map(i => safeJsonParse<ParsedProtoJSON>(i.json)),
          ...protoData.extra.map(i => safeJsonParse<ParsedProtoJSON>(i.json)),
        ].filter(i => i !== null),
      )

      const apiInfo = getServiceMethodContent(
        protoInfo,
        `trpc.${ctx.project.repository}.${ctx.project.name}`,
        serviceName,
        methodName,
      )

      const interfaceMetaData = await prisma.interfaceMetadata.findFirst({
        where: { projectId, serviceName, methodName },
        include: { tags: true, pathRule: true },
      })

      const data = { ...apiInfo, interfaceMetaData }

      console.log(`解析proto数据耗时: ${Date.now() - start}ms`)

      try {
        // 执行脚本生成内容
        const scriptResult = await executeScript(template.scriptEs5, data)

        //         const prompt = `以下是通过用户脚本生成的接口内容:\n${scriptResult}\n\n现在用户需要你基于这些接口内容，做一些微调，微调建议：\n${template.optimizePrompt}\n\n
        // 为了你更好地理解上下文，我将原始的接口信息也告诉你，仅供参考。接口原信息：\n${JSON.stringify(apiInfo)}
        // 请直接输入最终的内容，并使用 typescript 代码块包裹，你只需要输出代码，不要输出任何解释，并确保只输入一个代码块`

        const prompt = `You are tasked with optimizing API interface content.

Input Script Result:
${scriptResult}

Optimization Requirements:
${template.optimizePrompt}

Reference (Original API Info):
${JSON.stringify(data)}

Instructions:
1. Review and optimize the input content based on the requirements
2. Output ONLY the final TypeScript code
3. Wrap the code in a TypeScript code block
4. Do not include any explanations or comments
5. Ensure to output exactly one code block

Response Format:
\`\`\`typescript
// Your optimized code here
\`\`\``

        let contentIndex = 0

        const messages = [
          { role: 'system' as const, content: '你是一个 API 文档助手' },
          { role: 'user' as const, content: prompt },
        ]

        for await (const content of modelChatCompletions({ messages })) {
          contentIndex++
          yield tracked(contentIndex.toString(), content)
        }

        console.log(`AI模板(ID: ${templateId})执行完成，共生成${contentIndex}个内容片段`)
      } catch (error) {
        const err = error as any
        const message: string = err?.error?.message ?? err?.message ?? 'AI 模板执行失败'
        yield tracked('error', message)
        console.error('AI 模板执行失败:', error)
      }
    }),
})
