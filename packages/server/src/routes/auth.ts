import { z } from 'zod'
import { publicProcedure, router } from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'
import { omit } from 'lodash-es'

export const authRouter = router({
  // 获取当前用户信息
  getCurrentUser: publicProcedure.query(async ({ ctx }) => {
    const username = ctx.taiIdentity?.LoginName

    if (!username) {
      return null
    }

    // 查找用户，如果不存在则创建
    const user = await prisma.user.findUnique({
      where: { username },
      select: {
        username: true,
        chineseName: true,
        is_admin: true,
        gitAccessToken: true,
        deptId: true,
        deptName: true,
        staffId: true,
      },
    })

    return user ? { ...omit(user, ['gitAccessToken']), isBindGit: !!user.gitAccessToken } : null
  }),
})
