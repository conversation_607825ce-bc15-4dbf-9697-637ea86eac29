import { router } from '../trpc'
import { userRouter } from './user'
import { authRouter } from './auth'
import { protoRouter } from './proto'
import { projectRouter } from './project'
import { templateRouter } from './template/'
import { testRouter } from './test'
import { repositoryRouter } from './repository'
import { interfaceMetadataRouter } from './interface-metadata'
import { tagRouter } from './tag.router'
import { pathRuleRouter } from './path-rule.router'

export const appRouter = router({
  user: userRouter,
  auth: authRouter,
  project: projectRouter,
  template: templateRouter,
  test: testRouter,
  repository: repositoryRouter,
  proto: protoRouter,
  interfaceMetadata: interfaceMetadataRouter,
  tag: tagRouter,
  pathRule: pathRuleRouter,
})

export type AppRouter = typeof appRouter
