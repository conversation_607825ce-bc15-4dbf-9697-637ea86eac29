// import { z } from 'zod'
// import { protectedProcedure, publicProcedure } from '../../trpc'
// import { getProto, type GitUploadCfgJSON, type ProtoHistoryRaw } from '../../services/getProto'
// import { prisma } from '../../prisma'
// import { parseProtoFile } from '../../services/parser'
// import { safeJsonParse } from '../../utils/common'

// /**
//  * 添加或更新proto文件(包括依赖的proto文件)
//  */
// export const addOrUpdate = protectedProcedure
//   .input(
//     z.object({
//       pbInfoId: z.number(),
//       projectId: z.number().optional(),
//     }),
//   )
//   .mutation(async ({ ctx, input }) => {
//     const username = ctx.user.username
//     const { proto, relatedProtos } = await getProto(input.pbInfoId, username)
//     await addOrUpdateProto(proto, username)
//     for (const relatedProto of relatedProtos) {
//       await addOrUpdateProto(relatedProto, username)
//     }
//     return { success: true }
//   })

// async function addOrUpdateProto(
//   proto: {
//     pbInfoId: number
//     histories: ProtoHistoryRaw[]
//   },
//   username: string,
// ) {
//   await prisma.protoFile.upsert({
//     where: { pbInfoId: proto.pbInfoId },
//     update: {
//       updatedBy: username,
//     },
//     create: {
//       pbInfoId: proto.pbInfoId,
//       createdBy: username,
//       updatedBy: username,
//     },
//   })
//   for (const [index, history] of proto.histories.entries()) {
//     try {
//       const match = await prisma.protoFileHistory.findFirst({
//         where: { pbInfoId: proto.pbInfoId, version: history.version },
//       })
//       if (match) continue
//       const parseResult = await parseProtoFile(history.detail)
//       const gitUploadCfg = safeJsonParse<GitUploadCfgJSON>(history.gitUploadCfg)
//       await prisma.protoFileHistory.create({
//         data: {
//           ...history,
//           parseResult: JSON.stringify(parseResult),
//           projectFullPath: gitUploadCfg?.projectFullPath ?? '',
//           pbName: gitUploadCfg?.tagName.split('/').at(-2) ?? '',
//           pbFullPath: gitUploadCfg?.tagName.split('/').slice(0, -1).join('/') ?? '',
//           package: parseResult.package,
//           pbInfoId: undefined,
//           protoFile: {
//             connect: {
//               pbInfoId: proto.pbInfoId,
//             },
//           },
//         },
//       })
//     } catch (error) {
//       if (index === 0) {
//         throw error
//       }
//       console.error(error)
//       continue
//     }
//   }
// }
