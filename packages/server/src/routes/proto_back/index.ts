// import { z } from 'zod'
// import { publicProcedure, protectedProcedure, router } from '../../trpc'
// import { prisma } from '../../prisma'
// import { TRPCError } from '@trpc/server'
// import { addOrUpdate } from './add'
// import { compareProtoVersions } from '../../utils/protoCompare'
// import { safeJsonParse } from '../../utils/common'

// export const protoFileRouter = router({
//   // 创建 Proto 文件
//   addOrUpdate: addOrUpdate,

//   // 获取 Proto 文件元数据（不包含内容和解析结果）
//   getById: publicProcedure
//     .input(
//       z.object({
//         pbInfoId: z.number(),
//       }),
//     )
//     .query(async ({ input }) => {
//       const { pbInfoId } = input

//       // 获取 Proto 文件元数据
//       const protoFile = await prisma.protoFile.findUnique({
//         where: { pbInfoId },
//       })

//       const firstHistory = await prisma.protoFileHistory.findFirst({
//         where: { pbInfoId },
//         orderBy: { version: 'desc' },
//         select: {
//           createRtx: true,
//           id: true,
//           modifyTime: true,
//           pbInfoId: true,
//           versionStr: true,
//           pbFullPath: true,
//           projectFullPath: true,
//           pbName: true,
//         },
//         take: 1,
//       })

//       if (!protoFile) {
//         throw new TRPCError({ code: 'NOT_FOUND', message: 'Proto 文件不存在' })
//       }
//       if (!firstHistory) {
//         throw new TRPCError({ code: 'NOT_FOUND', message: 'Proto 文件历史不存在' })
//       }

//       return {
//         ...protoFile,
//         detail: firstHistory,
//       }
//     }),

//   // 获取 Proto 文件的解析结果
//   getParsedData: publicProcedure
//     .input(
//       z.object({
//         pbInfoId: z.number(),
//       }),
//     )
//     .query(async ({ input }) => {
//       const { pbInfoId } = input

//       // 获取 Proto 文件解析结果
//       const firstHistory = await prisma.protoFileHistory.findFirst({
//         where: { pbInfoId },
//         orderBy: { version: 'desc' },
//         select: {
//           parseResult: true,
//         },
//         take: 1,
//       })

//       if (!firstHistory) {
//         throw new TRPCError({
//           code: 'NOT_FOUND',
//           message: 'Proto 文件历史不存在',
//         })
//       }

//       // 返回解析后的 JSON 对象
//       try {
//         return JSON.parse(firstHistory.parseResult)
//       } catch (error) {
//         throw new TRPCError({
//           code: 'INTERNAL_SERVER_ERROR',
//           message: '解析结果格式无效',
//         })
//       }
//     }),

//   getHistoryParsedDataBatch: protectedProcedure
//     .input(
//       z.object({
//         historyIds: z.array(z.number()),
//       }),
//     )
//     .query(async ({ input }) => {
//       const { historyIds } = input

//       const start = performance.now()

//       const histories = await prisma.protoFileHistory.findMany({
//         where: { id: { in: historyIds } },
//         select: {
//           parseResult: true,
//         },
//       })

//       const end = performance.now()
//       console.log(`getHistoryParsedDataBatch 耗时: ${end - start} 毫秒`)

//       return histories
//     }),

//   // 获取 Proto 文件内容
//   getContent: publicProcedure
//     .input(
//       z.object({
//         pbInfoId: z.number(),
//       }),
//     )
//     .query(async ({ input }) => {
//       const { pbInfoId } = input

//       // 获取 Proto 文件内容
//       const firstHistory = await prisma.protoFileHistory.findFirst({
//         where: { pbInfoId },
//         orderBy: { version: 'desc' },
//         select: {
//           detail: true,
//         },
//         take: 1,
//       })

//       if (!firstHistory) {
//         throw new TRPCError({
//           code: 'NOT_FOUND',
//           message: 'Proto 文件历史不存在',
//         })
//       }

//       return firstHistory.detail
//     }),

//   // // 删除 Proto 文件
//   // delete: protectedProcedure
//   //   .input(
//   //     z.object({
//   //       package: z.string(),
//   //     }),
//   //   )
//   //   .mutation(async ({ input }) => {
//   //     const { package: protoPackage } = input

//   //     // 检查 Proto 文件是否存在
//   //     const existingProto = await prisma.protoFile.findUnique({
//   //       where: { package: protoPackage },
//   //     })

//   //     if (!existingProto) {
//   //       throw new TRPCError({
//   //         code: 'NOT_FOUND',
//   //         message: 'Proto 文件不存在',
//   //       })
//   //     }

//   //     // 删除 Proto 文件（会自动与相关项目解除关联）
//   //     await prisma.protoFile.delete({
//   //       where: { package: protoPackage },
//   //     })

//   //     return { success: true }
//   //   }),

//   // 获取项目中所有 Proto 文件的历史变更
//   getProjectHistoriesWithinTimeRange: protectedProcedure
//     .input(
//       z.object({
//         projectId: z.number(),
//         days: z.number().int().positive().default(7),
//       }),
//     )
//     .query(async ({ input }) => {
//       const { projectId, days } = input

//       // 计算时间范围
//       const now = new Date()
//       let startDate = new Date()

//       // 设置为指定的天数
//       startDate.setDate(now.getDate() - days)

//       // 获取项目关联的所有 Proto 文件
//       const project = await prisma.project.findUnique({
//         where: { id: projectId },
//         include: {
//           protoList: {
//             select: {
//               pbInfoId: true,
//             },
//           },
//         },
//       })

//       if (!project) {
//         throw new TRPCError({
//           code: 'NOT_FOUND',
//           message: '项目不存在',
//         })
//       }

//       const protoFileIds = project.protoList.map(proto => proto.pbInfoId)

//       // 为每个 Proto 文件获取历史记录和变更
//       const protoChanges = await Promise.all(
//         protoFileIds.map(async pbInfoId => {
//           // 获取最新版本
//           const latestHistory = await prisma.protoFileHistory.findFirst({
//             where: { pbInfoId },
//             orderBy: { version: 'desc' },
//             select: {
//               id: true,
//               version: true,
//               modifyTime: true,
//               parseResult: true,
//               pbInfoId: true,
//               pbName: true,
//               pbFullPath: true,
//             },
//           })

//           if (!latestHistory) {
//             return {
//               pbInfoId,
//               latest: null,
//               base: null,
//               changes: null,
//             }
//           }

//           // 获取指定时间范围内的最早版本
//           const earliestHistory = await prisma.protoFileHistory.findFirst({
//             where: {
//               pbInfoId,
//               modifyTime: {
//                 gte: startDate.toISOString(),
//               },
//             },
//             orderBy: { version: 'asc' },
//             select: {
//               id: true,
//               version: true,
//               modifyTime: true,
//               parseResult: true,
//               pbInfoId: true,
//               pbName: true,
//               pbFullPath: true,
//             },
//           })

//           // 如果时间范围内没有早期版本，尝试获取时间范围之前的最新版本
//           const baseHistory =
//             earliestHistory ||
//             (await prisma.protoFileHistory.findFirst({
//               where: {
//                 pbInfoId,
//                 version: { lt: latestHistory.version },
//               },
//               orderBy: { version: 'desc' },
//               select: {
//                 id: true,
//                 version: true,
//                 modifyTime: true,
//                 parseResult: true,
//                 pbInfoId: true,
//                 pbName: true,
//                 pbFullPath: true,
//               },
//             }))

//           // 如果没有找到基准版本（可能是第一个版本），则只返回最新版本
//           if (!baseHistory) {
//             return {
//               pbInfoId,
//               pbName: latestHistory.pbName,
//               pbFullPath: latestHistory.pbFullPath,
//               latest: {
//                 id: latestHistory.id,
//                 version: latestHistory.version,
//                 modifyTime: latestHistory.modifyTime,
//                 parseResult: safeJsonParse(latestHistory.parseResult),
//               },
//               base: null,
//               changes: null,
//             }
//           }

//           // 比较两个版本
//           const latestParsed = safeJsonParse(latestHistory.parseResult)
//           const baseParsed = safeJsonParse(baseHistory.parseResult)

//           const changes = compareProtoVersions(baseParsed, latestParsed)

//           return {
//             pbInfoId,
//             pbName: latestHistory.pbName,
//             pbFullPath: latestHistory.pbFullPath,
//             latest: {
//               id: latestHistory.id,
//               version: latestHistory.version,
//               modifyTime: latestHistory.modifyTime,
//               parseResult: latestParsed,
//             },
//             base: {
//               id: baseHistory.id,
//               version: baseHistory.version,
//               modifyTime: baseHistory.modifyTime,
//               parseResult: baseParsed,
//             },
//             changes,
//           }
//         }),
//       )

//       return {
//         projectId,
//         days,
//         protoChanges,
//       }
//     }),

//   // 获取单个 Proto 文件的历史变更
//   getHistoriesWithinTimeRange: protectedProcedure
//     .input(
//       z.object({
//         pbInfoId: z.number(),
//         days: z.number().int().positive().default(7),
//       }),
//     )
//     .query(async ({ input }) => {
//       const { pbInfoId, days } = input

//       // 计算时间范围
//       const now = new Date()
//       let startDate = new Date()

//       // 设置为指定的天数
//       startDate.setDate(now.getDate() - days)

//       // 获取最新版本
//       const latestHistory = await prisma.protoFileHistory.findFirst({
//         where: { pbInfoId },
//         orderBy: { version: 'desc' },
//         select: {
//           id: true,
//           version: true,
//           modifyTime: true,
//           parseResult: true,
//           pbName: true,
//           pbFullPath: true,
//         },
//       })

//       if (!latestHistory) {
//         throw new TRPCError({
//           code: 'NOT_FOUND',
//           message: 'Proto 文件历史不存在',
//         })
//       }

//       // 获取指定时间范围内的最早版本
//       const earliestHistory = await prisma.protoFileHistory.findFirst({
//         where: {
//           pbInfoId,
//           modifyTime: {
//             gte: startDate.toISOString(),
//           },
//         },
//         orderBy: { version: 'asc' },
//         select: {
//           id: true,
//           version: true,
//           modifyTime: true,
//           parseResult: true,
//         },
//       })

//       // 如果时间范围内没有早期版本，尝试获取时间范围之前的最新版本
//       const baseHistory =
//         earliestHistory ||
//         (await prisma.protoFileHistory.findFirst({
//           where: {
//             pbInfoId,
//             version: { lt: latestHistory.version },
//           },
//           orderBy: { version: 'desc' },
//           select: {
//             id: true,
//             version: true,
//             modifyTime: true,
//             parseResult: true,
//           },
//         }))

//       // 如果没有找到基准版本（可能是第一个版本），则只返回最新版本
//       if (!baseHistory) {
//         return {
//           pbInfoId,
//           pbName: latestHistory.pbName,
//           pbFullPath: latestHistory.pbFullPath,
//           latest: {
//             id: latestHistory.id,
//             version: latestHistory.version,
//             modifyTime: latestHistory.modifyTime,
//             parseResult: safeJsonParse(latestHistory.parseResult),
//           },
//           base: null,
//           changes: null,
//         }
//       }

//       // 比较两个版本
//       const latestParsed = safeJsonParse(latestHistory.parseResult)
//       const baseParsed = safeJsonParse(baseHistory.parseResult)

//       const changes = compareProtoVersions(baseParsed, latestParsed)

//       return {
//         pbInfoId,
//         pbName: latestHistory.pbName,
//         pbFullPath: latestHistory.pbFullPath,
//         latest: {
//           id: latestHistory.id,
//           version: latestHistory.version,
//           modifyTime: latestHistory.modifyTime,
//           parseResult: latestParsed,
//         },
//         base: {
//           id: baseHistory.id,
//           version: baseHistory.version,
//           modifyTime: baseHistory.modifyTime,
//           parseResult: baseParsed,
//         },
//         changes,
//       }
//     }),

//   // 列出所有 Proto 文件
//   list: publicProcedure
//     .input(
//       z
//         .object({
//           projectId: z.number().optional(), // 可选的项目 ID，用于过滤特定项目的 Proto 文件
//         })
//         .optional(),
//     )
//     .query(async ({ input }) => {
//       const projectId = input?.projectId

//       // 如果指定了项目 ID，则只返回该项目关联的 Proto 文件
//       if (projectId) {
//         const project = await prisma.project.findUnique({
//           where: { id: projectId },
//           include: {
//             protoList: {
//               include: {
//                 histories: {
//                   orderBy: { version: 'desc' },
//                   select: {
//                     createRtx: true,
//                     id: true,
//                     modifyTime: true,
//                     pbInfoId: true,
//                     versionStr: true,
//                     pbFullPath: true,
//                     projectFullPath: true,
//                     pbName: true,
//                   },
//                   take: 1,
//                 },
//               },
//             },
//           },
//         })

//         if (!project) {
//           throw new TRPCError({ code: 'NOT_FOUND', message: '项目不存在' })
//         }

//         return project.protoList
//       }

//       // 否则返回所有 Proto 文件
//       const protoFiles = await prisma.protoFile.findMany({
//         include: {
//           histories: {
//             orderBy: { version: 'desc' },
//             select: {
//               createRtx: true,
//               id: true,
//               modifyTime: true,
//               pbInfoId: true,
//               versionStr: true,
//               pbFullPath: true,
//               projectFullPath: true,
//               pbName: true,
//             },
//             take: 1,
//           },
//         },
//       })

//       return protoFiles
//     }),
// })
