import { z } from 'zod'
import {
  protectedProcedure,
  router,
  adminProcedure,
  projectOwnerProcedure,
  projectProcedure,
  projectWriteProcedure,
} from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'
import { GitService, GetRepositoryProtoMeta } from '../services/repository'
import { omit } from 'lodash-es'
import { parseProtoFile } from '../services/parser'
import { safeJsonParse } from '../utils/common'

// 定义变更类型接口
interface ProtoFileChanges {
  services: Record<string, { changeType: string }>
  methods: Record<string, Record<string, { changeType: string }>>
  messages: Record<
    string,
    {
      changeType: string
      fields?: Record<string, { changeType: string }>
    }
  >
  fields: Record<string, any>
}

export const protoRouter = router({
  getContentBatch: projectProcedure
    .input(
      z.object({
        commitIds: z.array(z.string()),
      }),
    )
    .query(async ({ ctx: { project }, input }) => {
      const { commitIds } = input
      const gitService = new GitService(project.createdByUser)

      const files = await prisma.protoFile.findMany({
        where: { projectId: project.id },
      })
      const contents = await prisma.protoHistory.findMany({
        where: { fileId: { in: files.map(f => f.id) }, commitId: { in: commitIds } },
      })
      const errorCommitIds = commitIds.filter(c => !contents.some(h => h.commitId === c))
      if (errorCommitIds.length > 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `Commit ids not found: ${errorCommitIds.join(', ')}`,
        })
      }
      // 确保文件内容都下载了
      for (const h of contents) {
        if (!h.raw || !h.meta) {
          console.log(`获取文件内容:  ${h.commitId}`)
          const file = files.find(f => f.id === h.fileId)!
          const content = await gitService.getRepositoryFileContent(
            project.repository,
            project.name,
            file.name,
            h.commitId,
          )
          const meta = await gitService.getRepositoryFileContent(
            project.repository,
            project.name,
            file.name,
            h.commitId,
            'json',
          )
          let parseResult = null as any
          try {
            parseResult = await parseProtoFile(content)
          } catch (error) {
            console.error(`解析错误: ${file.name} ${h.commitId}`, (error as Error)?.message)
          }
          await prisma.protoHistory.update({
            where: { id: h.id },
            data: { raw: content, json: parseResult ? JSON.stringify(parseResult) : '', meta },
          })
          h.raw = content
          h.json = parseResult ? JSON.stringify(parseResult) : ''
          h.meta = meta
        }
      }
      return contents.map(i => omit(i, ['raw']))
    }),

  // 注意: getProtoFileForService 方法已移除，现在在前端通过 extractComponents 实现服务到文件的映射
  // 这种实现更高效，避免了额外的网络请求获取 ProtoFile 信息

  /**
   * 获取项目中所有 Proto 文件的解析内容, 并关联获取依赖的外部 proto 内容
   */
  getParsedContentByProject: projectProcedure.query(async ({ ctx: { project } }) => {
    const gitService = new GitService(project.createdByUser)

    const protoFiles = await prisma.protoFile.findMany({
      where: { projectId: project.id },
      include: {
        histories: {
          orderBy: { updatedAt: 'desc' },
          take: 1,
          select: { meta: true, json: true, commitId: true, id: true },
        },
      },
    })
    for (const file of protoFiles) {
      if (!file || !file.histories.length) continue
      await gitService.ensureProtoFileHistoryContent({
        repository: project.repository,
        projectName: project.name,
        fileName: file.name,
        historyItem: file.histories[0],
      })
    }
    const extraProtoFileContents = []
    for (const file of protoFiles) {
      const meta = safeJsonParse<GetRepositoryProtoMeta>(file.histories[0].meta ?? '')
      for (const importName of meta?.importName ?? []) {
        const [repo, projectName, fileName] = importName.name.split('/')
        if (repo !== project.repository || projectName !== project.name) {
          const targetProject = await prisma.project.findFirst({
            where: { repository: repo, name: projectName },
          })
          if (!targetProject) continue
          const file = await prisma.protoFile.findFirst({
            where: { name: fileName, projectId: targetProject.id },
            include: {
              histories: {
                orderBy: { updatedAt: 'desc' },
                take: 1,
                select: { meta: true, json: true, commitId: true, id: true },
              },
            },
          })
          if (!file || !file.histories.length) continue
          await gitService.ensureProtoFileHistoryContent({
            repository: project.repository,
            projectName: project.name,
            fileName: file.name,
            historyItem: file.histories[0],
          })
          if (file?.histories.length) {
            extraProtoFileContents.push(file.histories[0])
          }
        }
      }
    }
    return {
      list: protoFiles.map(i => i.histories[0]),
      extra: extraProtoFileContents,
    }
  }),

  // 添加一个方法，用于获取项目中所有 Proto 文件在指定时间范围内的历史变更
  getProjectHistoriesWithinTimeRange: projectProcedure
    .input(
      z.object({
        days: z.number().default(7),
      }),
    )
    .query(async ({ ctx: { project }, input }) => {
      const { days } = input

      // 计算起始时间（当前时间减去指定天数）
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      // 获取项目下所有 Proto 文件
      const protoFiles = await prisma.protoFile.findMany({
        where: {
          projectId: project.id,
        },
        include: {
          histories: {
            orderBy: {
              updatedAt: 'desc',
            },
            take: 2, // 获取最新的两个版本，用于比较变更
            where: {
              updatedAt: {
                gte: startDate,
              },
            },
          },
        },
      })

      // 构造变更数据结构
      const protoChanges = protoFiles
        .filter(file => file.histories.length >= 1) // 至少有一个历史记录
        .map(file => {
          // 简单的变更检测逻辑
          const latestHistory = file.histories[0]
          const previousHistory = file.histories[1] // 可能是 undefined

          let changes: ProtoFileChanges | null = null
          if (latestHistory && previousHistory) {
            // 如果有前一个版本，执行差异比较
            try {
              const latestContent = JSON.parse(latestHistory.json)
              const previousContent = JSON.parse(previousHistory.json)

              // 简化后的差异检测（实际实现会更复杂）
              changes = {
                services: {}, // 服务变更
                methods: {}, // 方法变更
                messages: {}, // 消息类型变更
                fields: {}, // 字段变更
              }

              // 示例：检测服务和方法的添加、删除、修改
              // 实际实现会更加复杂，这里只是简化示例
              const latestServices = latestContent?.root?.nested || {}
              const previousServices = previousContent?.root?.nested || {}

              // 处理服务变更
              Object.keys(latestServices).forEach(serviceName => {
                const service = latestServices[serviceName]
                if (service.methods) {
                  // 这是一个服务
                  if (!previousServices[serviceName]) {
                    // 新增的服务
                    if (changes) {
                      changes.services[serviceName] = { changeType: 'added' }
                    }
                  } else {
                    // 检查方法变更
                    const methods = service.methods
                    const prevMethods = previousServices[serviceName].methods || {}

                    Object.keys(methods).forEach(methodName => {
                      if (!prevMethods[methodName]) {
                        // 新增的方法
                        if (changes) {
                          if (!changes.methods[serviceName]) changes.methods[serviceName] = {}
                          changes.methods[serviceName][methodName] = { changeType: 'added' }
                        }
                      } else if (
                        JSON.stringify(methods[methodName]) !==
                        JSON.stringify(prevMethods[methodName])
                      ) {
                        // 修改的方法
                        if (changes) {
                          if (!changes.methods[serviceName]) changes.methods[serviceName] = {}
                          changes.methods[serviceName][methodName] = { changeType: 'modified' }
                        }
                      }
                    })

                    // 检查删除的方法
                    Object.keys(prevMethods).forEach(methodName => {
                      if (!methods[methodName]) {
                        if (changes) {
                          if (!changes.methods[serviceName]) changes.methods[serviceName] = {}
                          changes.methods[serviceName][methodName] = { changeType: 'deleted' }
                        }
                      }
                    })
                  }
                }
              })

              // 检查删除的服务
              Object.keys(previousServices).forEach(serviceName => {
                const service = previousServices[serviceName]
                if (service.methods && !latestServices[serviceName]) {
                  // 删除的服务
                  if (changes) {
                    changes.services[serviceName] = { changeType: 'deleted' }
                  }
                }
              })

              // 处理消息类型变更
              // 实际实现会更复杂
            } catch (err) {
              console.error(`Failed to compare proto history for ${file.name}:`, err)
            }
          }

          return {
            fileId: file.id,
            fileName: file.name,
            latestCommitId: latestHistory?.commitId,
            latestUpdatedAt: latestHistory?.updatedAt,
            changes,
          }
        })

      return {
        protoChanges,
        startDate,
        endDate: new Date(),
      }
    }),
})
