// import { prisma } from '../prisma'
// import axios from 'axios'
// import https from 'https'

// type RickProtoResponse = {
//   success: boolean
//   list: ProtoHistoryRaw[]
// }

// export type IncludeFileDescJSON = {
//   currVersion: number
//   detailId: number
//   name: string
//   pbId: number
//   pbName: string
//   projectId: number
//   showVersion: string
//   version: string
// }[]

// export type GitUploadCfgJSON = {
//   projectFullPath: string
//   tagName: string
//   tarName: string
// }

// export type ProtoHistoryRaw = {
//   id: number
//   pbInfoId: number
//   detail: string
//   version: number
//   modifyTime: string
//   buildStatus: number
//   filesMd5: string
//   createRtx: string
//   includeFileDesc: string
//   envType: number
//   detailDesc: string
//   gitUploadCfg: string
//   branchId: number
//   versionStr: string
//   uploadStatus: string
//   envTypeStr: string
//   buildStatusName: string
// }

// export const getProto = async (
//   pbInfoId: number,
//   username: string,
// ): Promise<{
//   proto: { pbInfoId: number; histories: ProtoHistoryRaw[] }
//   relatedProtos: { pbInfoId: number; histories: ProtoHistoryRaw[] }[]
// }> => {
//   const user = await prisma.user.findUnique({ where: { username } })
//   if (!user) {
//     throw new Error('用户不存在')
//   }
//   const cookie = user.rickCookie
//   if (!cookie) {
//     throw new Error('用户没有rick cookie')
//   }

//   const histories = await getProtoHistories(pbInfoId, cookie)
//   const relatedProtos: { pbInfoId: number; histories: ProtoHistoryRaw[] }[] = []
//   const firstHistory = histories[0]
//   const includeFileDesc = firstHistory.includeFileDesc
//     ? (JSON.parse(firstHistory.includeFileDesc) as IncludeFileDescJSON)
//     : []
//   console.log(includeFileDesc)
//   for (const item of includeFileDesc) {
//     const relatedHistories = await getProtoHistories(item.pbId, cookie)
//     relatedProtos.push({ pbInfoId: item.pbId, histories: relatedHistories })
//   }
//   return {
//     proto: { pbInfoId, histories },
//     relatedProtos,
//   }
// }

// async function getProtoHistories(pbInfoId: number, cookie: string): Promise<ProtoHistoryRaw[]> {
//   const res = await axios
//     .post('https://trpc.rick.woa.com/rick/pb/listDeatal', `pbId=${pbInfoId}`, {
//       headers: {
//         Cookie: cookie,
//         'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
//         'X-Requested-With': 'XMLHttpRequest',
//       },
//       httpsAgent: new https.Agent({
//         rejectUnauthorized: false,
//       }),
//     })
//     .catch(e => {
//       if (e.response?.status === 403) {
//         throw new Error('用户没有权限')
//       } else if (e.response?.status === 401) {
//         throw new Error('用户没有权限, 或者 rick cookie 过期')
//       }
//       console.error(e)
//       throw e
//     })
//   const data = res.data as RickProtoResponse
//   if (!data.success || !data.list.length) {
//     console.error(data)
//     throw new Error(`获取proto失败 ${pbInfoId}`)
//   }
//   return data.list.filter(i => i.buildStatus === 1 && i.gitUploadCfg !== null)
// }
