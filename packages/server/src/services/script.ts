import { z } from 'zod'
import { prisma } from '../prisma'
import { publicProcedure, router } from '../trpc'
import ts from 'typescript'
import * as babel from '@babel/core'
import { ScriptLang } from '../../prisma/prisma'

/**
 * 将（ts/js）代码转成 es5
 */
export const covertScriptToEs5 = async (script: string, lang: ScriptLang) => {
  let jsCode = script
  try {
    // 如果是 TypeScript，编译为 JavaScript
    if (lang === 'TS') {
      const compilerOptions: ts.CompilerOptions = {
        target: ts.ScriptTarget.ES2015, // 先编译到 ES2015
        module: ts.ModuleKind.ES2015,
        esModuleInterop: true,
        strict: true,
        removeComments: false,
      }

      // 编译 TypeScript 代码
      const result = ts.transpileModule(script, {
        compilerOptions,
        reportDiagnostics: true,
      })

      // 检查编译错误
      if (result.diagnostics && result.diagnostics.length > 0) {
        const errors = result.diagnostics
          .map((diagnostic: ts.Diagnostic) => {
            const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n')
            if (diagnostic.file && diagnostic.start !== undefined) {
              const { line, character } = diagnostic.file.getLineAndCharacterOfPosition(
                diagnostic.start,
              )
              return `Error ${diagnostic.file.fileName} (${line + 1},${character + 1}): ${message}`
            }
            return `Error: ${message}`
          })
          .join('\n')

        throw new Error(`TypeScript 编译错误:\n${errors}`)
      }

      jsCode = result.outputText
    }

    // 处理 TypeScript 编译后的代码，确保正确导出函数
    // 检查是否有 export default 语句
    const hasDefaultExport = /export\s+default/.test(jsCode)

    // 如果没有默认导出，尝试查找可能的函数定义
    if (!hasDefaultExport) {
      // 查找函数定义
      const functionMatch = /function\s+(\w+)\s*\(/.exec(jsCode)
      if (functionMatch && functionMatch[1]) {
        const functionName = functionMatch[1]
        // 添加默认导出
        jsCode += `\n\nvar generateCode = ${functionName};\n`
      } else {
        throw new Error('未找到函数定义，无法添加导出')
      }
    } else {
      // 将 export default function 转换为 var generateCode = function
      jsCode = jsCode.replace(/export\s+default\s+function/g, 'var generateCode = function')
      jsCode = jsCode.replace(
        /export\s+default\s+async\s+function/g,
        'var generateCode = async function',
      )
      // 处理其他形式的默认导出
      jsCode = jsCode.replace(/export\s+default\s+(\w+)/g, 'var generateCode = $1')
    }

    // 移除其他 import 语句
    jsCode = jsCode.replace(/import\s+.*?from\s+['"].*?['"];?/g, '')
    jsCode = jsCode.replace(/import\s+{.*?}\s+from\s+['"].*?['"];?/g, '')

    // 使用 Babel 将 ES2015 代码转换为 ES5
    const babelResult = await babel.transformAsync(jsCode, {
      presets: [
        [
          '@babel/preset-env',
          {
            targets: {
              ie: '11',
            },
          },
        ],
      ],
      comments: false,
    })

    if (!babelResult || !babelResult.code) {
      throw new Error('Babel 转换失败')
    }

    // 最终的 ES5 代码
    const es5Code = babelResult.code

    // 添加包装函数，使其在 eval5 中可以正确执行
    const finalCode = `
// 编译后的脚本代码
${es5Code}

// 执行函数包装器
function executeScript(apiData) {
  try {
    // 检查是否存在生成函数
    if (typeof generateCode !== 'function') {
      throw new Error('Script must export a function');
    }
    
    // 调用函数并返回结果
    var result = generateCode(apiData);
    
    // 处理 Promise
    if (result && typeof result.then === 'function') {
      return result.then(function(data) {
        if (typeof data !== 'string') {
          throw new Error('Script function must return a string!');
        }
        return data;
      });
    }
    
    // 检查返回结果
    if (typeof result !== 'string') {
      throw new Error('Script function must return a string, got ' + typeof result);
    }
    
    return result;
  } catch (err) {
    throw new Error('Script execution error: ' + (err.message || 'Unknown error'));
  }
}
`

    return finalCode
  } catch (error) {
    console.error('编译脚本失败:', error)
    throw error
  }
}
