import axios from 'axios'
import { random } from 'lodash-es'

interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

interface ModelChatOptions {
  messages: ChatMessage[]
  model?: string
  temperature?: number
  queryId?: string
}

export async function* modelChatCompletions({
  messages,
  model = process.env.API_MODEL,
  temperature = 1,
  queryId = random() + '',
}: ModelChatOptions): AsyncGenerator<string, void, unknown> {
  const response = await axios.post(
    `${process.env.API_BASE_URL}/chat/completions`,
    {
      query_id: queryId,
      model,
      messages,
      temperature,
      stream: true,
    },
    {
      headers: {
        'Content-Type': 'application/json',
        Authorization: process.env.API_KEY,
      },
      responseType: 'stream',
    },
  )

  let buffer = ''

  for await (const chunk of response.data) {
    buffer += chunk.toString()
    const lines = buffer.split('\n')
    buffer = lines.pop() || ''

    for (const line of lines) {
      if (line.trim().startsWith('data:')) {
        try {
          const dataJson = JSON.parse(line.trim().replace('data:', ''))
          const content = dataJson.choices[0].delta.content
          if (content) {
            yield content
          }
        } catch (e) {
          console.error('解析响应数据失败:', e)
        }
      }
    }
  }
}
