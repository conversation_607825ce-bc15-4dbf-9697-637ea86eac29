import * as protobuf from 'protobufjs'
import { TRPCError } from '@trpc/server'

export type ParsedProtoJSON = {
  package: string
  root: any
  services: Record<
    string,
    {
      methods: Record<
        string,
        { requestType: string; responseType: string; comment?: string | null }
      >
      comment?: string | null
    }
  >
  messages: Record<
    string,
    {
      comment?: string | null
      fields: Record<string, { type: string; id: number; comment: string | null }>
    }
  >
  enums: Record<
    string,
    {
      comment?: string | null
      values: Record<string, number>
      comments: Record<string, string | null>
    }
  >
}

export const baseType = {
  double: '64 位浮点数',
  float: '32 位浮点数',
  int32: '32 位整数 (可变长度编码, 负数效率低)',
  int64: '64 位整数 (可变长度编码, 负数效率低)',
  uint32: '32 位无符号整数 (可变长度编码)',
  uint64: '64 位无符号整数 (可变长度编码)',
  sint32: '32 位整数 (ZigZag 编码, 负数效率高)',
  sint64: '64 位整数 (ZigZag 编码, 负数效率高)',
  fixed32: '32 位无符号整数 (固定长度, 4 字节)',
  fixed64: '64 位无符号整数 (固定长度, 8 字节)',
  sfixed32: '32 位整数 (固定长度, 4 字节)',
  sfixed64: '64 位整数 (固定长度, 8 字节)',
  bool: '布尔值 (true/false)',
  string: 'UTF-8 编码字符串',
  bytes: '任意字节序列',
}

/**
 * 解析 Proto 文件内容
 * @param content Proto 文件内容
 * @returns 解析后的 JSON 字符串
 */
export const parseProtoFile = async (content: string) => {
  try {
    // 使用 protobuf.js 解析 Proto 文件内容
    const parsed = protobuf.parse(fixProtoFile(content), {
      keepCase: true,
      alternateCommentMode: true,
      preferTrailingComment: true,
    })

    const root = parsed.root.toJSON({ keepComments: true })

    // 提取有用的信息
    const result = extractProtoInfo({ ...parsed, root })

    // 转换为 JSON 字符串，供前端使用
    return result
  } catch (error) {
    console.error('解析 Proto 文件时出错:', error)
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: `Proto 文件解析失败: ${(error as Error).message}`,
    })
  }
}

const fixProtoFile = (content: string) => {
  return content.replace(/;;/g, ';')
}

/**
 * 从 Proto 解析结果中提取有用的信息
 * @param parseResult protobuf.js 解析结果
 * @returns 提取后的结构化信息
 */
function extractProtoInfo(parseResult: any): ParsedProtoJSON {
  const { package: packageName, root } = parseResult

  // 提取服务、消息和枚举
  const services: Record<string, any> = {}
  const messages: Record<string, any> = {}
  const enums: Record<string, any> = {}

  // 处理根级别的嵌套内容
  if (root && root.nested) {
    processNestedContent(root.nested, services, messages, enums)
  }

  return {
    package: packageName as string,
    root: parseResult.root,
    services,
    messages,
    enums,
  }
}

/**
 * 递归处理嵌套内容
 * @param nested 嵌套内容
 * @param services 服务集合
 * @param messages 消息集合
 * @param enums 枚举集合
 * @param prefix 前缀（用于嵌套命名）
 */
function processNestedContent(
  nested: Record<string, any>,
  services: Record<string, any>,
  messages: Record<string, any>,
  enums: Record<string, any>,
  prefix = '',
) {
  for (const key in nested) {
    const item = nested[key]
    const fullName = prefix ? `${prefix}.${key}` : key

    if (item.methods) {
      // 这是一个服务
      services[fullName] = item
    } else if (item.fields) {
      // 这是一个消息
      messages[fullName] = item
    } else if (item.values) {
      // 这是一个枚举
      enums[fullName] = item
    }

    // 递归处理嵌套内容
    if (item.nested) {
      processNestedContent(item.nested, services, messages, enums, fullName)
    }
  }
}

/**
 * 使用 protobuf.js 的 load 方法从文件内容加载 Proto 定义
 * 这个方法更适合处理导入语句
 * @param content Proto 文件内容
 * @returns 解析后的 Root 对象
 */
export const loadProtoFromContent = async (content: string): Promise<protobuf.Root> => {
  try {
    // 创建一个临时的 Root 对象
    const root = new protobuf.Root()

    // 从内容加载 Proto 定义
    return await root.load(content, { keepCase: true })
  } catch (error) {
    console.error('加载 Proto 文件时出错:', error)
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: `Proto 文件加载失败: ${(error as Error).message}`,
    })
  }
}

/**
 * 从 Proto 文件内容中提取包名
 * @param content Proto 文件内容
 * @returns 包名
 */
export const extractPackageName = (content: string): string | null => {
  const packageMatch = content.match(/package\s+([a-zA-Z0-9_.]+)\s*;/)
  return packageMatch ? packageMatch[1] : null
}

export const mergeParsedProtoJSON = (jsonList: ParsedProtoJSON[]) => {
  const result: ParsedProtoJSON = {
    package: '',
    root: {},
    services: {},
    messages: {},
    enums: {},
  }
  jsonList.forEach(json => {
    result.services = { ...result.services, ...json.services }
    result.messages = { ...result.messages, ...json.messages }
    result.enums = { ...result.enums, ...json.enums }
  })
  return result
}

export type ApiDataInfo = {
  packageName: string
  serviceName: string
  serviceComment: string | null | undefined
  methodName: string
  methodComment: string | null | undefined
  requestType: string
  responseType: string
  messages: Array<{
    name: string
    comment: string | null | undefined
    fields: Record<string, { type: string; id: number; comment: string | null }>
  }>
  enums: Array<{
    name: string
    comment: string | null | undefined
    values: Record<string, number>
    comments: Record<string, string | null>
  }>
}

export const getServiceMethodContent = (
  json: ParsedProtoJSON,
  packageName: string,
  serviceName: string,
  methodName: string,
) => {
  if (!json['services'][`${packageName}.${serviceName}`]) {
    throw new TRPCError({ code: 'BAD_REQUEST', message: `服务 ${serviceName} 不存在` })
  }

  const service = json['services'][`${packageName}.${serviceName}`]
  if (!service.methods[methodName]) {
    throw new TRPCError({ code: 'BAD_REQUEST', message: `方法 ${methodName} 不存在` })
  }
  const method = service.methods[methodName]

  const result: ApiDataInfo = {
    packageName,
    serviceName,
    serviceComment: service.comment,
    methodName,
    methodComment: method.comment,
    requestType: method.requestType,
    responseType: method.responseType,
    messages: [],
    enums: [],
  }

  const fixType = (type: string) => {
    if (!type.startsWith('trpc.')) return `${packageName}.${type}`
    return type
  }

  // 用于存储已处理过的类型，避免重复处理
  const processedTypes = new Set<string>()

  // 递归收集类型引用
  const collectTypeReferences = (typeName: string) => {
    // 如果已经处理过这个类型，直接返回
    if (processedTypes.has(typeName)) {
      return
    }
    processedTypes.add(typeName)

    // 检查是否是消息类型
    if (json.messages[typeName]) {
      const message = json.messages[typeName]
      result.messages.push({
        name: typeName,
        comment: message.comment,
        fields: message.fields,
      })

      // 递归处理消息中的每个字段
      Object.values(message.fields).forEach(field => {
        if (!baseType[field.type as keyof typeof baseType]) {
          collectTypeReferences(fixType(field.type))
        }
      })
    }
    // 检查是否是枚举类型
    else if (json.enums[typeName]) {
      const enumType = json.enums[typeName]
      result.enums.push({
        name: typeName,
        comment: enumType.comment,
        values: enumType.values,
        comments: enumType.comments,
      })
    }
  }

  // 处理请求和响应类型
  collectTypeReferences(fixType(method.requestType))
  collectTypeReferences(fixType(method.responseType))

  return result
}
