import axios, { AxiosInstance, isAxiosError } from 'axios'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'
import { parseProtoFile } from './parser'

export class GitService {
  private readonly http: AxiosInstance

  constructor(info: { username: string; gitAccessToken: string | null }) {
    const is_dev_user = info.username === 'dev_user'
    if (!info.gitAccessToken && !is_dev_user) {
      throw new TRPCError({ code: 'FORBIDDEN', message: '没有绑定工蜂 Git 账号' })
    }
    this.http = axios.create({
      baseURL: 'https://git.woa.com/api/v3',
      headers: info.gitAccessToken
        ? { 'OAUTH-TOKEN': info.gitAccessToken }
        : is_dev_user
          ? { 'PRIVATE-TOKEN': process.env.PRIVATE_TOKEN! }
          : {},
    })
    this.http.interceptors.response.use(
      response => response,
      error => {
        if (isAxiosError(error)) {
          console.log(`git.woa.com 请求失败(${error.config?.url}): ${error.response?.status}`)
          console.error(error.response?.data)
        } else {
          console.error(error)
        }
        if (error.response?.status === 429) {
          throw new TRPCError({ code: 'TOO_MANY_REQUESTS', message: '请求过于频繁' })
        }
        return Promise.reject(isAxiosError(error) ? error.response?.data : error)
      },
    )
  }

  public async getRepositoryModuleFiles(repositoryFullName: string, module: string) {
    const name = repositoryFullName.startsWith('rick_proto/')
      ? repositoryFullName
      : `rick_proto/${repositoryFullName}`
    const res = await this.http.get(
      `projects/${encodeURIComponent(name)}/repository/tree?max_depth=1&merge_alone_dir=false&page=0&per_page=100&path=${module}`,
    )
    const data = res.data as GetRepositoryFilesItem[]
    return data
      .filter(item => item.type === 'blob' && item.name.endsWith('.proto'))
      .map(i => i.name.replace('.proto', ''))
  }

  public async getRepositoryModules(repositoryFullName: string) {
    const name = repositoryFullName.startsWith('rick_proto/')
      ? repositoryFullName
      : `rick_proto/${repositoryFullName}`
    const res = await this.http.get(
      `projects/${encodeURIComponent(name)}/repository/tree?max_depth=1&merge_alone_dir=false&page=0&per_page=100`,
    )
    const data = res.data as GetRepositoryFilesItem[]
    return data.filter(item => item.type === 'tree').map(item => item.name)
  }

  public async getRepositoryFileContent(
    repositoryFullName: string,
    module: string,
    file: string,
    commitId: string = 'master',
    format: 'proto' | 'json' = 'proto',
  ) {
    const name = repositoryFullName.startsWith('rick_proto/')
      ? repositoryFullName
      : `rick_proto/${repositoryFullName}`
    const res = await this.http.get(
      `projects/${encodeURIComponent(name)}/repository/files?file_path=${encodeURIComponent(`${module}/${file}.${format}`)}&ref=${commitId}`,
    )
    const data = res.data as GetRepositoryFileContent
    const decodedContent = Buffer.from(data.content, 'base64').toString('utf-8')
    return decodedContent
  }

  public async getRepositoryFileHistory(repositoryFullName: string, module: string, file: string) {
    const name = repositoryFullName.startsWith('rick_proto/')
      ? repositoryFullName
      : `rick_proto/${repositoryFullName}`
    const res = await this.http.get(
      `projects/${encodeURIComponent(name)}/repository/files/${encodeURIComponent(`${module}/${file}.proto`)}/blame?ref=master`,
    )
    const data = res.data as GetRepositoryFileHistory[]

    const result = new Map<string, { commitId: string; username: string; updatedAt: Date }>()
    for (const item of data) {
      if (result.has(item.commit.id)) {
        continue
      }
      result.set(item.commit.id, {
        commitId: item.commit.id,
        username: item.commit.message.match(/:(\w+):/)?.[1] ?? '',
        updatedAt: new Date(item.commit.committed_date),
      })
    }
    return Array.from(result.values()).sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
  }

  public async getRepositoryFileHistoryBatch(params: GetRepositoryFileHistoryBatchParams) {
    const { repositoryFullName, module, files } = params

    // 验证文件数量不超过20个
    if (files.length > 20) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: '批量文件数不能超过 20 个' })
    }

    // 构建请求体
    const requestBody: Record<string, any> = {
      file_path: files.map(file => `${module}/${file}.proto`),
      ref: 'master',
    }

    const name = repositoryFullName.startsWith('rick_proto/')
      ? repositoryFullName
      : `rick_proto/${repositoryFullName}`

    const res = await this.http.post(
      `projects/${encodeURIComponent(name)}/repository/files/blames`,
      requestBody,
    )

    const list = res.data as GetRepositoryFileHistoryBatchResult[]

    return list.map((data, index) => {
      const result = new Map<string, { commitId: string; username: string; updatedAt: Date }>()
      for (const item of data.annotateds) {
        if (!item.commit?.id) continue

        if (result.has(item.commit.id)) continue

        result.set(item.commit.id, {
          commitId: item.commit.id,
          username: item.commit.message.match(/:(\w+):/)?.[1] ?? '',
          updatedAt: new Date(item.commit.committed_date),
        })
      }

      return {
        file: files.find(f => data.fileName === `${module}/${f}.proto`) || null,
        history: Array.from(result.values()).sort(
          (a, b) => b.updatedAt.getTime() - a.updatedAt.getTime(),
        ),
      }
    })
  }

  /**
   * 获取项目组详细信息及其下所有项目
   * @param groupId 项目组ID或全路径
   * @param includeSubgroups 是否包含子组项目
   * @returns 项目组详细信息及其下所有项目
   */
  public async getProjectsInGroup(groupName: string) {
    const res = await this.http.get(`groups/${encodeURIComponent(groupName)}`)
    const data = res.data as GroupDetails
    return data.projects.map(i => i.path)
  }

  public async searchProjectsInGroup(groupName: string, keyword: string) {
    const res = await this.http.get(`groups/${encodeURIComponent(groupName)}`)
    const data = res.data as GroupDetails
    return data.projects.map(i => i.path).filter(i => i.includes(keyword))
  }

  public async ensureProtoFileHistoryContent(info: {
    repository: string
    projectName: string
    fileName: string
    historyItem: { id: number; commitId: string; meta: string | null; json: string }
  }) {
    if (info.historyItem.json && info.historyItem.meta) return
    const content = await this.getRepositoryFileContent(
      info.repository,
      info.projectName,
      info.fileName,
      info.historyItem.commitId,
    )
    const meta = await this.getRepositoryFileContent(
      info.repository,
      info.projectName,
      info.fileName,
      info.historyItem.commitId,
      'json',
    )
    let parseResult = null as any
    try {
      parseResult = await parseProtoFile(content)
    } catch (error) {
      console.error(
        `解析错误: ${info.fileName} ${info.historyItem.commitId}`,
        (error as Error)?.message,
      )
    }
    await prisma.protoHistory.update({
      where: { id: info.historyItem.id },
      data: { raw: content, json: parseResult ? JSON.stringify(parseResult) : '', meta },
    })
    info.historyItem.json = parseResult ? JSON.stringify(parseResult) : ''
    info.historyItem.meta = meta
  }
}

// const http = axios.create({
//   baseURL: 'https://git.woa.com/api/v3',
//   headers: {
//     'PRIVATE-TOKEN': process.env.PRIVATE_TOKEN,
//   },
// })

export interface GetRepositoryFilesItem {
  id: string
  name: string
  type: 'blob' | 'tree'
  mode: string
}

export interface GetRepositoryFileContent {
  ref: string
  content: string
  encoding: string
  file_name: string
  file_path: string
  size: number
  blob_id: string
  commit_id: string
  blob_mode: string
  blob_type: string
}

export interface GetRepositoryFileHistory {
  commit: {
    id: string
    short_id: string
    authored_date: string
    author_name: string
    committer_name: string
    committed_date: string
    message: string
  }
}

/** 批量获取文件历史 */
export interface GetRepositoryFileHistoryBatchParams {
  repositoryFullName: string
  module: string
  files: string[]
}

export interface GetRepositoryFileHistoryBatchCommit {
  id: string
  message: string
  parent_ids: string[]
  authored_date: string
  author_name: string
  author_email: string
  committed_date: string
  committer_name: string
  committer_email: string
  title: string
  created_at: string
  short_id: string
}

export interface GetRepositoryFileHistoryBatchAnnotated {
  commit: GetRepositoryFileHistoryBatchCommit | null
  lines: string[]
}

export interface GetRepositoryFileHistoryBatchResult {
  fileName: string
  annotateds: GetRepositoryFileHistoryBatchAnnotated[]
}

/**
 * 获取项目组详细信息及其下所有项目
 */
export interface GroupDetails {
  id: number
  name: string
  path: string
  description: string
  avatar_url: string
  full_name: string
  full_path: string
  web_url: string
  parent_id: number | null
  projects: GroupProject[]
  sub_projects: string[] // 假定为 string[] 类型，后续可调整
}

export interface GroupProject {
  path: string
  path_with_namespace: string
}

export interface SearchProjectsResult {
  namespace: { name: string; path: string; avatar_url: string }
  name: string
  path: string
  avatar_url: string
}

/**
 # 查看仓库文件列表
curl -X GET "https://git.woa.com/api/v3/projects/rick_proto%2Fpublishing_application/repository/tree?max_depth=3&merge_alone_dir=true&page=0&per_page=10" -H "accept: *" -H "PRIVATE-TOKEN: TMoT-QP7LOf0mNoa4GWA" -H "X-XSRF-TOKEN: 3f4bd37d-1ad9-427d-be02-cb5e6c996f8a"
# 返回内容
[{"id":"d877c5bce328119ff7c91febb3d5e7236abf1688","name":"README.md","type":"blob","mode":"100644"},{"id":"dd4a166b95c5a32c17315f28f2144126417275fc","name":"cnbot","type":"tree","mode":"040000"},{"id":"be4526dfff9aaf771444e023bc9c82751eee0b2a","name":"cnbot/oidb_test.json","type":"blob","mode":"100644"},{"id":"2d62e5facc49ff049cf03b3732defe66f6277af3","name":"cnbot/oidb_test.proto","type":"blob","mode":"100644"},{"id":"5260d508fcc37cf012dc585675a7d6f5091fd5bd","name":"cnbot/operations.desc","type":"blob","mode":"100644"},{"id":"0bdc20b460c501297551b20e7067cbc95e233fce","name":"cnbot/operations.json","type":"blob","mode":"100644"},{"id":"176a16c1aaed739548cf5fef374299b75285712c","name":"cnbot/operations.proto","type":"blob","mode":"100644"},{"id":"93a2fa3388beb519e1da953cd09e9344f795f8e5","name":"commodity","type":"tree","mode":"040000"},{"id":"49b844e73675526751e202dbe76b37f80246ee5e","name":"commodity/act_present.desc","type":"blob","mode":"100644"},{"id":"9d31328ddde683b72f4c599747fb97aa4ac6e6e0","name":"commodity/act_present.json","type":"blob","mode":"100644"}]

# 查看文件内容
curl -X GET "https://git.woa.com/api/v3/projects/rick_proto%2Fpublishing_application/repository/files?file_path=cnbot%2Foidb_test.proto&ref=master" -H "accept: *" -H "PRIVATE-TOKEN: TMoT-QP7LOf0mNoa4GWA" -H "X-XSRF-TOKEN: 3f4bd37d-1ad9-427d-be02-cb5e6c996f8a"

# 返回内容（content base64 编码）
{
  "ref": "master",
  "content": "c3ludGF4ID0Ap9IAo=",
  "encoding": "base64",
  "file_name": "oidb_test.proto",
  "file_path": "cnbot/oidb_test.proto",
  "size": 1646,
  "blob_id": "2d62e5facc49ff049cf03b3732defe66f6277af3",
  "commit_id": "3fdaca299a2ca0aec5727c6f5a4c90bbcfa142a5",
  "blob_mode": "100644",
  "blob_type": "blob",
  "submodule_git_url": null,
  "submodule_git_html_url": null
}
 */

export interface GetRepositoryProtoMeta {
  /**
   * @example commodity
   */
  name: string
  /**
   * @example commodity/act_present.proto
   */
  masterFile: string
  /**
   * @example publishing_application/commodity/commodity.proto
   */
  importName?: {
    /**
     * @example publishing_marketing/game/game
     */
    name: string
    /**
     * @example game
     */
    pbName: string
  }[]
}

export const getGitUserAccessToken = async (code: string) => {
  const res = await fetch('https://git.woa.com/oauth/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: `client_id=${process.env.GIT_CLIENT_ID}&client_secret=${process.env.GIT_CLIENT_SECRET}&code=${code}&grant_type=authorization_code&redirect_uri=${encodeURIComponent('https://proto.iegg-pa.woa.com/api/git/callback')}&expires_in=1576800000`,
  }).catch(res => {
    console.log(res)
    throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: `获取 Git 访问令牌失败: ${res}` })
  })
  try {
    const json = await res.json()
    return json as {
      access_token: string
      refresh_token: string
      expires_in: number
      token_type: string
    }
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: `获取 Git 访问令牌失败: ${await res.text()}`,
    })
  }
}
