export const testProto = `syntax = "proto3"; 
//package code generated by <PERSON>  DO NOT EDIT.
package   trpc.publishing_application.standalonesite;

//code generated by <PERSON>  DO NOT EDIT.
option  java_package="com.tencent.trpcprotocol.publishing_application.standalonesite.dynamics";
option  java_multiple_files = false; 
//java_outer_classname的命名不要与message、service、enum的命名相同
option  java_outer_classname  = "DynamicsPB"; 
//code generated by Rick  DO NOT EDIT.
option  go_package ="git.woa.com/trpcprotocol/publishing_application/standalonesite_dynamics";

// 建议使用谷歌protobuf规范 遵循PB语法检查 
// 谷歌protobuf规范地址：https://developers.google.com/protocol-buffers/docs/style
// Proto格式检查（Tencent）包含：Google protobuf规范和数据校验检查 

// 不建议使用 google/protobuf/any.proto 
// any强依赖package type.googleapis.com/_packagename_._messagename_.   
// https://developers.google.com/protocol-buffers/docs/proto3#any 

// tRPC-Go数据校验模块（**移除注释使用**） 
// tsecstr仅允许中英文字母、数字、=、+、/、@、#、_、-传入。注意，字符集不包括空格、|等符号，如有需要，请自定义校验表达式。
// 详参见规则手册：https://iwiki.woa.com/pages/viewpage.action?pageId=241919333  
import "trpc/common/validate.proto";
import "publishing_application/standalonesite/user.proto";

enum Status {
  FAILURE = 0;
  SUCCESS = 1;
}

enum PageType {
  NEXTPAGE = 0; // 查询下一页数据
  PREVIOUSPAGE = 1; // 查询上一页数据
}

message HelloReq {
} 

 
message HelloRsp { 
  	   Status status = 1;
}
message GetPostListReq {
    int64 search_type = 1;  // 搜索类型，0：默认按时间降序，1：根据tag搜索，2：根据热度搜索，3：根据关键字搜索 
    int64 plate_id = 2;
    int64 tag_id = 3;
    bool need_all_region = 4;
    string keyword = 5;
    int64 order_by = 6; // 排序类型，1：创建时间排序，2：按热度排序
    PageType page_type = 7;
    string previous_page_cursor = 8;
    string next_page_cursor = 9;
    int64 limit = 10;
    string platform = 11; // 外部平台名称
    string socialmedia_post_id = 12; //社媒外部资讯id
    int32 rank_id = 13; // creatorhub赛道id
    int32 task_id = 14; // creatorhub活动任务id
    string plate_unique_id = 15; // 板块唯一标识
}

message GetUserPostListReq {
    string intl_openid = 1 [(validate.rules).string = {ignore_empty: true, tsecstr: true}];
    PageType page_type = 2;
    string previous_page_cursor = 3;
    string next_page_cursor = 4;
    int64 limit = 5;
}

message CMSGetPostListReq {
    int64 post_type = 1;  // 查询类型，0：默认搜索，1：精华帖，2：置顶贴
    string post_uuid = 2;
    string keyword = 3;
    string intl_openid = 4;
    int64 tag_id = 5;
    int64 is_audit = 6;  // 审核状态： 1是已审核，2未审核
    PageType page_type = 7;
    string previous_page_cursor = 8;
    string next_page_cursor = 9;
    int64 limit = 10;
    string game_id = 11; // CMS的游戏id
    string area_id = 12; // CMS的游戏大区id
    int32 is_del = 13; // 是否删除状态
    string intl_user_openid = 14;//用户openid，不包含intl_gameid
    int32 is_official = 15; //是否是官方发布的帖子1-否2-是
    int32 content_status = 16; // 内容状态筛选1-正常2-沙盒中3-c端用户删除4-c端管理员删除5-b端审核删除6-b端管理删除
    string language = 17; // 语言
    string sorter = 18; // 排序字段名称+方式，不传或不符合规范则使用创建时间递减排序， 例如: "sort_field desc" "sort_field asc"
    int32 weight_range_type = 19; // 权重范围类型. 1: "小于1", 2: "等于1", 3: "大于1"
}

message CMSGetPostAuditListReq {
    int64 text_risk_level = 1;  // 文字风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
    int64 text_risk_type = 2;  // 文字风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
    int64 pic_risk_level = 3;  // 图片风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
    int64 pic_risk_type = 4;  // 图片风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
    int64 status = 5; // 审核状态 1:未处理;2:已发布;3:已忽略
    string keyword = 6;
    string intl_openid = 7;
    int64 start_time = 8;
    int64 end_time = 9;
    PageType page_type = 10;
    string previous_page_cursor = 11;
    string next_page_cursor = 12;
    int64 limit = 13;
    string game_id = 14; // CMS的游戏id
    string area_id = 15; // CMS的游戏大区id
    string intl_user_openid = 16;//用户openid，不包含intl_gameid
    int32 is_official = 17; // 是否是官方发布的帖子1-否2-是
    int32 is_del = 18; // 是否已删除0-未删除，1-已删除
    int32 machine_status = 19; //机审状态：0-未处理1-审核通过2-审核异常
    int32 artificial_status = 20; //人审状态：0-未处理1-审核通过2-审核拒绝
    int32 content_status = 21; //内容状态筛选1-正常2-沙盒中3-c端用户删除4-c端管理员删除5-b端审核删除6-b端管理删除
    string language = 22; //语言
    string post_uuid = 23; //动态id
}

message CMSGetReportListReq {
    int64 report_type = 1;  // 举报的类型：1是广告，2是涉嫌刷屏，3涉嫌低俗言论，4涉嫌虚假消息，5涉嫌游戏服务或交易，6其他
    int64 content_type = 2;  // 内容的类型：1是动态，2评论
    string content_uuid = 3;
    string title_keyword = 4;
    string content_keyword = 5;
    string reason = 6;
    string report_intl_openid = 7; // 发起举报人用户intl openid
    string reported_intl_openid = 8; // 被举报人用户intl openid
    int64 status = 9;  // 审核状态：0是未处理， 1是已忽略，2已删除
    PageType page_type = 10;
    string previous_page_cursor = 11;
    string next_page_cursor = 12;
    int64 limit = 13;
    string game_id = 14; // CMS的游戏id
    string area_id = 15; // CMS的游戏大区id
    string report_intl_user_openid = 16; // 发起举报人用户intl openid,不包含intl_gameid
    string reported_intl_user_openid = 17; // 被举报人用户intl openid,不包含intl_gameid
    string language = 18;
}

message CMSGetPostCommentAuditListReq {
    int64 text_risk_level = 1;  // 文字风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
    int64 text_risk_type = 2;  // 文字风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
    int64 pic_risk_level = 3;  // 图片风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
    int64 pic_risk_type = 4;  // 图片风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
    int64 status = 5; // 审核状态 1:未处理;2:已发布;3:已忽略
    string keyword = 6;
    string intl_openid = 7;
    int64 start_time = 8;
    int64 end_time = 9;
    PageType page_type = 10;
    string previous_page_cursor = 11;
    string next_page_cursor = 12;
    int64 limit = 13;
    string game_id = 14; // CMS的游戏id
    string area_id = 15; // CMS的游戏大区id
    string intl_user_openid = 16;//用户openid，不包含intl_gameid
    int32 is_del = 17; // 是否已删除0-未删除，1-已删除
    string language = 18; //语言字段
    int32 del_type = 19; // 删除原因
    int32 machine_status = 20; // 机审状态
    int32 artificial_status = 21; // 人审状态
    int32 content_status = 22; // 内容状态1-正常2-沙盒3-用户自删4-c端管理员删除5-b端审核删除6-b端管理删除7-b端举报删除
}

message TitleLanguageItem { 
    int64 id = 1;
    string language = 2;
    int64 title_id = 3;
    string title = 4;
    string introduce = 5;
}

message PostContent { 
    int64 id = 1;
    int64 post_id = 2;
    string content = 3;
    int32 type = 4;
    int64 sort = 5;
}

message TagInfo {
    int64 id = 1;
    string name = 2;
}

message MyUpvote {
    bool is_star = 1;
    int32 upvote_type = 2;
}

message CreatorHubActivityInfo {
    int32 id = 1;
    int32 task_id = 2;
    string task_name = 3;
}

message CreatorHubActivityRank {
    int32 id = 1;
    string rank_name = 2;
}

message GetPostRsp {
    string post_uuid = 1;
    string intl_openid = 2;
    string title = 3;
    string content = 4;
    string content_summary = 5;
    repeated string pic_urls = 6;
    float power_num = 7;
    int64 comment_count = 8;
    int64 collection_count = 9;
    int64 upvote_count = 10;
    map<int64, int64> upvote_map = 11;
    int64 browse_count = 12;
    int32 is_top = 13;
    int32 top_sort = 14;
    int64 top_on = 15;
    int32 is_essence = 16;
    int64 essence_on = 17;
    int32 type = 18; // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
    bool can_delete = 19;
    int32 is_original = 20;
    string original_url = 21;
    int32 original_reprint = 22;
    int64 latest_replied_on = 23;
    int64 created_on = 24;
    int64 modified_on = 25;
    string game_id = 26;
    string game_name = 27;
    string area_id = 28;
    string platform = 29;
    repeated TagInfo tags = 30;
    trpc.publishing_application.standalonesite.UserInfo user = 31;
    MyUpvote my_upvote = 32;
    bool is_comment = 33;
    bool is_collection = 34;
    bool is_follow = 35;
    bool is_mutual_follow = 36;
    string ext_info = 37;
    bool is_mine = 38;
    int32 forward_count =39;
    bool can_report = 40; // 提供给前端判断是否是自己的评论，如果是则不展示举报按钮
    int32 plate_id = 41;
    string language = 42;
    int32 is_audit = 43;  // 审核状态： 1是已审核，2未审核
    int32 hot_num = 44; // 热度值
    int32 is_del = 45; // 是否删除
    CreatorHubActivityRank rank_info = 46; // creatorhub作品关联的赛道信息
    CreatorHubActivityInfo task_info = 47; // creatorhub活动的信息
    string plate_name = 48; // 板块名称
    int32 publish_on = 49; //定时发布时间
    int32 is_official = 50; //是否是官方发布的帖子
    repeated CreatePostContent content_languages = 51; // 内容多语言
    bool can_edit = 52; //是否可以编辑
    int32 del_reason = 53; //删除理由
    FriendCard friend_card = 54;
    int32 del_type = 55; // 删除类型
    int64 created_on_ms = 56; // 创建的微秒时间戳
    int32 is_hide = 57; // 是否隐藏0-否1-是
    GuildCard guild_card = 58; // 公会卡片信息
    bool can_update_tags = 59; // 是否可更新话题
    int32 creator_statement_type = 60; // 创作声明类型：0 无声明，1搬运内容，2原创-不允许转载，3原创-允许转载
    int32 risk_remind_type = 61; // 风险提醒类型：0无提醒，1剧透风险，2内容风险
    int32 ai_content_type = 62; // ai内容类型：0非AI内容，1AI内容
    bool can_edit_statement = 63;// 是否可编辑创作声明？
    bool can_move = 64; //帖子是否可移动
}

message CmsPostItem {
    string post_uuid = 1;
    string intl_openid = 2;
    string title = 3;
    string content = 4;
    string content_summary = 5;
    repeated string pic_urls = 6;
    float power_num = 7;
    int64 comment_count = 8;
    int64 collection_count = 9;
    int64 upvote_count = 10;
    int64 browse_count = 12;
    int32 is_top = 13;
    int32 top_sort = 14;
    int64 top_on = 15;
    int32 is_essence = 16;
    int64 essence_on = 17;
    int32 type = 18; // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
    bool can_delete = 19;
    int64 created_on = 20;
    int64 modified_on = 21;
    string platform = 22;
    repeated TagInfo tags = 23;
    trpc.publishing_application.standalonesite.UserInfo user = 24;
    int32 forward_count =25;
    int32 plate_id = 26;
    string language = 27;
    int32 is_audit = 28;  // 审核状态： 1是已审核，2未审核
    int32 hot_num = 29; // 热度值
    int32 is_del = 30; // 是否删除
    string plate_name = 31; // 板块名称
    int32 publish_on = 32; //定时发布时间
    int32 is_official = 33; //是否是官方发布的帖子
    int32 del_reason = 34; //删除理由
    int32 del_type = 35; // 删除类型
    int32 is_hide = 36; // 是否隐藏0-否1-是
    int32 creator_statement_type = 37; // 创作声明类型：0 无声明，1搬运内容，2原创-不允许转载，3原创-允许转载
    int32 risk_remind_type = 38; // 风险提醒类型：0无提醒，1剧透风险，2内容风险
    int32 ai_content_type = 39; // ai内容类型：0非AI内容，1AI内容
}

message FriendCard {
    bool show_friend_card = 1; // 是否配置了该帖子展示加好友的游戏名片信息。ture=展示；false=不展示
    int32 icon = 2; // 头像id
    int32 player_level = 3; // 游戏等级
    string role_name = 4; // 角色名称
    string area_id = 5; // 区服id
    int32 team_combat = 6; // 战斗力
    bool is_send_friend_request = 7; // 是否已经发起过好友请求
    bool show_friend_card_detail = 8; // 是否能点击用户好友卡打开shiftypad详情页面
}

message GuildCard {
    bool show_guild_card = 1; // 是否配置了该帖子展示公会名片信息。ture=展示；false=不展示
    int32 nikke_area_id = 2; // nikke区服id 例如84表示GLOBAL, 91表示HMT
    string guild_card_uuid = 3; // 公会卡片唯一uuid
    string guild_id = 4; // 公会id
    string guild_name = 5; // 公会名称
    string guild_description = 6; // 公会描述
    int32 guild_icon = 7; // 公会icon
    int32 guild_level = 8; // 公会等级
    int32 guild_rank = 9; // 公会rank
    int32 guild_activity = 10; // 公会活跃度
    string guild_locale = 11; // 公会locale
    int32 guild_join_type = 12; // 公会join_type 0:直接加入, 1:需要申请, 2:禁止加入
    int32 guild_entry_level = 13; // 公会加入等级
}

message PostAuditInfo {
    int64 text_risk_level = 1;  // 文字风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
    int64 text_risk_type = 2;  // 文字风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
    int64 pic_risk_level = 3;  // 图片风险等级，0：未检测；1：正常；:2：高风险；3普通风险；4检测失败；
    int64 pic_risk_type = 4;  // 图片风险类别，0：不区分恶意； 100：正常；101：涉政；102：色情；103：低俗；104：广告；105：游戏拉人；106：辱骂；107：违法违规；999：其他
    int64 status = 5; // 审核状态 1:未处理;2:已发布;3:已忽略
    string audit_user = 6;
    int64 audit_on = 7;
    string audit_introduce = 8;
    int32 machine_status = 9; //机审状态：0-未处理1-审核通过2-审核异常
    int32 artificial_status = 10; //人审状态：0-未处理1-审核通过2-审核拒绝
    int32 id = 11;
}

message PageInfo {
    string previous_page_cursor = 1;
    string next_page_cursor = 2;
    bool is_finish = 3;
    int32 total = 4;
}

message GetPostListRsp {
    repeated GetPostRsp list = 1;
    PageInfo page_info = 2;
}

message GetUserPostListRsp {
    repeated GetPostRsp list = 1;
    PageInfo page_info = 2;
}

message CMSGetPostListRsp {
    repeated CmsPostItem list = 1;
    PageInfo page_info = 2;
}

message GetPostAuditForCMSRsp {
    GetPostRsp post_info = 1;
    PostAuditInfo audit_info = 2;
}

message CMSGetReportRsp {
    int64 report_type = 1;  // 举报的类型：1是广告，2是涉嫌刷屏，3涉嫌低俗言论，4涉嫌虚假消息，5涉嫌游戏服务或交易，6其他
    int64 content_type = 2;  // 内容的类型：1是动态，2评论
    int32 sub_content_type = 3; // 子内容类型：  1帖子(富文本) 2图文 3 外部平台视频动态
    string content_uuid = 4;
    string intl_openid = 5;
    string title = 6;
    string content = 7;
    string content_summary = 8;
    repeated string pic_urls = 9;
    int64 created_on = 10;
    int64 modified_on = 11;
    string game_id = 12;
    string area_id = 13;
    string platform = 14;
    repeated TagInfo tags = 15;
    string ext_info = 16;
    string reason = 17;
    string report_intl_openid = 18;
    string reported_intl_openid = 19;
    int32 status = 20; // 举报处理状态：0未处理，1已忽略，2已删除
    string update_user = 21;
    trpc.publishing_application.standalonesite.UserInfo user = 22;
    string language = 23;
}

message CMSGetReportListRsp {
    repeated CMSGetReportRsp list = 1;
    PageInfo page_info = 2;
}

message CMSGetPostAuditListRsp {
    repeated GetPostAuditForCMSRsp list = 1;
    PageInfo page_info = 2;
}

message GetPostReq {
    string post_uuid = 1 [(validate.rules).string.tsecstr = true];
    int32 is_all_language = 2 [(validate.rules).int32 = {ignore_empty: true, in: [0, 1]}]; //是否需要返回多语言
}

message GetTagReq {
    int32 id = 1;
}

message GetTagListReq {
    int32 plate_id = 1;
    PageType page_type = 2;
    string previous_page_cursor = 3;
    string next_page_cursor = 4;
    int64 limit = 5;
}

message GetTagRsp {
    int64 id = 1;
    int32 type = 2;
    string pic_url = 3;
    string tag_name = 4;
    int64 comment_num = 5;
    int64 quote_num = 6;
    int64 read_num = 7;
    int64 fans_num = 8;
    int64 hot_num = 9;
    int64 power_num = 10;
    string game_id = 11;
    string area_id = 12;
    int64 kudos_num = 13;
    int64 recommend_deadline = 14;
    int64 post_num = 15;
}

message GetTagListRsp {
    repeated GetTagRsp list = 1;
    PageInfo page_info = 2;
}

message TagReloadReq {}
message TagReloadRsp {}

message SecurityCheckReq {
    repeated string urls = 1;
    int64 url_scene_id = 2;
    string text = 3;
    int64 text_scene_id = 4;
}

message SecurityCheckRsp {
    map<string, int64> result = 1;
}
message PostPushCacheReq {
    int64 id = 1;
}
message PostPushCacheRsp {}

message PostContentItem { 
    string content = 1;
    int32 type = 2;
    int64 sort = 3;
}

message CreatePostReq {
    int32 plate_id = 1;
    string language = 2;
    int32 type = 3; // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
    uint32 visibility = 4;
    string title = 5;
    string content = 6;
    string platform = 7; // 视频类型动态时填写。社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
    repeated string pic_urls = 8;
    repeated int64 tags = 9;
    int32 is_original = 10;
    string original_url = 11;
    int32 original_reprint = 12;
    string ext_info = 13; //扩展字段，json字符串
    string content_summary = 14; // 富文本文字汇总，只有富文本类型才会消费这个字段
}

message CreatePostRsp {
    GetPostRsp post_data = 1;
}

message CreatePostContent {
    string title = 1;
    string content = 2;
    string platform = 3; // 视频类型动态时填写。社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
    repeated string pic_urls = 4;
    int32 is_original = 5;
    string original_url = 6;
    int32 original_reprint = 7;
    string ext_info = 8; //扩展字段，json字符串
    string content_summary = 9; // 富文本文字汇总，只有富文本类型才会消费这个字段
    string language = 10; //语言
    int32 id = 11;
    int32 order = 12; // 排序值
}

message CreatePostNewReq {
    int32 plate_id = 1 [(validate.rules).int32.gte = 1];
    repeated CreatePostContent contents = 2 [(validate.rules).repeated = {min_items: 1, max_items: 6}]; // 发布多语言内容列表
    int32 type = 3 [(validate.rules).int32.gte = 1]; // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
    uint32 visibility = 4;
    repeated int64 tags = 5;
    int32 publish_on = 6; //定时发布时间
    bool need_friend_card = 7; //是否配置了该帖子展示加好友的游戏名片信息。ture=展示；false=不展示
    int32 from = 8; // 发布帖子的来源：0=nikke独立站本站；1=nikke-上线推广h5活动
    int64 ch_work_id= 9; // creatorhub 同步的帖子
    bool need_guild_card = 10; //是否配置了该帖子展示公会名片信息。ture=展示；false=不展示
    int32 creator_statement_type = 11; // 创作声明类型：0 无声明，1搬运内容，2原创-不允许转载，3原创-允许转载
    int32 risk_remind_type = 12; // 风险提醒类型：0无提醒，1剧透风险，2内容风险
    int32 ai_content_type = 13; // ai内容类型：0非AI内容，1AI内容
    string original_url = 14; // 
}

message UpdatePostReq {
    int32 plate_id = 1 [(validate.rules).int32.gte = 1];
    repeated CreatePostContent contents = 2 [(validate.rules).repeated = {min_items: 1, max_items: 6}]; // 发布多语言内容列表
    int32 type = 3 [(validate.rules).int32.gte = 1]; // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
    uint32 visibility = 4;
    repeated int64 tags = 5;
    int32 publish_on = 6; //定时发布时间
    string post_uuid = 7 [(validate.rules).string.tsecstr = true];
    int32 need_refresh_friend_card = 8; // 是否需要更新好友卡，0=不变更；1=需要更新为最新保存角色数据；2=删除好友卡
    int32 need_refresh_guild_card = 9; // 是否需要更新公会卡，0=不变更；1=需要更新为最新公会数据；2=删除公会卡
    int32 creator_statement_type = 10; // 创作声明类型：0 无声明，1搬运内容，2原创-不允许转载，3原创-允许转载
    int32 risk_remind_type = 11; // 风险提醒类型：0无提醒，1剧透风险，2内容风险
    int32 ai_content_type = 12; // ai内容类型：0非AI内容，1AI内容
    string original_url = 13; // 
}

message UpdatePostRsp {
    GetPostRsp post_data = 1;
}

message DeletePostReq {
    string post_uuid = 1 [(validate.rules).string.tsecstr = true];
    int32 del_reason = 2; // 删除原因
}

message DeletePostRsp {}

message CMSReviewPostReq {
    repeated string post_uuids = 1;
    int32 type = 2; // CMS审批Post类型，1审核通过，2审核不通过，3CMS删除动态，4CMS动态加精，5CMS取消动态加精，6CMS动态置顶，7CMS取消动态置顶，8CMS忽略动态举报, 9 CMS删除举报动态, 10 CMS修改权重
    string update_user = 3;
    int64 days = 4; // 生效天数
    int64 sort = 5; // 排序值
    string audit_introduce = 6; // 审批备注
    int64 update_value = 7; // 对应类型下的修改的值
    int32 del_reason = 8; // 删除原因
    repeated int64 post_audit_ids = 9;
}

message CMSReviewPostCommentReq {
    repeated string comment_uuids = 1;
    int32 type = 2; // CMS审批动态评论类型，1审核通过，2审核不通过，3CMS删除评论，4CMS忽略评论举报
    string update_user = 3;
    string audit_introduce = 4; // 审批备注
    int32 del_reason = 5; //删除原因
    
}

message CMSReviewPostRsp {}

message CMSReviewPostCommentRsp {}

message GetPostStarReq {
    int64 id = 1;
}
message GetPostStarRsp {
    Status Status = 1;
}

message PostStarReq {
    string post_uuid = 1 [(validate.rules).string.tsecstr = true];
    int64 type = 2;
    int32 like_type = 3; // 点赞位置1-点赞2-表态点赞
}
message PostStarRsp {
   bool Status = 1; 
}

message PostBrowseReq {
    string post_uuid = 1 [(validate.rules).string.tsecstr = true];
}

message PostBrowseRsp {
    Status Status = 1; 
}

message PostForwardReq {
    string post_uuid = 1 [(validate.rules).string.tsecstr = true];
}

message PostForwardRsp {
    int32 forward_count = 1; 
}

message GetPostCollectionReq {
    int64 id = 1;
}

message GetPostCollectionRsp {
    Status Status = 1; 
}

message PostCollectionReq {
    string post_uuid = 1 [(validate.rules).string.tsecstr = true];
}

message PostCollectionRsp {
    bool Status = 1; 
}

message TagCollectionReq {
    int64 id = 1;
}

message TagCollectionRsp {
    Status Status = 1; 
}

message LockPostReq {
    int64 id = 1;
}

message LockPostRsp {
    int32 lock_status = 1;
}

message VisiblePostReq {
    int64 id = 1;
    int32 visibility = 2;
}

message VisiblePostRsp {
    int32 visibility = 1;
}

message GetGamesReq {
    int32 limit = 1;
    int32 offset= 2;
}
message GameLanguageFormated { 
    int64 id = 1;
    int32 game_t_id = 2;
    string language = 3;
    string name = 4;
    string introduce = 5;
}
message GetGameRsp {
    int64 id = 1;
    GameLanguageFormated language = 2;
    string game_id = 3;
    string en_abbreviation = 4;
    string avatar = 5;
    string bg_image_pc = 6;
    string bg_image_h5 = 7;
    string poster_image_pc = 8;
    string poster_image_h5 = 9;
    int64 user_number = 10;
    int64 hot_num = 11;
    string qrcode = 12; 
}

message GetGamesRsp {
    repeated GetGameRsp games = 1;
}

message GetGameReq {
    string game_id = 1;
}

message ContentReportReq {
    string content_uuid = 1 [(validate.rules).string.tsecstr = true]; // 举报内容id，包括动态id、动态评论id、评论回复的id
    int32 content_type = 2; // 举报的内容类型：1是动态，2是评论，3是评论回复
    int32 report_type = 3; // 举报的类型：1是广告，2是涉嫌刷屏，3涉嫌低俗言论，4涉嫌虚假消息，5涉嫌游戏服务或交易，6其他
    string reason = 4;
}

message ContentReportRsp {}

message GetPostCommentReq{
  string comment_uuid = 1 [(validate.rules).string.tsecstr = true];
}

message GetPostCommentRsp {
  CommentItem comment = 1;
}

message GetPostCommentsReq {
    string post_uuid = 1 [(validate.rules).string.tsecstr = true];
    PageType page_type = 2;
    string previous_page_cursor = 3;
    string next_page_cursor = 4;
    int64 limit = 5;
    int32 comment_reply_limit = 6; //每条评论的第一页回复的条数
    int32 order_by = 7; // 排序类型，0：创建时间排序（默认值），1：创建时间排序，2：按热度排序
}

message CommentContent {
  int64 comment_id = 1;
  string intl_openid = 2;
  string content = 3;
  int32 type = 4; //类型，1标题，2文字段落，3图片地址，4视频地址，5语音地址，6链接地址，7附件资源，8收费资源，9富文本文字段落
  int64 sort = 5;
}

message RepliesItem {
  string comment_uuid = 1;
  string post_uuid = 2;
  string reply_uuid = 3;
  string intl_openid = 4;
  int32 is_audit = 5;
  bool is_star = 6;
  int32 is_parent_del = 7;
  string at_intl_openid = 8;
  string game_id = 9;
  string area_id = 10;
  string title = 11;
  string content = 12;
  repeated string pic_urls = 13;
  int64 upvote_count = 14;
  int64 created_on = 15;
  int64 modified_on = 16;
  bool can_delete = 17; // 提供给前端判断是否是自己的评论，如果是则不展示删除按钮
  bool can_report = 18; // 提供给前端判断是否是自己的评论，如果是则不展示举报按钮
  bool is_mine = 19;
  bool is_author = 20;
  trpc.publishing_application.standalonesite.UserInfo user = 21;
  trpc.publishing_application.standalonesite.UserInfo at_user = 22;
}

message Replies {
    repeated RepliesItem data_list = 1;
    PageInfo page_info = 2;
}

message CommentItem {
  string comment_uuid = 1;
  string post_uuid = 2;
  string intl_openid = 3;
  string title = 4;
  string content = 5;
  repeated string pic_urls = 6;
  int64 upvote_count = 7;
  string game_id = 8;
  string area_id = 9;
  bool is_audit = 10; // 是否已审核 1是 2不是
  bool is_star = 11; // 是否已点赞 1是 2不是
  int64 created_on = 12;
  int64 modified_on = 13;
  bool can_delete = 14; // 提供给前端判断是否是自己的评论，如果是则不展示删除按钮
  bool can_report = 15; // 提供给前端判断是否是自己的评论，如果是则不展示举报按钮
  bool is_mine = 16;
  bool is_author = 17;
  trpc.publishing_application.standalonesite.UserInfo user = 18;
  Replies replies = 19;
  string at_intl_openid = 20;
  trpc.publishing_application.standalonesite.UserInfo at_user = 21;
  int64 comment_bubble_id = 22; //评论气泡id
  string language = 23; //语言字段
  bool can_top = 24; // 可置顶
  bool can_bottom = 25; // 可置底
  string top_bottom_status = 26; // 置顶置底状态：'unset' | 'top' | 'bottom'
}

message GetPostCommentsRsp {
    repeated CommentItem list = 1;
    PageInfo page_info = 2;
}

message GetPostCommentRepliesReq {
    string comment_uuid = 1 [(validate.rules).string.tsecstr = true];
    PageType page_type = 2;
    string previous_page_cursor = 3;
    string next_page_cursor = 4;
    int64 limit = 5;
}

message GetPostCommentRepliesRsp {
  repeated RepliesItem list = 1;
    PageInfo page_info = 2;
}

message GetUserCommentListReq {
    string intl_openid = 1 [(validate.rules).string = {ignore_empty: true, tsecstr: true}];
    PageType page_type = 2;
    string previous_page_cursor = 3;
    string next_page_cursor = 4;
    int64 limit = 5;
}

// 查看他人评论的结构体
message UserCommentItem {
  string comment_uuid = 1;
  string post_uuid = 2;
  string intl_openid = 3;
  int32 type = 4;
  string title = 5;
  string content = 6;
  repeated string pic_urls = 7;
  int64 upvote_count = 8;
  string game_id = 9;
  string area_id = 10;
  int64 created_on = 11;
  int64 modified_on = 12;
  string post_context = 13;
  string comment_context = 14;
  bool can_delete = 15;
  bool is_mine = 16;
  bool can_report = 17; // 提供给前端判断是否是自己的评论，如果是则不展示举报按钮
  bool is_author = 18;
  trpc.publishing_application.standalonesite.UserInfo user = 19;
  string post_title = 20;
  string reply_uuid = 21; // 回复的评论id
  string reply_to_reply_uuid = 22; // 回复的回复的id 
  int32 comment_del = 23; // 评论、回复的删除0-未删1-删除
  int32 post_del = 24; // 帖子的删除0-未删1-删除
  int32 plate_id = 25;
  string plate_name = 26; // 板块名称
  int32 is_del = 27; // 是否删除状态
  string language = 28; //语言字段
  int32 del_type = 29; //删除类型
  int32 del_reason = 30; // 删除原因
  int32  pos_status = 31; // 评论位置状态 1:置顶, 2:置底, 3:取消置顶或取消置底
  int64  pos_set_time = 32; // 置顶或置底秒级时间戳
}

message GetUserCommentListRsp {
  repeated UserCommentItem list = 1;
    PageInfo page_info = 2;
}

message GetPostCommentAuditForCMSRsp {
    UserCommentItem comment_item = 1;
    PostAuditInfo audit_info = 2;
}

message CMSGetPostCommentAuditListRsp {
    repeated GetPostCommentAuditForCMSRsp list = 1;
    PageInfo page_info = 2;
}

message CommentStarReq {
  string comment_uuid = 1 [(validate.rules).string.tsecstr = true];
}

message CommentStarRsp {
  Status status = 1;
}
message CommentReplyStarReq {
  int64 comment_reply_id = 1;
}
message CommentReplyStarRsp {
  Status status = 1;
}

message PostCommentReq {
  string post_uuid = 1 [(validate.rules).string.tsecstr = true];
  string comment_uuid = 2;
  int32 type = 3;
  string content = 4;
  repeated string pic_urls = 5;
  string at_intl_openid = 6;
  int64 comment_bubble_id=7; // 评论气泡id
}

message PostCommentRsp {
  CommentItem comment = 1;
}

message DeletePostCommentReq {
  string comment_uuid = 1 [(validate.rules).string.tsecstr = true];
  int32 del_reason = 2; //删除评论原因
}
message DeletePostCommentRsp {

}

message PostCommentReplyReq {
    int64  comment_id = 1;
    string content = 2;
    string at_intl_openid = 3;
    int64 reply_id = 4;
}

message PostCommentReplyRsp {
  RepliesItem replies = 1;
}

message DeletePostCommentReplyReq {
  int64 id = 1;
}

message DeletePostCommentReplyRsp {

}

message SyncNewsByMediaReq {
    
}

message SyncNewsByMediaRsp {
    
}

message GetCosStsReq {
    
}

message GetCosStsRsp {
    string app_id = 1;
    string bucket = 2;
    string region = 3;
    string cdn_domain = 4;
    string cloud_type = 5;
    string tmp_secret_id = 6;
    string tmp_secret_key = 7;
    string token = 8;
    string expiration = 9;
    int64 start_time = 10;
    int64 expired_time = 11;
    string bucket_domain = 12;
}

message GetUserPostCollectionListReq {
    string intl_openid = 1 [(validate.rules).string.tsecstr = true];
    PageType page_type = 2;
    string previous_page_cursor = 3;
    string next_page_cursor = 4;
    int64 limit = 5;
}

message GetUserPostCollectionListRsp {
    repeated GetPostRsp list = 1;
    PageInfo page_info = 2;
}

message GetPlateListReq {
    PageType page_type = 2;
    string previous_page_cursor = 3;
    string next_page_cursor = 4;
    int64 limit = 5;
}

message PlateItem {
    int32 id = 1;
    int32 type = 2;
    string language_data = 3;
    int32 status = 4;
    int32 order = 5;
    string game_id = 6;
    string area_id = 7;
    string plate_name = 8;
    string unique_identifier = 9;
}

message GetPlateListRsp {
    repeated PlateItem list = 1;
    PageInfo page_info = 2;
}

message GetVideoInfoByURLReq {
    string video_url = 1 ;
    string platform = 2;
}

message GetVideoInfoByURLRsp {
    string video_id = 1;
    string video_title = 2;
    string video_desc = 3;
    string video_cover = 4;
    string platform = 5;
}

message EmoticonIconItem {
    int64 id = 1;
    string name = 2;
    string pic_url = 3;
}

message EmoticonGroupItem {
    int64 id = 1;
    string name = 2;
    string pic_url = 3;
    repeated EmoticonIconItem icon_list = 4;
}

message GetDistrictListReq {
    PageType page_type = 2;
    string previous_page_cursor = 3;
    string next_page_cursor = 4;
    int64 limit = 5;
}

message DistrictItem {
    string tool_name = 1;
    string icon = 2;
    string jump_url = 3;
    int64 order = 4;
    string ext_info = 5; //CMS配置额外字段
}
message GetDistrictListRsp {
    repeated DistrictItem list = 1;
    PageInfo page_info = 2;
}

message GetAllEmoticonsReq {}

message GetAllEmoticonsRsp {
    repeated EmoticonGroupItem list = 1;
    PageInfo page_info = 2;
}

message GetPostShareHtmlReq {
    string post_uuid = 1 [(validate.rules).string.tsecstr = true];
}

message GetPostShareHtmlRsp {
    string html_content = 1;
}

message GetTagShareHtmlReq {
    int32 tag_id = 1;
    string language = 2;
}

message GetTagShareHtmlRsp {
    string html_content = 1;
}

message GetRewardsShareHtmlReq {
    string language = 1;
}

message GetRewardsShareHtmlRsp {
    string html_content = 1;
}

message MovePostReq {
    int32 plate_id = 1;
    string post_uuid = 2;
    string language = 3;
}

message MovePostRsp {
    
}

message SyncESDataReq {
    
}

message SyncESDataRsp {
    
}

message HotPostEventTickerReq {
    
}

message HotPostEventTickerRsp {
    
}

message HotCommentEventTickerReq {
    
}

message HotCommentEventTickerRsp {
    
}

message GetCreatorHubTaskListReq {
    PageType page_type = 2;
    string previous_page_cursor = 3;
    string next_page_cursor = 4;
    int64 limit = 5;
}

message GetCreatorHubRankItem {
    int32 id = 1;
    string rank_name = 2; 
}

message GetCreatorHubTaskItem {
    int32 id = 1;
    int32 task_id = 2;
    int32 publish_time = 3;
    string image_url = 4;
    int32 task_status = 5;
    string task_name = 6;
    repeated GetCreatorHubRankItem ranks = 7;
    string task_page_url = 8;
}

message GetCreatorHubTaskListRsp {
    repeated GetCreatorHubTaskItem list = 1;
    PageInfo page_info = 2;
}

message TranslateContentReq {
    string content_uuid = 1 [(validate.rules).string.tsecstr = true];
    int32 type = 2;
    string language = 3;
}

message TranslateContentRsp {
    string title = 1;
    string content = 2;
}

message SiteMessageTickerReq {}

message SiteMessageTickerRsp {}

message SyncCreatorHubActivityTaskReq {
    string game_id = 1;
    string area_id = 2;
}

message SyncCreatorHubActivityTaskRsp {}

message SyncCreatorHubActivityWorkReq {
    string game_id = 1;
    string area_id = 2;
}

message SearchTagReq {
    string tag_name = 1;
    PageType page_type = 2;
    string previous_page_cursor = 3;
    string next_page_cursor = 4;
    int64 limit = 5;
}

message SearchTagRsp {
    repeated GetTagRsp list = 1;
    PageInfo page_info = 2;
}

message SyncCreatorHubActivityWorkRsp {}

message SyncUserIntlOpenidReq {}

message SyncUserIntlOpenidRsp {}

message SyncCreatedOnMsReq {}

message SyncCreatedOnMsRsp {}

message SyncBaseDataReq{
    int32 type = 1; //区分类型，具体看代码
    string params = 2; // 入参
}

message SyncBaseDataRsp {}

message OfficialPublishPostTimerReq {}

message OfficialPublishPostTimerRsp {}

message SendFriendRequestReq {
    string post_uuid = 1 [(validate.rules).string.tsecstr = true];
}

message SendFriendRequestRsp {
    
}

message CommentBubbleItem {
    int64 id = 1;
    string comment_bubble = 2;
    string bg_color = 3;
    
}

message GetAllCommentBubbleListReq {
}

message GetAllCommentBubbleListRsp {
    repeated CommentBubbleItem comment_bubbles = 1;
}

message SyncSiteMessageStatReq {}

message SyncSiteMessageStatRsp {}

message PostMachineReviewReq{
    string game_id = 1; //游戏id
    string area_id = 2; //大区id
}

message PostMachineReviewRsp{}

message CommentContentMachineReviewTimerReq {}

message CommentContentMachineReviewTimerRsp {}

message UserInfoMachineReviewTimerReq {}

message UserInfoMachineReviewTimerRsp {}

message CMSUpdateBaseReq{}
message CMSUpdateBaseRsp{}

message GetFollowTaskOfficialAccountsReq{
}
message GetFollowTaskOfficialAccountsRsp{
    repeated trpc.publishing_application.standalonesite.UserCollectionItem users = 1;
}

message QuicklyFollowAllOfficialAccountsReq{}
message QuicklyFollowAllOfficialAccountsRsp{}

message HasFollowedOfficialAccountsReq{}
message HasFollowedOfficialAccountsRsp{}

message CheckIfUserHasPostedByNIKKEH5PageReq{
    string intl_openid = 1 [(validate.rules).string.tsecstr = true];
}

message CheckIfUserHasPostedByNIKKEH5PageRsp{
    bool status = 1; // 是否有发布：false没有，true有
}

// cms 查询评论列表信息
message CMSGetPostCommentsReq {
    string   game_id              = 1;   // CMS的游戏id
    string   area_id              = 2;   // CMS的游戏大区id
    string   intl_user_openid     = 3;   // 用户openid，不包含intl_gameid
    string   comment_uuid         = 4;   // 评论uuid
    string   post_uuid            = 5;   // 帖子uuid
    string   keyword              = 6;   // 评论内容搜索
    int64    start_time           = 7;   // 评论创建开始时间 
    int64    end_time             = 8;   // 评论创建结束时间
    int32    comment_type         = 9;   // 评论类型 1: 评论;2:回复
    int32    content_status       = 10;  // 内容状态 1-正常，2-沙盒，3-用户自删，4-c端管理员删除 ，5-b端审核删除，6-b端评论管理删除，7-b端举报删除
    int32    is_del               = 11;  // 是否已删除0-未删除，1-已删除
    PageType page_type            = 12;
    string   previous_page_cursor = 13;
    string   next_page_cursor     = 14;
    int64    limit                = 15;
    int32    pos_status           = 16;  // 评论位置状态 1:置顶, 2:置底  
    string   language             = 17;  // 语言
}

message CMSGetPostCommentsRsp {
    repeated GetPostCommentAuditForCMSRsp commments = 1;  // 评论信息列表
    PageInfo page_info                              = 2;
    string   trace_id                               = 3;  // 请求id
}

// cms 设置评论位置信息
message CMSSetCommentPositionInfoReq {
    string game_id      = 1; // CMS的游戏id
    string area_id      = 2; // CMS的游戏大区id
    string comment_uuid = 3; // 评论uuid
    int32  pos_status   = 4; // 评论位置状态 1:置顶, 2:置底, 3:取消置顶或取消置底
}

message CMSSetCommentPositionInfoRsp {
    string trace_id = 1; // 请求id
}

message CreatorHubWorkMediaInfo {
    int32 channel_type = 1;
    string media_id = 2;
    string media_type = 3;
    string original_url = 4;
    string preview_image_url = 5;
}

message WorkContent {
    string uid = 1;
    int64 work_id = 2;
    int64 work_publish_time = 3;
}

message CreatorHubWorkItem {
    CreatePostContent post_content = 1;
    WorkContent work_content = 2;
    int32 is_published_post = 3; // 是否发布到独立站post
    int64 tag_id = 4;  // creator 话题id
}

message GetMySubmissionReq {
    int32 limit = 1;
    int32 next_idx =2;
}

message GetMySubmissionRsp{
    repeated CreatorHubWorkItem list = 1;
    int32 next_idx = 2;
}

message CreatorHubTaskItem {
    int64 end_time  = 1;
    string image_url  = 2;
    string lip_gameid  = 3;
    string name  = 4;
    int64 publish_time  = 5;
    int32 task_model_type  = 6;
    int32 task_status = 7;
    int32 task_type = 8;
    string thumbnail_url = 9;
    int32 is_published = 10;
    int64 start_time = 11;
    string task_url = 12;//活动链接
}

message GetRecentTasksReq{
    int32 limit = 1;
    int32 offset = 2;
}

message GetRecentTasksRsp{
    repeated CreatorHubTaskItem task_list = 1;
    int32 next_offset = 2;
}

message SetCommentTopOrBottomReq {
    string comment_uuid = 1;
    string  top_bottom_status = 2 [(validate.rules).string = {in: ["unset", "top", "bottom"]}] ;
}

message SetCommentTopOrBottomRsp {
    
}

message PostChangeTagBindReq {
    string post_uuid = 1 [(validate.rules).string.tsecstr = true];
    repeated int64 tag_ids = 2;
}

message PostChangeTagBindRsp {}

message UpdatePostTagsReq{
    string post_uuid = 1;
    repeated int64 tags = 2;
}
message UpdatePostTagsRsp{}

message GetGuildHotPostReq {
    string guild_id = 1 [(validate.rules).string.tsecstr = true];
    int64 nikke_area_id = 2;
}

message GetGuildHotPostRsp {
    GetPostRsp post_info = 1;
}


message UpdateStatementReq{
    string post_uuid = 1;
    int32 creator_statement_type = 2; // 创作声明类型：0 无声明，1搬运内容，2原创-不允许转载，3原创-允许转载
    int32 risk_remind_type = 3; // 风险提醒类型：0无提醒，1剧透风险，2内容风险
    int32 ai_content_type = 4; // ai内容类型：0非AI内容，1AI内容
    string original_url = 5;
}

message UpdateStatementRsp {
}

message GetActivityPostTagsReq{
}

message GetActivityPostTagsRsp {
    repeated ActivityPostTag tags = 1;
}

message ActivityPostTag {
    string tag_code = 1;
    int32 tag_id = 2;
}

service Dynamics { 
      rpc SayHello (HelloReq) returns (HelloRsp);
      rpc GetPostList (GetPostListReq) returns (GetPostListRsp); // 获取文章列表
      rpc GetPost (GetPostReq) returns (GetPostRsp); //获取单个文章
      rpc GetTagList (GetTagListReq) returns (GetTagListRsp); //获取话题列表
      rpc GetTag (GetTagReq) returns (GetTagRsp); //获取话题详情
      rpc TagReload (TagReloadReq) returns (TagReloadRsp); //手动触发tag到db
      rpc SecurityCheck (SecurityCheckReq) returns (SecurityCheckRsp); // 手动触发机审校验
      rpc PostPushCache (PostPushCacheReq) returns (PostPushCacheRsp); // 资讯设置到es中
      rpc CreatePost (CreatePostReq) returns (CreatePostRsp); // 创建动态
      rpc DeletePost (DeletePostReq) returns (DeletePostRsp); // 删除动态
      rpc GetPostStar (GetPostStarReq) returns (GetPostStarRsp); // 获取动态点赞状态
      rpc PostStar (PostStarReq) returns (PostStarRsp); // 动态点赞操作
      rpc PostBrowse (PostBrowseReq) returns (PostBrowseRsp); //动态浏览上报操作
      rpc PostForward (PostForwardReq) returns (PostForwardRsp); //动态浏览上报操作
      rpc GetPostCollection (GetPostCollectionReq) returns (GetPostCollectionRsp); //获取动态收藏状态
      rpc PostCollection (PostCollectionReq) returns (PostCollectionRsp); //收藏动态操作
      rpc TagCollection (TagCollectionReq) returns (TagCollectionRsp); // 话题关注
      rpc LockPost (LockPostReq) returns (LockPostRsp); //锁定动态
      rpc VisiblePost (VisiblePostReq) returns (VisiblePostRsp); //修改动态可见度
      rpc GetGames(GetGamesReq) returns (GetGamesRsp); //获取游戏列表
      rpc GetGame(GetGameReq) returns (GetGameRsp); //获取游戏详情
      rpc ContentReport (ContentReportReq) returns (ContentReportRsp); //内容举报，包括动态、评论、评论回复内容的举报
      rpc GetPostComment(GetPostCommentReq) returns (GetPostCommentRsp);//获取评论列表
      rpc GetPostComments(GetPostCommentsReq) returns (GetPostCommentsRsp);//获取评论列表
      rpc GetPostCommentReplies (GetPostCommentRepliesReq) returns (GetPostCommentRepliesRsp);//获取评论回复列表
      rpc CommentStar(CommentStarReq) returns (CommentStarRsp);//评论点赞
      rpc CommentReplyStar (CommentReplyStarReq) returns (CommentReplyStarRsp);//评论回复点赞
      rpc PostComment(PostCommentReq) returns (PostCommentRsp);//发布评论
      rpc DeletePostComment (DeletePostCommentReq) returns (DeletePostCommentRsp); // 删除评论
      rpc PostCommentReply (PostCommentReplyReq) returns (PostCommentReplyRsp); // 发布评论的回复
      rpc DeletePostCommentReply (DeletePostCommentReplyReq) returns (DeletePostCommentReplyRsp); //删除评论的回复
      rpc GetCosSts (GetCosStsReq) returns (GetCosStsRsp); // 获取COS临时秘钥
      rpc GetPlateList (GetPlateListReq) returns (GetPlateListRsp); //获取板块配置信息
      rpc GetVideoInfoByURL (GetVideoInfoByURLReq) returns (GetVideoInfoByURLRsp); // 通过视频链接获取视频详情信息
      rpc GetAllEmoticons (GetAllEmoticonsReq) returns (GetAllEmoticonsRsp); // 查询全量表情信息
      rpc GetDistrictList (GetDistrictListReq) returns (GetDistrictListRsp); //获取金刚区配置列表
      rpc GetPostShareHtml (GetPostShareHtmlReq) returns (GetPostShareHtmlRsp); // 用于前端动态分享，社媒爬虫拉取数据用
      rpc GetTagShareHtml (GetTagShareHtmlReq) returns (GetTagShareHtmlRsp); // 用于前端话题分享，社媒爬虫拉取数据用
      rpc GetRewardsShareHtml (GetRewardsShareHtmlReq) returns (GetRewardsShareHtmlRsp); // 用于前端话题分享，社媒爬虫拉取数据用
      rpc MovePost (MovePostReq) returns (MovePostRsp); //迁移帖子
      rpc GetCreatorHubTaskList (GetCreatorHubTaskListReq) returns (GetCreatorHubTaskListRsp); //获取网红活动任务的列表
      rpc TranslateContent (TranslateContentReq) returns (TranslateContentRsp); // 翻译动态、评论、回复文字内容
      rpc CreatePostNew (CreatePostNewReq) returns (CreatePostRsp); // 创建动态
      rpc UpdatePost (UpdatePostReq) returns (UpdatePostRsp); //更新动态
      rpc SearchTag (SearchTagReq) returns (SearchTagRsp); // 根据话题名称搜索话题列表
      rpc SendFriendRequest (SendFriendRequestReq) returns (SendFriendRequestRsp); // 发起NIKKE添加好友请求
      rpc CheckIfUserHasPostedByNIKKEH5Page (CheckIfUserHasPostedByNIKKEH5PageReq) returns (CheckIfUserHasPostedByNIKKEH5PageRsp); // 查看用户是否有通过nikke独立站推广H5活动页面发布帖子
      rpc PostChangeTagBind (PostChangeTagBindReq) returns (PostChangeTagBindRsp); // 帖子更换话题绑定
      rpc GetGuildHotPost (GetGuildHotPostReq) returns (GetGuildHotPostRsp); // 获取公会下热度最高的帖子
      rpc GetActivityPostTags (GetActivityPostTagsReq) returns (GetActivityPostTagsRsp); // 获取活动帖子默认追加的话题
      
      rpc GetUserPostList (GetUserPostListReq) returns (GetUserPostListRsp); // 获取用户文章列表
      rpc GetUserCommentList(GetUserCommentListReq) returns (GetUserCommentListRsp);//获取用户评论
      rpc GetUserPostCollectionList (GetUserPostCollectionListReq) returns (GetUserPostCollectionListRsp); // 获取用户动态收藏列表
      
      rpc CMSGetPostList (CMSGetPostListReq) returns (CMSGetPostListRsp); //提供给CMS管理端，查询动态列表
      rpc CMSGetPostAuditList (CMSGetPostAuditListReq) returns (CMSGetPostAuditListRsp); //提供给CMS管理端，查询动态审核列表
      rpc CMSGetReportList (CMSGetReportListReq) returns (CMSGetReportListRsp); //提供给CMS管理端，查询动态、评论的举报列表
      rpc CMSReviewPost (CMSReviewPostReq) returns (CMSReviewPostRsp); //提供给CMS管理端操作动态（包括：审核通过，审核不通过，动态加精，动态置顶，删除动态，忽略举报） 
      rpc CMSGetPostCommentAuditList (CMSGetPostCommentAuditListReq) returns (CMSGetPostCommentAuditListRsp); //提供给CMS管理端，查询评论审核列表
      rpc CMSReviewPostComment (CMSReviewPostCommentReq) returns (CMSReviewPostCommentRsp); //提供给CMS管理端操作评论（包括：审核通过，审核不通过，动态加精，动态置顶，删除动态，忽略举报） 
      // cms 查询评论列表信息
      rpc CMSGetPostComments (CMSGetPostCommentsReq) returns (CMSGetPostCommentsRsp);
      // cms 设置评论位置信息
      rpc CMSSetCommentPositionInfo (CMSSetCommentPositionInfoReq) returns (CMSSetCommentPositionInfoRsp);


      rpc SyncESData (SyncESDataReq) returns (SyncESDataRsp); // 全量重新同步动态ES数据
      rpc SyncUserESData (SyncESDataReq) returns (SyncESDataRsp); // 全量重新同步用户ES数据
      rpc SyncNewsByMedia (SyncNewsByMediaReq) returns (SyncNewsByMediaRsp); // 同步社媒的资讯
      rpc HotPostEventTicker (HotPostEventTickerReq) returns (HotPostEventTickerRsp); // 定时任务触发动态的热度计算
      rpc HotCommentEventTicker (HotCommentEventTickerReq) returns (HotCommentEventTickerRsp); // 定时任务触发评论的热度计算
      rpc SiteMessageTicker (SiteMessageTickerReq) returns (SiteMessageTickerRsp); // 定时任务触发站内信逻辑
      rpc SyncCreatorHubActivityTask (SyncCreatorHubActivityTaskReq) returns (SyncCreatorHubActivityTaskRsp); //同步creatorhub 获取网红活动任务
      rpc SyncCreatorHubActivityWork (SyncCreatorHubActivityWorkReq) returns (SyncCreatorHubActivityWorkRsp); //同步creatorhub 获取网红活动作品
      rpc SyncUserIntlOpenid (SyncUserIntlOpenidReq) returns (SyncUserIntlOpenidRsp); // 全量更新用户intl_openid2
      rpc SyncCreatedOnMs (SyncCreatedOnMsReq) returns (SyncCreatedOnMsRsp); // 全量更新动态、评论的微秒
      rpc SyncBaseData (SyncBaseDataReq) returns (SyncBaseDataRsp); // 基础同步数据接口，用类型区分不同同步逻辑
      rpc OfficialPublishPostTimer (OfficialPublishPostTimerReq) returns (OfficialPublishPostTimerRsp); // 官方发布帖子定时任务
      rpc GetAllCommentBubbleList (GetAllCommentBubbleListReq) returns (GetAllCommentBubbleListRsp); // 获取全量的评论气泡、包括已删除气泡
      rpc SyncSiteMessageStatTicker (SyncSiteMessageStatReq) returns (SyncSiteMessageStatRsp); // 定时任务触发站内信数据统计
      rpc PostMachineReviewTicker (PostMachineReviewReq) returns (PostCommentReplyRsp); //定时任务触发帖子机器审核
      rpc CommentContentMachineReviewTimer (CommentContentMachineReviewTimerReq) returns (CommentContentMachineReviewTimerRsp); // 评论机审定时任务
      rpc UserInfoMachineReviewTimer (UserInfoMachineReviewTimerReq) returns (UserInfoMachineReviewTimerRsp); // cms用户信息送入机审定时任务
      
      rpc CMSUpdatePlateListCache (CMSUpdateBaseReq) returns (CMSUpdateBaseRsp); // CMS 通知更新板块
      rpc CMSUpdateDistrictListCache (CMSUpdateBaseReq) returns (CMSUpdateBaseRsp); // CMS 通知金刚区板块
      rpc CMSUpdateTagListCache (CMSUpdateBaseReq) returns (CMSUpdateBaseRsp); // CMS 通知话题
      rpc CMSUpdateEmoticonsCache (CMSUpdateBaseReq) returns (CMSUpdateBaseRsp); // CMS 通知标签
      
      rpc GetFollowTaskOfficialAccounts(GetFollowTaskOfficialAccountsReq) returns (GetFollowTaskOfficialAccountsRsp);
      rpc QuicklyFollowAllOfficialAccounts(QuicklyFollowAllOfficialAccountsReq) returns (QuicklyFollowAllOfficialAccountsRsp);
      rpc HasFollowedOfficialAccounts(HasFollowedOfficialAccountsReq) returns (HasFollowedOfficialAccountsRsp);
      
        rpc GetMySubmission(GetMySubmissionReq) returns (GetMySubmissionRsp); //creatorhub 提交作品
      rpc GetRecentTasks(GetRecentTasksReq) returns (GetRecentTasksRsp); //最近活动
      rpc SetCommentTopOrBottom (SetCommentTopOrBottomReq) returns (SetCommentTopOrBottomRsp); // 设置置顶置底
      rpc UpdatePostTags (UpdatePostTagsReq) returns (UpdatePostTagsRsp); // 更新话题
      rpc UpdateStatement (UpdateStatementReq) returns (UpdateStatementRsp);// 更新创作声明
      
}`
