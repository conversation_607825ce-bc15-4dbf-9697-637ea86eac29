import cron from 'node-cron'
import { prisma } from '../prisma'
import { GitService } from '../services/repository'
import dayjs from 'dayjs'
import { updateProjectFilesWithHistories } from '../routes/project'
import { Logger } from '../utils/logger'

export const syncProjectProtoContent = async () => {
  const projects = await prisma.project.findMany({
    include: {
      createdByUser: { select: { username: true, chineseName: true, gitAccessToken: true } },
    },
  })

  for (const project of projects) {
    try {
      Logger.info(`开始更新项目 ${project.repository} ${project.name} 的文件和历史`)
      await updateProjectFilesWithHistories({
        creator: project.createdByUser,
        project: project.name,
        projectId: project.id,
        repository: project.repository,
      })
      Logger.success(`更新项目 ${project.repository} ${project.name} 的文件和历史成功`)
    } catch (error) {
      Logger.error(`更新项目 ${project.repository} ${project.name} 的文件和历史失败`)
      console.error(error)
    }
  }
}

// 每两分钟执行一次，每天 9-23 点执行，周一到周五
cron.schedule('*/20 9-23 * * 1-5', async () => {
  Logger.info(`开始更新所有项目文件和历史`)
  await syncProjectProtoContent()
  Logger.success(`更新所有项目文件和历史成功`)
})
