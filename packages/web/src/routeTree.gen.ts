/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as SettingsImport } from './routes/settings'
import { Route as ApiDocsImport } from './routes/api-docs'
import { Route as ProjectIdImport } from './routes/$projectId'
import { Route as IndexImport } from './routes/index'
import { Route as TemplatesIndexImport } from './routes/templates.index'
import { Route as ProjectsIndexImport } from './routes/projects.index'
import { Route as GitCallbackImport } from './routes/git.callback'
import { Route as ProjectIdSectionImport } from './routes/$projectId.$section'
import { Route as TemplatesNewScriptImport } from './routes/templates.new.script'
import { Route as TemplatesNewAiImport } from './routes/templates.new.ai'
import { Route as ProtosPbIdSectionImport } from './routes/protos.$pbId.$section'
import { Route as TemplatesDetailScriptIdImport } from './routes/templates.detail.script.$id'
import { Route as TemplatesDetailAiIdImport } from './routes/templates.detail.ai.$id'
import { Route as ProjectIdApiServiceMethodImport } from './routes/$projectId.api.$service.$method'

// Create/Update Routes

const SettingsRoute = SettingsImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRoute,
} as any)

const ApiDocsRoute = ApiDocsImport.update({
  id: '/api-docs',
  path: '/api-docs',
  getParentRoute: () => rootRoute,
} as any)

const ProjectIdRoute = ProjectIdImport.update({
  id: '/$projectId',
  path: '/$projectId',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const TemplatesIndexRoute = TemplatesIndexImport.update({
  id: '/templates/',
  path: '/templates/',
  getParentRoute: () => rootRoute,
} as any)

const ProjectsIndexRoute = ProjectsIndexImport.update({
  id: '/projects/',
  path: '/projects/',
  getParentRoute: () => rootRoute,
} as any)

const GitCallbackRoute = GitCallbackImport.update({
  id: '/git/callback',
  path: '/git/callback',
  getParentRoute: () => rootRoute,
} as any)

const ProjectIdSectionRoute = ProjectIdSectionImport.update({
  id: '/$section',
  path: '/$section',
  getParentRoute: () => ProjectIdRoute,
} as any)

const TemplatesNewScriptRoute = TemplatesNewScriptImport.update({
  id: '/templates/new/script',
  path: '/templates/new/script',
  getParentRoute: () => rootRoute,
} as any)

const TemplatesNewAiRoute = TemplatesNewAiImport.update({
  id: '/templates/new/ai',
  path: '/templates/new/ai',
  getParentRoute: () => rootRoute,
} as any)

const ProtosPbIdSectionRoute = ProtosPbIdSectionImport.update({
  id: '/protos/$pbId/$section',
  path: '/protos/$pbId/$section',
  getParentRoute: () => rootRoute,
} as any)

const TemplatesDetailScriptIdRoute = TemplatesDetailScriptIdImport.update({
  id: '/templates/detail/script/$id',
  path: '/templates/detail/script/$id',
  getParentRoute: () => rootRoute,
} as any)

const TemplatesDetailAiIdRoute = TemplatesDetailAiIdImport.update({
  id: '/templates/detail/ai/$id',
  path: '/templates/detail/ai/$id',
  getParentRoute: () => rootRoute,
} as any)

const ProjectIdApiServiceMethodRoute = ProjectIdApiServiceMethodImport.update({
  id: '/api/$service/$method',
  path: '/api/$service/$method',
  getParentRoute: () => ProjectIdRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/$projectId': {
      id: '/$projectId'
      path: '/$projectId'
      fullPath: '/$projectId'
      preLoaderRoute: typeof ProjectIdImport
      parentRoute: typeof rootRoute
    }
    '/api-docs': {
      id: '/api-docs'
      path: '/api-docs'
      fullPath: '/api-docs'
      preLoaderRoute: typeof ApiDocsImport
      parentRoute: typeof rootRoute
    }
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsImport
      parentRoute: typeof rootRoute
    }
    '/$projectId/$section': {
      id: '/$projectId/$section'
      path: '/$section'
      fullPath: '/$projectId/$section'
      preLoaderRoute: typeof ProjectIdSectionImport
      parentRoute: typeof ProjectIdImport
    }
    '/git/callback': {
      id: '/git/callback'
      path: '/git/callback'
      fullPath: '/git/callback'
      preLoaderRoute: typeof GitCallbackImport
      parentRoute: typeof rootRoute
    }
    '/projects/': {
      id: '/projects/'
      path: '/projects'
      fullPath: '/projects'
      preLoaderRoute: typeof ProjectsIndexImport
      parentRoute: typeof rootRoute
    }
    '/templates/': {
      id: '/templates/'
      path: '/templates'
      fullPath: '/templates'
      preLoaderRoute: typeof TemplatesIndexImport
      parentRoute: typeof rootRoute
    }
    '/protos/$pbId/$section': {
      id: '/protos/$pbId/$section'
      path: '/protos/$pbId/$section'
      fullPath: '/protos/$pbId/$section'
      preLoaderRoute: typeof ProtosPbIdSectionImport
      parentRoute: typeof rootRoute
    }
    '/templates/new/ai': {
      id: '/templates/new/ai'
      path: '/templates/new/ai'
      fullPath: '/templates/new/ai'
      preLoaderRoute: typeof TemplatesNewAiImport
      parentRoute: typeof rootRoute
    }
    '/templates/new/script': {
      id: '/templates/new/script'
      path: '/templates/new/script'
      fullPath: '/templates/new/script'
      preLoaderRoute: typeof TemplatesNewScriptImport
      parentRoute: typeof rootRoute
    }
    '/$projectId/api/$service/$method': {
      id: '/$projectId/api/$service/$method'
      path: '/api/$service/$method'
      fullPath: '/$projectId/api/$service/$method'
      preLoaderRoute: typeof ProjectIdApiServiceMethodImport
      parentRoute: typeof ProjectIdImport
    }
    '/templates/detail/ai/$id': {
      id: '/templates/detail/ai/$id'
      path: '/templates/detail/ai/$id'
      fullPath: '/templates/detail/ai/$id'
      preLoaderRoute: typeof TemplatesDetailAiIdImport
      parentRoute: typeof rootRoute
    }
    '/templates/detail/script/$id': {
      id: '/templates/detail/script/$id'
      path: '/templates/detail/script/$id'
      fullPath: '/templates/detail/script/$id'
      preLoaderRoute: typeof TemplatesDetailScriptIdImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

interface ProjectIdRouteChildren {
  ProjectIdSectionRoute: typeof ProjectIdSectionRoute
  ProjectIdApiServiceMethodRoute: typeof ProjectIdApiServiceMethodRoute
}

const ProjectIdRouteChildren: ProjectIdRouteChildren = {
  ProjectIdSectionRoute: ProjectIdSectionRoute,
  ProjectIdApiServiceMethodRoute: ProjectIdApiServiceMethodRoute,
}

const ProjectIdRouteWithChildren = ProjectIdRoute._addFileChildren(
  ProjectIdRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/$projectId': typeof ProjectIdRouteWithChildren
  '/api-docs': typeof ApiDocsRoute
  '/settings': typeof SettingsRoute
  '/$projectId/$section': typeof ProjectIdSectionRoute
  '/git/callback': typeof GitCallbackRoute
  '/projects': typeof ProjectsIndexRoute
  '/templates': typeof TemplatesIndexRoute
  '/protos/$pbId/$section': typeof ProtosPbIdSectionRoute
  '/templates/new/ai': typeof TemplatesNewAiRoute
  '/templates/new/script': typeof TemplatesNewScriptRoute
  '/$projectId/api/$service/$method': typeof ProjectIdApiServiceMethodRoute
  '/templates/detail/ai/$id': typeof TemplatesDetailAiIdRoute
  '/templates/detail/script/$id': typeof TemplatesDetailScriptIdRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/$projectId': typeof ProjectIdRouteWithChildren
  '/api-docs': typeof ApiDocsRoute
  '/settings': typeof SettingsRoute
  '/$projectId/$section': typeof ProjectIdSectionRoute
  '/git/callback': typeof GitCallbackRoute
  '/projects': typeof ProjectsIndexRoute
  '/templates': typeof TemplatesIndexRoute
  '/protos/$pbId/$section': typeof ProtosPbIdSectionRoute
  '/templates/new/ai': typeof TemplatesNewAiRoute
  '/templates/new/script': typeof TemplatesNewScriptRoute
  '/$projectId/api/$service/$method': typeof ProjectIdApiServiceMethodRoute
  '/templates/detail/ai/$id': typeof TemplatesDetailAiIdRoute
  '/templates/detail/script/$id': typeof TemplatesDetailScriptIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/$projectId': typeof ProjectIdRouteWithChildren
  '/api-docs': typeof ApiDocsRoute
  '/settings': typeof SettingsRoute
  '/$projectId/$section': typeof ProjectIdSectionRoute
  '/git/callback': typeof GitCallbackRoute
  '/projects/': typeof ProjectsIndexRoute
  '/templates/': typeof TemplatesIndexRoute
  '/protos/$pbId/$section': typeof ProtosPbIdSectionRoute
  '/templates/new/ai': typeof TemplatesNewAiRoute
  '/templates/new/script': typeof TemplatesNewScriptRoute
  '/$projectId/api/$service/$method': typeof ProjectIdApiServiceMethodRoute
  '/templates/detail/ai/$id': typeof TemplatesDetailAiIdRoute
  '/templates/detail/script/$id': typeof TemplatesDetailScriptIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/$projectId'
    | '/api-docs'
    | '/settings'
    | '/$projectId/$section'
    | '/git/callback'
    | '/projects'
    | '/templates'
    | '/protos/$pbId/$section'
    | '/templates/new/ai'
    | '/templates/new/script'
    | '/$projectId/api/$service/$method'
    | '/templates/detail/ai/$id'
    | '/templates/detail/script/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/$projectId'
    | '/api-docs'
    | '/settings'
    | '/$projectId/$section'
    | '/git/callback'
    | '/projects'
    | '/templates'
    | '/protos/$pbId/$section'
    | '/templates/new/ai'
    | '/templates/new/script'
    | '/$projectId/api/$service/$method'
    | '/templates/detail/ai/$id'
    | '/templates/detail/script/$id'
  id:
    | '__root__'
    | '/'
    | '/$projectId'
    | '/api-docs'
    | '/settings'
    | '/$projectId/$section'
    | '/git/callback'
    | '/projects/'
    | '/templates/'
    | '/protos/$pbId/$section'
    | '/templates/new/ai'
    | '/templates/new/script'
    | '/$projectId/api/$service/$method'
    | '/templates/detail/ai/$id'
    | '/templates/detail/script/$id'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  ProjectIdRoute: typeof ProjectIdRouteWithChildren
  ApiDocsRoute: typeof ApiDocsRoute
  SettingsRoute: typeof SettingsRoute
  GitCallbackRoute: typeof GitCallbackRoute
  ProjectsIndexRoute: typeof ProjectsIndexRoute
  TemplatesIndexRoute: typeof TemplatesIndexRoute
  ProtosPbIdSectionRoute: typeof ProtosPbIdSectionRoute
  TemplatesNewAiRoute: typeof TemplatesNewAiRoute
  TemplatesNewScriptRoute: typeof TemplatesNewScriptRoute
  TemplatesDetailAiIdRoute: typeof TemplatesDetailAiIdRoute
  TemplatesDetailScriptIdRoute: typeof TemplatesDetailScriptIdRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  ProjectIdRoute: ProjectIdRouteWithChildren,
  ApiDocsRoute: ApiDocsRoute,
  SettingsRoute: SettingsRoute,
  GitCallbackRoute: GitCallbackRoute,
  ProjectsIndexRoute: ProjectsIndexRoute,
  TemplatesIndexRoute: TemplatesIndexRoute,
  ProtosPbIdSectionRoute: ProtosPbIdSectionRoute,
  TemplatesNewAiRoute: TemplatesNewAiRoute,
  TemplatesNewScriptRoute: TemplatesNewScriptRoute,
  TemplatesDetailAiIdRoute: TemplatesDetailAiIdRoute,
  TemplatesDetailScriptIdRoute: TemplatesDetailScriptIdRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/$projectId",
        "/api-docs",
        "/settings",
        "/git/callback",
        "/projects/",
        "/templates/",
        "/protos/$pbId/$section",
        "/templates/new/ai",
        "/templates/new/script",
        "/templates/detail/ai/$id",
        "/templates/detail/script/$id"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/$projectId": {
      "filePath": "$projectId.tsx",
      "children": [
        "/$projectId/$section",
        "/$projectId/api/$service/$method"
      ]
    },
    "/api-docs": {
      "filePath": "api-docs.tsx"
    },
    "/settings": {
      "filePath": "settings.tsx"
    },
    "/$projectId/$section": {
      "filePath": "$projectId.$section.tsx",
      "parent": "/$projectId"
    },
    "/git/callback": {
      "filePath": "git.callback.tsx"
    },
    "/projects/": {
      "filePath": "projects.index.tsx"
    },
    "/templates/": {
      "filePath": "templates.index.tsx"
    },
    "/protos/$pbId/$section": {
      "filePath": "protos.$pbId.$section.tsx"
    },
    "/templates/new/ai": {
      "filePath": "templates.new.ai.tsx"
    },
    "/templates/new/script": {
      "filePath": "templates.new.script.tsx"
    },
    "/$projectId/api/$service/$method": {
      "filePath": "$projectId.api.$service.$method.tsx",
      "parent": "/$projectId"
    },
    "/templates/detail/ai/$id": {
      "filePath": "templates.detail.ai.$id.tsx"
    },
    "/templates/detail/script/$id": {
      "filePath": "templates.detail.script.$id.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
