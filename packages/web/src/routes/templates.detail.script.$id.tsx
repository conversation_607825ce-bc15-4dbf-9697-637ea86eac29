import { createFileRoute, useNavigate } from '@tanstack/react-router'
import {
  Button,
  Container,
  Group,
  Stack,
  Title,
  Text,
  Paper,
  Badge,
  Loader,
  Alert,
  Divider,
  ActionIcon,
  Card,
  Tabs,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import {
  IconAlertCircle,
  IconArrowLeft,
  IconEdit,
  IconTrash,
  IconBrandJavascript,
  IconBrandTypescript,
} from '@tabler/icons-react'
import { useState } from 'react'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useModal } from '@/utils/modal'
import { ScriptTemplateModal } from '@/components/template/ScriptTemplateModal'
import { modals } from '@mantine/modals'
import { CodeHighlight } from '@mantine/code-highlight'
import { ProjectBindingManager } from '@/components/template/ProjectBindingManager'

export const Route = createFileRoute('/templates/detail/script/$id')({
  component: ScriptTemplateDetailPage,
})

function ScriptTemplateDetailPage() {
  const navigate = useNavigate()
  const params = Route.useParams()
  const templateId = parseInt(params.id)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<string | null>('script')

  // 获取模板详情
  const {
    data: template,
    isLoading,
    refetch,
  } = useQuery(
    trpc.template.script.getById.queryOptions(
      { id: templateId },
      { enabled: !isNaN(templateId), retry: false },
    ),
  )

  // 获取项目列表
  const { data: projects = [] } = useQuery(trpc.project.list.queryOptions())

  // 删除模板的 mutation
  const deleteTemplate = useMutation(trpc.template.script.delete.mutationOptions())

  // 使用 useModal 创建编辑模板弹框
  const editModal = useModal(ScriptTemplateModal)

  // 处理编辑模板
  const handleEdit = () => {
    if (!template) return

    editModal.openModal({
      mode: 'edit',
      templateId: template.id,
      initialValues: {
        title: template.title,
        script: template.script,
        description: template.desc || '',
        auth: template.auth,
        lang: template.lang,
        optimizePrompt: template.optimizePrompt || '',
        projectIds: [],
      },
      onFinish: () => {
        refetch()
        notifications.show({
          title: '更新成功',
          message: '模板已成功更新',
          color: 'green',
        })
      },
    })
  }

  // 处理删除模板
  const handleDelete = () => {
    if (!template) return

    modals.openConfirmModal({
      title: '确认删除',
      children: (
        <Text size="sm">
          确定要删除模板 <strong>{template.title}</strong> 吗？此操作不可撤销。
        </Text>
      ),
      labels: { confirm: '删除', cancel: '取消' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          await deleteTemplate.mutateAsync({ id: template.id })
          notifications.show({
            title: '删除成功',
            message: '模板已成功删除',
            color: 'green',
          })
          navigate({ to: '/templates', search: { tab: 'script' } })
        } catch (error: any) {
          notifications.show({
            title: '删除失败',
            message: error.message || '删除模板失败，请重试',
            color: 'red',
          })
        }
      },
    })
  }

  // 获取语言图标
  const getLangIcon = (lang: string) => {
    switch (lang) {
      case 'TS':
        return <IconBrandTypescript size={16} />
      case 'JS':
        return <IconBrandJavascript size={16} />
      default:
        return null
    }
  }

  if (isLoading) {
    return (
      <Container size="lg" py="xl">
        <Stack align="center" py="xl">
          <Loader size="lg" />
          <Text>加载模板详情...</Text>
        </Stack>
      </Container>
    )
  }

  if (!template) {
    return (
      <Container size="lg" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="未找到模板"
          color="red"
          variant="filled"
        >
          找不到ID为 {templateId} 的模板，可能已被删除或您没有访问权限。
        </Alert>
        <Button
          mt="md"
          variant="subtle"
          leftSection={<IconArrowLeft size={16} />}
          onClick={() => navigate({ to: '/templates', search: { tab: 'script' } })}
        >
          返回模板列表
        </Button>
      </Container>
    )
  }

  return (
    <Container size="lg" py="xl">
      <Stack gap="xl">
        <Group>
          <Button
            size="md"
            variant="subtle"
            leftSection={<IconArrowLeft size={16} />}
            onClick={() => navigate({ to: '/templates', search: { tab: 'script' } })}
          >
            返回模板列表
          </Button>
        </Group>

        {error && (
          <Alert icon={<IconAlertCircle size="1rem" />} title="错误" color="red">
            {error}
          </Alert>
        )}

        <Paper shadow="xs" p="md" withBorder>
          <Stack gap="md">
            <Group justify="space-between">
              <Title order={2}>{template.title}</Title>
              <Group>
                <ActionIcon variant="light" color="blue" onClick={handleEdit}>
                  <IconEdit size={18} />
                </ActionIcon>
                <ActionIcon variant="light" color="red" onClick={handleDelete}>
                  <IconTrash size={18} />
                </ActionIcon>
              </Group>
            </Group>

            <Group>
              <Badge color={template.auth === 'PRIVATE' ? 'gray' : 'blue'}>
                {template.auth === 'PRIVATE'
                  ? '私有'
                  : template.auth === 'PUBLIC'
                    ? '公开'
                    : '开放'}
              </Badge>
              <Badge leftSection={getLangIcon(template.lang)}>{template.lang}</Badge>
              <Text size="sm" c="dimmed">
                创建者: {template.createdBy}
              </Text>
              <Text size="sm" c="dimmed">
                创建于{' '}
                {formatDistanceToNow(new Date(template.createdAt), {
                  addSuffix: true,
                  locale: zhCN,
                })}
              </Text>
            </Group>

            {template.desc && (
              <Text size="sm" c="dimmed">
                {template.desc}
              </Text>
            )}

            <Divider />

            <ProjectBindingManager
              templateId={templateId}
              templateType="script"
              onUpdate={() => refetch()}
            />

            <Divider />

            <Tabs value={activeTab} onChange={setActiveTab}>
              <Tabs.List>
                <Tabs.Tab value="script">脚本代码</Tabs.Tab>
                {template.optimizePrompt && <Tabs.Tab value="optimize">AI优化提示词</Tabs.Tab>}
              </Tabs.List>

              <Tabs.Panel value="script" pt="md">
                <Title order={4}>脚本内容</Title>
                <Card withBorder>
                  <CodeHighlight
                    code={template.script}
                    language={template.lang === 'TS' ? 'typescript' : 'javascript'}
                  />
                </Card>
              </Tabs.Panel>

              {template.optimizePrompt && (
                <Tabs.Panel value="optimize" pt="md">
                  <Title order={4}>AI 优化提示词</Title>
                  <Card withBorder>
                    <CodeHighlight code={template.optimizePrompt} language="markdown" />
                  </Card>
                </Tabs.Panel>
              )}
            </Tabs>
          </Stack>
        </Paper>
      </Stack>
    </Container>
  )
}
