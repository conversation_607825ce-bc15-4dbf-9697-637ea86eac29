import { createFileRoute } from '@tanstack/react-router'
import {
  Button,
  Card,
  Container,
  Group,
  Loader,
  Stack,
  Text,
  Title,
  Badge,
  Grid,
  Divider,
  Paper,
  Box,
  ThemeIcon,
  rem,
  Accordion,
} from '@mantine/core'
import { useQuery } from '@tanstack/react-query'
import { trpc, type ApiData } from '@/utils/trpc'
import { Link } from '@tanstack/react-router'
import {
  IconPlus,
  IconFolder,
  IconFileCode,
  IconFolderFilled,
  IconBriefcase,
  IconFolders,
  IconLock,
  IconShield,
} from '@tabler/icons-react'
import { useModal } from '@/utils/modal'
import { ProjectFormModal } from '@/components/project/ProjectFormModal'
import { ProjectPermissionModal } from '@/components/project/ProjectPermissionModal'
import { useMemo } from 'react'
import { UserAvatar } from '@/components/common/UserAvatar'

export const Route = createFileRoute('/projects/')({
  component: ProjectListPage,
})

function ProjectListPage() {
  // 获取项目列表
  const { data: projects, isLoading, refetch } = useQuery(trpc.project.list.queryOptions())

  // 使用 useModal 创建项目弹窗
  const projectFormModal = useModal(ProjectFormModal)

  // 使用 useModal 创建权限申请弹窗
  const permissionModal = useModal(ProjectPermissionModal)

  // 按 repositoryInfo.group 分组项目
  const groupedProjects = useMemo(() => {
    if (!projects || projects.length === 0) return {}

    // 创建分组对象
    const groups: Record<string, typeof projects> = {}

    // 对项目进行分组
    projects.forEach(project => {
      const group = project.repository || '未分组'
      if (!groups[group]) {
        groups[group] = []
      }
      groups[group].push(project)
    })

    return groups
  }, [projects])

  // 处理项目卡片点击事件
  const handleProjectClick = (
    project: ApiData['project']['list'][number],
    event: React.MouseEvent,
  ) => {
    // 如果用户有权限，正常跳转
    if (project.canView) {
      return
    }

    // 如果没有权限，阻止默认跳转并显示权限申请弹窗
    event.preventDefault()
    event.stopPropagation()

    permissionModal.openModal({
      projectName: project.name,
      projectTitle: project.title || '',
      repository: project.repository,
      members: project.members || [],
    })
  }

  return (
    <Container size="xl" py="xl">
      <Stack gap="xl">
        <Group justify="space-between" align="center">
          <Group>
            <ThemeIcon size="xl" radius="xl" color="blue" variant="light">
              <IconBriefcase style={{ width: rem(24), height: rem(24) }} />
            </ThemeIcon>
            <Title order={2}>我的项目</Title>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() =>
              projectFormModal.openModal({
                mode: 'create',
                onFinish: () => refetch(),
              })
            }
          >
            导入新项目
          </Button>
        </Group>

        <Divider />

        {isLoading ? (
          <Group justify="center" p="xl">
            <Loader size="md" />
          </Group>
        ) : (
          <>
            {projects && projects.length > 0 ? (
              <Accordion multiple defaultValue={Object.keys(groupedProjects)} variant="separated">
                {Object.entries(groupedProjects).map(([group, groupProjects]) => {
                  const hasPermissionCount = groupProjects.filter(p => p.canView).length
                  const totalCount = groupProjects.length

                  return (
                    <Accordion.Item key={group} value={group}>
                      <Accordion.Control>
                        <Group justify="space-between" wrap="nowrap" mr="md">
                          <Group gap="sm">
                            <ThemeIcon size="md" radius="md" color="indigo" variant="light">
                              <IconFolders style={{ width: rem(16), height: rem(16) }} />
                            </ThemeIcon>
                            <Title order={4}>{group}</Title>
                          </Group>
                          <Group gap="xs">
                            <Badge variant="light" color="blue" size="sm">
                              {totalCount} 个项目
                            </Badge>
                          </Group>
                        </Group>
                      </Accordion.Control>
                      <Accordion.Panel>
                        <Grid>
                          {groupProjects.map(project => (
                            <Grid.Col key={project.id} span={{ base: 12, sm: 6, md: 4 }}>
                              <Card
                                shadow="sm"
                                padding="sm"
                                radius="md"
                                withBorder
                                component={(project.canView ? Link : 'div') as any}
                                {...(project.canView ? { to: `/${project.id}/api` } : {})}
                                onClick={(event: React.MouseEvent) =>
                                  handleProjectClick(project, event)
                                }
                                style={{
                                  textDecoration: 'none',
                                  color: 'inherit',
                                  height: '100%',
                                  display: 'flex',
                                  flexDirection: 'column',
                                  cursor: project.canView ? 'pointer' : 'not-allowed',
                                  opacity: project.canView ? 1 : 0.7,
                                }}
                              >
                                <Stack gap="md" style={{ flex: 1 }}>
                                  <Group justify="space-between" wrap="nowrap" className="w-full">
                                    <Group gap="sm" wrap="nowrap" className="w-full">
                                      <ThemeIcon
                                        size="md"
                                        radius="xl"
                                        color={project.canView ? 'blue' : 'gray'}
                                        variant="light"
                                      >
                                        {project.canView ? (
                                          <IconFolder style={{ width: rem(16), height: rem(16) }} />
                                        ) : (
                                          <IconLock style={{ width: rem(16), height: rem(16) }} />
                                        )}
                                      </ThemeIcon>
                                      <Title order={5} lineClamp={1} className="break-all">
                                        {project.title || project.name}
                                      </Title>
                                    </Group>
                                    {!project.canView && (
                                      <ThemeIcon size="sm" color="orange" variant="light">
                                        <IconShield style={{ width: rem(12), height: rem(12) }} />
                                      </ThemeIcon>
                                    )}
                                  </Group>

                                  <Group mt="auto" gap="xs" justify="space-between">
                                    <Badge
                                      leftSection={<IconFileCode size={12} />}
                                      size="sm"
                                      variant="light"
                                      radius="md"
                                      color={project.canView ? 'blue' : 'gray'}
                                    >
                                      {project.files?.length || 0} 个 BP 文件
                                    </Badge>
                                    <Group gap="xs">
                                      {!project.canView && (
                                        <Badge size="xs" color="orange" variant="light">
                                          无权限
                                        </Badge>
                                      )}
                                      <UserAvatar username={project.createdBy} size={24} />
                                    </Group>
                                  </Group>
                                </Stack>
                              </Card>
                            </Grid.Col>
                          ))}
                        </Grid>
                      </Accordion.Panel>
                    </Accordion.Item>
                  )
                })}
              </Accordion>
            ) : (
              <Paper shadow="sm" p="xl" radius="md" withBorder>
                <Stack align="center" gap="md">
                  <ThemeIcon size="xl" radius="xl" color="gray" variant="light">
                    <IconFolder style={{ width: rem(32), height: rem(32) }} opacity={0.8} />
                  </ThemeIcon>
                  <Title order={3}>暂无项目</Title>
                  <Text ta="center" c="dimmed">
                    您还没有创建或加入任何项目，点击"创建新项目"按钮开始使用。
                  </Text>
                  <Button
                    leftSection={<IconPlus size={16} />}
                    onClick={() =>
                      projectFormModal.openModal({
                        mode: 'create',
                        onFinish: () => refetch(),
                      })
                    }
                    mt="md"
                    variant="light"
                  >
                    创建新项目
                  </Button>
                </Stack>
              </Paper>
            )}
          </>
        )}
      </Stack>
    </Container>
  )
}
