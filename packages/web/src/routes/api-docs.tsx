import { createFileRoute } from '@tanstack/react-router'
import {
  Container,
  Stack,
  Title,
  Paper,
  Text,
  Badge,
  Group,
  Box,
  Accordion,
  Code,
  Alert,
  ThemeIcon,
  rem,
  Divider,
  Button,
  CopyButton,
  ActionIcon,
  Tooltip,
} from '@mantine/core'
import { IconApi, IconInfoCircle, IconCopy, IconCheck, IconExternalLink } from '@tabler/icons-react'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'

export const Route = createFileRoute('/api-docs')({
  component: ApiDocsPage,
})

interface ApiProcedure {
  inputSchema: {
    type: string
    properties: Record<string, any>
    required?: string[]
    additionalProperties: boolean
    $schema: string
  }
  nodeType: 'procedure'
  procedureType: 'query' | 'mutation'
  pathFromRootRouter: string[]
  extraData: {
    parameterDescriptions: Record<string, string>
  }
}

interface ApiRouter {
  children: Record<string, ApiProcedure | ApiRouter>
  nodeType: 'router'
  path: string[]
}

interface ApiDocsData {
  children: Record<string, ApiRouter>
  nodeType: 'router'
  path: string[]
}

function ApiDocsPage() {
  const [expandedSections, setExpandedSections] = useState<string[]>([])

  // 获取API文档数据
  const {
    data: apiDocs,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['api-docs'],
    queryFn: async (): Promise<ApiDocsData> => {
      const response = await fetch('/trpc/docs')
      if (!response.ok) {
        throw new Error('Failed to fetch API docs')
      }
      return response.json()
    },
  })

  const renderInputSchema = (schema: ApiProcedure['inputSchema']) => {
    if (!schema.properties || Object.keys(schema.properties).length === 0) {
      return (
        <Text size="sm" c="dimmed">
          无参数
        </Text>
      )
    }

    return (
      <Stack gap="xs">
        {Object.entries(schema.properties).map(([key, value]) => (
          <Group key={key} gap="xs" wrap="nowrap">
            <Code size="sm" style={{ minWidth: 'fit-content' }}>
              {key}
            </Code>
            <Badge size="xs" variant="light" color={value.type === 'string' ? 'blue' : 'green'}>
              {value.type}
            </Badge>
            {schema.required?.includes(key) && (
              <Badge size="xs" color="red" variant="light">
                必填
              </Badge>
            )}
            {value.description && (
              <Text size="xs" c="dimmed" style={{ flex: 1 }}>
                {value.description}
              </Text>
            )}
          </Group>
        ))}
      </Stack>
    )
  }

  const generateCurlExample = (procedure: ApiProcedure) => {
    const path = procedure.pathFromRootRouter.join('.')
    const hasParams =
      procedure.inputSchema.properties && Object.keys(procedure.inputSchema.properties).length > 0

    let body = '{"0":{"json":{}}}'
    if (hasParams) {
      const exampleParams: Record<string, any> = {}
      Object.entries(procedure.inputSchema.properties).forEach(([key, value]) => {
        switch (value.type) {
          case 'string':
            exampleParams[key] = 'example_string'
            break
          case 'number':
            exampleParams[key] = 123
            break
          case 'boolean':
            exampleParams[key] = true
            break
          default:
            exampleParams[key] = 'example_value'
        }
      })
      body = JSON.stringify({ '0': { json: exampleParams } }, null, 2)
    }

    return `curl -X POST "${window.location.origin}/api/trpc/${path}" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_TOKEN" \\
  -d '${body}'`
  }

  const renderProcedure = (name: string, procedure: ApiProcedure) => {
    const path = procedure.pathFromRootRouter.join('.')
    const isQuery = procedure.procedureType === 'query'
    const curlExample = generateCurlExample(procedure)

    return (
      <Paper key={path} p="md" withBorder radius="md">
        <Stack gap="sm">
          <Group justify="space-between" align="flex-start">
            <Box>
              <Group gap="xs" mb="xs">
                <Text fw={500} size="sm">
                  {name}
                </Text>
                <Badge size="xs" color={isQuery ? 'blue' : 'orange'} variant="light">
                  {isQuery ? 'Query' : 'Mutation'}
                </Badge>
              </Group>
              <Code size="xs" c="dimmed">
                {path}
              </Code>
            </Box>
            <CopyButton value={curlExample}>
              {({ copied, copy }) => (
                <Tooltip label={copied ? '已复制curl命令' : '复制curl命令'}>
                  <ActionIcon color={copied ? 'teal' : 'gray'} variant="subtle" onClick={copy}>
                    {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
          </Group>

          <Box>
            <Text size="sm" fw={500} mb="xs">
              请求参数：
            </Text>
            {renderInputSchema(procedure.inputSchema)}
          </Box>

          <Accordion variant="contained" size="xs">
            <Accordion.Item value="curl-example">
              <Accordion.Control>
                <Text size="sm" fw={500}>
                  调用示例
                </Text>
              </Accordion.Control>
              <Accordion.Panel>
                <Code block size="xs" style={{ whiteSpace: 'pre-wrap' }}>
                  {curlExample}
                </Code>
              </Accordion.Panel>
            </Accordion.Item>
          </Accordion>
        </Stack>
      </Paper>
    )
  }

  const renderRouter = (name: string, router: ApiRouter) => {
    const procedures: Array<[string, ApiProcedure]> = []
    const subRouters: Array<[string, ApiRouter]> = []

    Object.entries(router.children).forEach(([key, value]) => {
      if (value.nodeType === 'procedure') {
        procedures.push([key, value as ApiProcedure])
      } else {
        subRouters.push([key, value as ApiRouter])
      }
    })

    return (
      <Accordion.Item key={name} value={name}>
        <Accordion.Control>
          <Group>
            <Text fw={500}>{name}</Text>
            <Badge size="xs" variant="light">
              {procedures.length} 个接口
            </Badge>
          </Group>
        </Accordion.Control>
        <Accordion.Panel>
          <Stack gap="md">
            {procedures.map(([procName, procedure]) => renderProcedure(procName, procedure))}
            {subRouters.map(([routerName, subRouter]) => renderRouter(routerName, subRouter))}
          </Stack>
        </Accordion.Panel>
      </Accordion.Item>
    )
  }

  if (isLoading) {
    return (
      <Container size="lg" py="xl">
        <Text>加载中...</Text>
      </Container>
    )
  }

  if (error) {
    return (
      <Container size="lg" py="xl">
        <Alert color="red" icon={<IconInfoCircle size={16} />}>
          加载API文档失败：{(error as Error).message}
        </Alert>
      </Container>
    )
  }

  return (
    <Container size="lg" py="xl">
      <Stack gap="lg">
        {/* 页面标题 */}
        <Group align="center" mb="xl">
          <ThemeIcon size="xl" radius="xl" color="blue" variant="light">
            <IconApi style={{ width: rem(24), height: rem(24) }} />
          </ThemeIcon>
          <Box>
            <Title order={2}>API 接口文档</Title>
            <Text size="sm" c="dimmed">
              系统第三方调用接口文档
            </Text>
          </Box>
        </Group>

        {/* 使用说明 */}
        <Alert icon={<IconInfoCircle size={16} />} color="blue" variant="light">
          <Stack gap="xs">
            <Text size="sm" fw={500}>
              使用说明：
            </Text>
            <Text size="sm">• 此文档为自动生成，仅包含请求体信息，返回体请自行调用测试</Text>
            <Text size="sm">
              • 调用时需要在请求头中添加：<Code size="sm">Authorization: Bearer YOUR_TOKEN</Code>
            </Text>
            <Text size="sm">
              • API 基础地址：<Code size="sm">{window.location.origin}/api/trpc</Code>
            </Text>
          </Stack>
        </Alert>

        {/* API 接口列表 */}
        {apiDocs && (
          <Accordion
            multiple
            value={expandedSections}
            onChange={setExpandedSections}
            variant="separated"
          >
            {Object.entries(apiDocs.children).map(([name, router]) =>
              renderRouter(name, router as ApiRouter),
            )}
          </Accordion>
        )}
      </Stack>
    </Container>
  )
}
