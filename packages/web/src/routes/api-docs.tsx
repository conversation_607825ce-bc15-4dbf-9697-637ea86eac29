import { createFileRoute } from '@tanstack/react-router'
import {
  Container,
  Stack,
  Title,
  Text,
  Group,
  Box,
  Code,
  Alert,
  ThemeIcon,
  rem,
  LoadingOverlay,
} from '@mantine/core'
import { IconApi, IconInfoCircle } from '@tabler/icons-react'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { ApiTabs } from '@/components/api-docs/ApiTabs'
import { TokenSettings } from '@/components/api-docs/TokenSettings'
import { trpc } from '@/utils/trpc'

export const Route = createFileRoute('/api-docs')({
  component: ApiDocsPage,
})

interface ApiRouter {
  children: Record<string, any>
  nodeType: 'router'
  path: string[]
}

interface ApiDocsData {
  children: Record<string, ApiRouter>
  nodeType: 'router'
  path: string[]
}

function ApiDocsPage() {
  const [useRealToken, setUseRealToken] = useState(false)

  // 获取API文档数据
  const {
    data: apiDocs,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['api-docs'],
    staleTime: Infinity,
    queryFn: async (): Promise<ApiDocsData> => {
      const response = await fetch('/trpc/docs')
      if (!response.ok) {
        throw new Error('Failed to fetch API docs')
      }
      return response.json()
    },
  })

  // 获取用户token信息
  const { data: tokenInfo } = useQuery(
    trpc.user.getApiToken.queryOptions(undefined, { staleTime: Infinity }),
  )

  if (isLoading) {
    return (
      <Container size="lg" py="xl" pos="relative">
        <LoadingOverlay visible />
        <Text ta="center">加载API文档中...</Text>
      </Container>
    )
  }

  if (error) {
    return (
      <Container size="lg" py="xl">
        <Alert color="red" icon={<IconInfoCircle size={16} />}>
          加载API文档失败：{(error as Error).message}
        </Alert>
      </Container>
    )
  }

  return (
    <Container size="lg" py="xl">
      <Stack gap="lg">
        {/* 页面标题和Token设置 */}
        <Group justify="space-between" align="flex-start" mb="xl">
          <Group align="center">
            <ThemeIcon size="xl" radius="xl" color="blue" variant="light">
              <IconApi style={{ width: rem(24), height: rem(24) }} />
            </ThemeIcon>
            <Box>
              <Title order={2}>API 接口文档</Title>
              <Text size="sm" c="dimmed">
                系统第三方调用接口文档
              </Text>
            </Box>
          </Group>
          <TokenSettings hasToken={!!tokenInfo?.hasToken} onToggle={setUseRealToken} />
        </Group>

        {/* 使用说明 */}
        <Alert icon={<IconInfoCircle size={16} />} color="blue" variant="light">
          <Stack gap="xs">
            <Text size="sm" fw={500}>
              使用说明：
            </Text>
            <Text size="sm">• 此文档为自动生成，仅包含请求体信息，返回体请自行调用测试</Text>
            <Text size="sm">• Query接口使用GET方法，Mutation接口使用POST方法</Text>
            <Text size="sm">
              • 调用时需要在请求头中添加：<Code>Authorization: Bearer YOUR_TOKEN</Code>
            </Text>
            <Text size="sm">
              • API 基础地址：<Code>{window.location.origin}/api/trpc</Code>
            </Text>
          </Stack>
        </Alert>

        {/* API 接口列表 */}
        {apiDocs && (
          <ApiTabs
            data={apiDocs.children}
            useRealToken={useRealToken}
            userToken={tokenInfo?.token || undefined}
          />
        )}
      </Stack>
    </Container>
  )
}
