import { createFileRoute } from '@tanstack/react-router'
import {
  Container,
  Stack,
  Title,
  Text,
  Group,
  Box,
  Code,
  Alert,
  ThemeIcon,
  rem,
  LoadingOverlay,
  Badge,
} from '@mantine/core'
import { IconApi, IconInfoCircle } from '@tabler/icons-react'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { ApiTabs } from '@/components/api-docs/ApiTabs'
import { TokenSettings } from '@/components/api-docs/TokenSettings'
import { trpc } from '@/utils/trpc'

export const Route = createFileRoute('/api-docs')({
  component: ApiDocsPage,
})

interface ApiRouter {
  children: Record<string, any>
  nodeType: 'router'
  path: string[]
}

interface ApiDocsData {
  children: Record<string, ApiRouter>
  nodeType: 'router'
  path: string[]
}

function ApiDocsPage() {
  const [useRealToken, setUseRealToken] = useState(false)

  // 获取API文档数据
  const {
    data: apiDocs,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['api-docs'],
    staleTime: Infinity,
    queryFn: async (): Promise<ApiDocsData> => {
      const response = await fetch('/trpc/docs')
      if (!response.ok) {
        throw new Error('Failed to fetch API docs')
      }
      return response.json()
    },
  })

  // 获取用户token信息
  const { data: tokenInfo } = useQuery(
    trpc.user.getApiToken.queryOptions(undefined, { staleTime: Infinity }),
  )

  if (isLoading) {
    return (
      <Container size="lg" py="xl" pos="relative">
        <LoadingOverlay visible />
        <Text ta="center">加载API文档中...</Text>
      </Container>
    )
  }

  if (error) {
    return (
      <Container size="lg" py="xl">
        <Alert color="red" icon={<IconInfoCircle size={16} />}>
          加载API文档失败：{(error as Error).message}
        </Alert>
      </Container>
    )
  }

  return (
    <Container size="lg" py="xl">
      <Stack gap="lg">
        {/* 页面标题和Token设置 */}
        <Group justify="space-between" align="flex-start" mb="xl">
          <Group align="center">
            <ThemeIcon size="xl" radius="xl" color="blue" variant="light">
              <IconApi style={{ width: rem(24), height: rem(24) }} />
            </ThemeIcon>
            <Box>
              <Title order={2}>API 接口文档</Title>
              <Text size="sm" c="dimmed">
                系统第三方调用接口文档
              </Text>
            </Box>
          </Group>
          <TokenSettings hasToken={!!tokenInfo?.hasToken} onToggle={setUseRealToken} />
        </Group>

        {/* 使用说明 */}
        <Alert icon={<IconInfoCircle size={16} />} color="blue" variant="light">
          <Stack gap="md">
            <Box>
              <Text size="sm" fw={600} mb="xs">
                📖 文档说明
              </Text>
              <Text size="sm" c="dimmed">
                本文档面向需要直接调用系统API的开发者，提供完整的接口参数说明和调用示例。
                适用于第三方系统集成、自动化脚本开发、API测试等场景。
              </Text>
            </Box>

            <Box>
              <Text size="sm" fw={600} mb="xs">
                🔑 认证方式
              </Text>
              <Text size="sm" mb="xs">
                所有接口调用都需要在请求头中添加API Token进行认证：
              </Text>
              <Code block>Authorization: Bearer YOUR_TOKEN</Code>
              <Text size="xs" c="dimmed" mt="xs">
                💡 在个人设置页面生成您的API Token，右上角开关可控制curl示例中是否显示真实Token
              </Text>
            </Box>

            <Box>
              <Text size="sm" fw={600} mb="xs">
                🌐 接口类型与调用方式
              </Text>
              <Stack gap="sm">
                <Box>
                  <Group gap="xs" mb="xs">
                    <Badge size="sm" color="blue" variant="light">
                      GET
                    </Badge>
                    <Text size="sm" fw={500}>
                      Query 查询接口
                    </Text>
                  </Group>
                  <Text size="sm" c="dimmed" mb="xs">
                    用于数据查询，参数通过URL查询字符串传递：
                  </Text>
                  <Code block>
                    {`GET /api/trpc/user.getByUsername?input={"username":"example"}`}
                  </Code>
                  <Text size="xs" c="dimmed" mt="xs">
                    参数对象需要JSON编码后进行URL编码作为input参数值
                  </Text>
                </Box>

                <Box>
                  <Group gap="xs" mb="xs">
                    <Badge size="sm" color="orange" variant="light">
                      POST
                    </Badge>
                    <Text size="sm" fw={500}>
                      Mutation 操作接口
                    </Text>
                  </Group>
                  <Text size="sm" c="dimmed" mb="xs">
                    用于数据修改，参数通过请求体传递：
                  </Text>
                  <Code block>
                    {`POST /api/trpc/user.generateApiToken
Content-Type: application/json

{"param1":"value1","param2":"value2"}`}
                  </Code>
                  <Text size="xs" c="dimmed" mt="xs">
                    参数对象直接放在请求体中，无需额外包装
                  </Text>
                </Box>
              </Stack>
            </Box>

            <Box>
              <Text size="sm" fw={600} mb="xs">
                📋 参数格式说明
              </Text>
              <Text size="sm" c="dimmed" mb="xs">
                参数格式说明：
              </Text>
              <Stack gap="sm">
                <Box>
                  <Text size="sm" fw={500} mb="xs">
                    GET请求参数格式：
                  </Text>
                  <Code block>{`?input={"参数名1":"参数值1","参数名2":"参数值2"}`}</Code>
                  <Text size="xs" c="dimmed" mt="xs">
                    • 无参数时：<Code>?input={'{}'}</Code>
                    <br />• 参数对象需要进行URL编码
                  </Text>
                </Box>
                <Box>
                  <Text size="sm" fw={500} mb="xs">
                    POST请求参数格式：
                  </Text>
                  <Code block>{`{"参数名1":"参数值1","参数名2":"参数值2"}`}</Code>
                  <Text size="xs" c="dimmed" mt="xs">
                    • 无参数时：<Code>{'{}'}</Code>
                    <br />• 参数对象直接作为请求体
                  </Text>
                </Box>
              </Stack>
              <Text size="xs" c="dimmed" mt="xs">
                参数类型和必填状态请参考下方接口详情中的参数表格
              </Text>
            </Box>

            <Box>
              <Text size="sm" fw={600} mb="xs">
                ⚠️ 重要提醒
              </Text>
              <Text size="sm" c="dimmed">
                • 此文档为自动生成，仅包含请求参数信息，响应格式请通过实际调用获取
                <br />• API基础地址：<Code>{window.location.origin}/api/trpc</Code>
                <br />• 建议先在开发环境测试接口调用，确认无误后再在生产环境使用
              </Text>
            </Box>
          </Stack>
        </Alert>

        {/* API 接口列表 */}
        {apiDocs && (
          <ApiTabs
            data={apiDocs.children}
            useRealToken={useRealToken}
            userToken={tokenInfo?.token || undefined}
          />
        )}
      </Stack>
    </Container>
  )
}
