import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { Container, Stack, Text, Loader, Al<PERSON>, But<PERSON> } from '@mantine/core'
import { notifications } from '@mantine/notifications'
import { useEffect, useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { IconCheck, IconX, IconArrowLeft } from '@tabler/icons-react'
import { useModal } from '@/utils/modal'
import { GitBindSuccessModal } from '@/components/git/GitBindSuccessModal'
import { GIT_OAUTH_URL } from '@/configs/git'

export const Route = createFileRoute('/git/callback')({
  component: RouteComponent,
})

function RouteComponent() {
  const navigate = useNavigate()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [errorMessage, setErrorMessage] = useState<string>('')

  // 绑定Git访问令牌的mutation
  const setGitAccessToken = useMutation(trpc.user.setUserGitAccessToken.mutationOptions())

  // 创建绑定成功模态框
  const gitBindSuccessModal = useModal(GitBindSuccessModal)

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // 从URL参数中获取code和state
        const urlParams = new URLSearchParams(window.location.search)
        const code = urlParams.get('code')
        const state = urlParams.get('state')

        if (!code) {
          setStatus('error')
          setErrorMessage('授权失败：未获取到授权码')
          return
        }

        // 调用后端接口设置用户Git访问令牌
        await setGitAccessToken.mutateAsync({ code })

        // 绑定成功
        setStatus('success')

        // 显示成功通知
        notifications.show({
          title: '绑定成功',
          message: '您已成功绑定Git账号，现在可以添加项目了！',
          color: 'green',
          icon: <IconCheck size={16} />,
        })

        // 显示绑定成功模态框
        gitBindSuccessModal.openModal({
          onNavigateToProjects: () => {
            navigate({ to: '/projects' })
          },
          onNavigateToSettings: () => {
            navigate({ to: '/settings' })
          },
        })
      } catch (error: any) {
        console.error('Git账号绑定失败:', error)
        setStatus('error')
        setErrorMessage(error.message || '绑定失败，请重试')

        notifications.show({
          title: '绑定失败',
          message: error.message || 'Git账号绑定失败，请重试',
          color: 'red',
          icon: <IconX size={16} />,
        })
      }
    }

    handleCallback()
  }, [])

  const handleRetry = () => {
    // 重新跳转到Git授权页面
    window.location.href = GIT_OAUTH_URL
  }

  const handleBackToSettings = () => {
    navigate({ to: '/settings' })
  }

  return (
    <Container size="sm" py="xl">
      <Stack align="center" gap="lg">
        {status === 'loading' && (
          <>
            <Loader size="lg" />
            <Text size="lg" fw={500}>
              正在绑定Git账号...
            </Text>
            <Text size="sm" c="dimmed">
              请稍候，正在处理您的授权信息
            </Text>
          </>
        )}

        {status === 'success' && (
          <>
            <Alert
              icon={<IconCheck size="1.5rem" />}
              title="绑定处理完成"
              color="green"
              variant="filled"
              w="100%"
            >
              <Text>Git账号绑定成功，请在弹窗中选择下一步操作。</Text>
            </Alert>
          </>
        )}

        {status === 'error' && (
          <>
            <Alert
              icon={<IconX size="1.5rem" />}
              title="绑定失败"
              color="red"
              variant="filled"
              w="100%"
            >
              <Text>{errorMessage}</Text>
            </Alert>

            <Stack>
              <Button variant="filled" color="blue" onClick={handleRetry} fullWidth>
                重新绑定
              </Button>

              <Button
                variant="subtle"
                leftSection={<IconArrowLeft size={16} />}
                onClick={handleBackToSettings}
                fullWidth
              >
                返回设置页面
              </Button>
            </Stack>
          </>
        )}
      </Stack>
    </Container>
  )
}
