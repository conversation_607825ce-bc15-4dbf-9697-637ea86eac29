import { createFileRoute, useNavigate, useSearch } from '@tanstack/react-router'
import {
  Button,
  Container,
  Group,
  Stack,
  TextInput,
  Textarea,
  Title,
  Paper,
  Alert,
  Text,
  Select,
  Loader,
  MultiSelect,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { useForm, zodResolver } from '@mantine/form'
import { z } from 'zod'
import { notifications } from '@mantine/notifications'
import { IconAlertCircle, IconArrowLeft } from '@tabler/icons-react'
import { useState, useEffect } from 'react'

export const Route = createFileRoute('/templates/new/ai')({
  component: CreateAiTemplatePage,
  validateSearch: z.object({
    projectId: z.string().optional(),
  }),
})

function CreateAiTemplatePage() {
  const navigate = useNavigate()
  const search = useSearch({ from: '/templates/new/ai' })
  const [error, setError] = useState<string | null>(null)
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(
    search.projectId ? String(search.projectId) : null,
  )

  // 获取项目列表
  const { data: projects, isLoading: isLoadingProjects } = useQuery(
    trpc.project.list.queryOptions(),
  )

  // 创建模板的 mutation
  const createTemplate = useMutation(trpc.template.ai.create.mutationOptions())

  // 定义表单 schema
  const formSchema = z.object({
    title: z.string().min(1, '模板名称不能为空'),
    prompt: z.string().min(1, '提示词不能为空'),
    description: z.string().optional(),
    auth: z.string().min(1, '请选择权限'),
    projectIds: z.array(z.string()).optional(),
  })

  // 初始化表单
  const form = useForm({
    initialValues: {
      title: '',
      prompt: `# 代码生成提示词

你是一个专业的代码生成助手，请根据以下API定义生成TypeScript客户端代码。

## 要求
1. 生成的代码应该是TypeScript类型安全的
2. 使用fetch API进行网络请求
3. 为每个API方法生成一个独立的函数
4. 为请求和响应类型生成接口定义
5. 添加适当的错误处理
6. 添加详细的注释说明每个函数的用途

## 输出格式
请生成完整的TypeScript代码，包括：
- 类型定义
- API调用函数
- 必要的工具函数

请确保代码可以直接复制使用，不要包含markdown格式。`,
      description: '',
      auth: 'PUBLIC',
      projectIds: selectedProjectId ? [selectedProjectId] : [],
    },
    validate: zodResolver(formSchema),
  })

  // 当URL中的projectId变化时更新表单
  useEffect(() => {
    if (selectedProjectId) {
      form.setFieldValue('projectIds', [selectedProjectId])
    }
  }, [selectedProjectId])

  const bindProject = useMutation(trpc.template.ai.bindProject.mutationOptions())

  // 处理创建模板
  const handleCreateTemplate = async (values: typeof form.values) => {
    setError(null)

    try {
      const newTemplate = await createTemplate.mutateAsync({
        title: values.title,
        prompt: values.prompt,
        desc: values.description,
        auth: values.auth as 'PUBLIC' | 'PRIVATE' | 'OPEN',
      })

      // 绑定选中的项目
      if (values.projectIds && values.projectIds.length > 0) {
        // 为每个项目单独调用绑定API
        const bindPromises = values.projectIds.map(async projectId => {
          return bindProject.mutateAsync({
            projectId: parseInt(projectId),
            templateIds: [newTemplate.id],
          })
        })
        await Promise.all(bindPromises)
      }

      notifications.show({
        title: '创建成功',
        message: 'AI 模板已成功创建',
        color: 'green',
      })

      // 导航到模板列表页
      navigate({ to: '/templates' })
    } catch (error: any) {
      console.error('创建模板失败:', error)
      setError(error.message || '创建模板失败，请重试')
    }
  }

  return (
    <Container size="lg" py="xl">
      <Stack gap="xl">
        <Group>
          <Button
            variant="subtle"
            leftSection={<IconArrowLeft size={16} />}
            onClick={() => navigate({ to: '/templates' })}
          >
            返回模板列表
          </Button>
        </Group>

        <Title order={2}>创建 AI 代码生成模板</Title>

        <Text c="dimmed">
          创建 AI 代码生成模板，用于根据 Proto 文件自动生成客户端代码。 模板使用提示词指导 AI
          生成代码，系统会将 API 数据和提示词一起发送给 AI 模型。
        </Text>

        {error && (
          <Alert icon={<IconAlertCircle size="1rem" />} title="错误" color="red">
            {error}
          </Alert>
        )}

        <Paper shadow="xs" p="md" withBorder>
          <form onSubmit={form.onSubmit(handleCreateTemplate)}>
            <Stack gap="md">
              <Group grow>
                <TextInput
                  label="模板名称"
                  placeholder="输入模板名称"
                  required
                  {...form.getInputProps('title')}
                />

                <Select
                  label="权限"
                  placeholder="选择权限"
                  required
                  data={[
                    { value: 'PRIVATE', label: '私有' },
                    { value: 'PUBLIC', label: '公开' },
                    { value: 'OPEN', label: '开放（任何人可修改）' },
                  ]}
                  {...form.getInputProps('auth')}
                />
              </Group>

              <MultiSelect
                label="绑定项目"
                placeholder="选择要绑定的项目（可选）"
                searchable
                nothingFoundMessage="没有找到匹配的项目"
                data={
                  projects?.map(project => ({
                    value: project.id.toString(),
                    label: project.title || project.name,
                  })) || []
                }
                rightSection={isLoadingProjects ? <Loader size="xs" /> : null}
                disabled={isLoadingProjects}
                {...form.getInputProps('projectIds')}
              />
              <Textarea
                label="描述"
                placeholder="输入模板描述（可选）"
                minRows={2}
                {...form.getInputProps('description')}
              />

              <Textarea
                label="提示词"
                placeholder="输入提示词，用于指导 AI 生成代码"
                minRows={15}
                required
                {...form.getInputProps('prompt')}
                styles={{ input: { fontFamily: 'monospace' } }}
                autosize
                maxRows={30}
              />

              <Text size="sm" c="dimmed">
                提示：在提示词中，你可以使用 API
                数据的各种属性，如服务名称、方法名称、请求和响应类型等。 AI 将根据你的提示词和 API
                数据生成代码。
              </Text>

              <Group justify="flex-end">
                <Button variant="default" onClick={() => navigate({ to: '/templates' })}>
                  取消
                </Button>
                <Button type="submit" loading={createTemplate.isPending}>
                  创建模板
                </Button>
              </Group>
            </Stack>
          </form>
        </Paper>
      </Stack>
    </Container>
  )
}
