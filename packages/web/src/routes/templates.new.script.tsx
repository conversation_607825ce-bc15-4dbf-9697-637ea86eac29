import { createFileRoute, useNavigate, useSearch } from '@tanstack/react-router'
import {
  Button,
  Container,
  Group,
  Stack,
  TextInput,
  Textarea,
  Title,
  Paper,
  Alert,
  Text,
  Select,
  Tabs,
  Code,
  Loader,
  MultiSelect,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { useForm, zodResolver } from '@mantine/form'
import { z } from 'zod'
import { notifications } from '@mantine/notifications'
import {
  IconAlertCircle,
  IconArrowLeft,
  IconBrandJavascript,
  IconBrandTypescript,
  IconCode,
} from '@tabler/icons-react'
import { useEffect, useState } from 'react'
import { CodeHighlight } from '@mantine/code-highlight'
import { Editor } from '@monaco-editor/react'

export const Route = createFileRoute('/templates/new/script')({
  component: CreateTemplatePage,
  validateSearch: z.object({
    projectId: z.string().optional(),
  }),
})

export const defaultTemplate = `/**
 * 代码生成模板
 *
 * 此函数接收 API 数据作为参数，并返回生成的代码
 * @param {ApiDataInfo} apiData - API 数据，包含服务、消息和枚举定义
 * @returns {string} 生成的代码
 */
function generate(apiData: ApiDataInfo): string {
  // 提取服务、消息和枚举
  const {
    messages,
    methodComment,
    methodName,
    enums,
    packageName,
    requestType,
    responseType,
    serviceComment,
    serviceName,
  } = apiData;

  // 生成代码的逻辑
  let code = "";

  return code;
}
`

function CreateTemplatePage() {
  const navigate = useNavigate()
  const search = useSearch({ from: '/templates/new/script' })
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<string | null>('editor')
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(
    search.projectId ? String(search.projectId) : null,
  )

  // 获取项目列表
  const { data: projects, isLoading: isLoadingProjects } = useQuery(
    trpc.project.list.queryOptions(),
  )

  // 创建模板的 mutation
  const createTemplate = useMutation(trpc.template.script.create.mutationOptions())

  // 绑定项目的 mutation
  const bindProject = useMutation(trpc.template.script.bindProject.mutationOptions())

  // 定义表单 schema
  const formSchema = z.object({
    title: z.string().min(1, '模板名称不能为空'),
    lang: z.string().min(1, '请选择语言'),
    script: z.string().min(1, '模板脚本不能为空'),
    description: z.string().optional(),
    auth: z.string().min(1, '请选择权限'),
    projectIds: z.array(z.string()).optional(),
  })

  // 初始化表单
  const form = useForm({
    initialValues: {
      title: '',
      lang: 'TS',
      script: defaultTemplate,
      description: '',
      auth: 'PUBLIC',
      projectIds: selectedProjectId ? [selectedProjectId] : [],
    },
    validate: zodResolver(formSchema),
  })

  // 当URL中的projectId变化时更新表单
  useEffect(() => {
    if (selectedProjectId) {
      form.setFieldValue('projectIds', [selectedProjectId])
    }
  }, [selectedProjectId])

  // 示例 API 数据
  const exampleApiData = {
    services: {
      ExampleService: {
        methods: {
          GetUser: {
            requestType: 'GetUserRequest',
            responseType: 'User',
          },
          ListUsers: {
            requestType: 'ListUsersRequest',
            responseType: 'ListUsersResponse',
          },
        },
      },
    },
    messages: {
      GetUserRequest: {
        fields: {
          userId: { type: 'string', id: 1 },
        },
      },
      User: {
        fields: {
          id: { type: 'string', id: 1 },
          name: { type: 'string', id: 2 },
          email: { type: 'string', id: 3 },
        },
      },
      ListUsersRequest: {
        fields: {
          pageSize: { type: 'int32', id: 1 },
          pageToken: { type: 'string', id: 2 },
        },
      },
      ListUsersResponse: {
        fields: {
          users: { type: 'User', rule: 'repeated', id: 1 },
          nextPageToken: { type: 'string', id: 2 },
        },
      },
    },
    enums: {},
  }

  // 预览生成的代码
  const [previewCode, setPreviewCode] = useState<string>('')
  const [previewError, setPreviewError] = useState<string | null>(null)

  const handlePreview = () => {
    setPreviewError(null)

    try {
      // 创建一个安全的执行环境
      const executeTemplate = new Function(
        'apiData',
        `
        try {
          ${form.values.script}

          // 假设脚本中定义了一个 generate 函数
          if (typeof generate === 'function') {
            return generate(apiData);
          } else {
            throw new Error('模板中未定义 generate 函数');
          }
        } catch (error) {
          throw new Error('执行模板时出错: ' + error.message);
        }
      `,
      )

      // 执行模板
      const result = executeTemplate(exampleApiData)
      setPreviewCode(result)
    } catch (error: any) {
      console.error('预览失败:', error)
      setPreviewError(error.message || '无法预览生成的代码')
      setPreviewCode('')
    }
  }

  // 处理创建模板
  const handleCreateTemplate = async (values: typeof form.values) => {
    setError(null)

    try {
      const newTemplate = await createTemplate.mutateAsync({
        title: values.title,
        lang: values.lang as 'JS' | 'TS',
        script: values.script,
        desc: values.description,
        auth: values.auth as 'PUBLIC' | 'PRIVATE' | 'OPEN',
      })

      // 绑定选中的项目
      if (values.projectIds && values.projectIds.length > 0) {
        // 为每个项目单独调用绑定API
        const bindPromises = values.projectIds.map(async projectId => {
          return bindProject.mutateAsync({
            projectId: parseInt(projectId),
            templateIds: [newTemplate.id],
          })
        })
        await Promise.all(bindPromises)
      }

      notifications.show({
        title: '创建成功',
        message: '模板已成功创建',
        color: 'green',
      })

      // 导航到模板列表页
      navigate({ to: '/templates' })
    } catch (error: any) {
      console.error('创建模板失败:', error)
      setError(error.message || '创建模板失败，请重试')
    }
  }

  return (
    <Container size="lg" py="xl">
      <Stack gap="xl">
        <Group>
          <Button
            variant="subtle"
            leftSection={<IconArrowLeft size={16} />}
            onClick={() => navigate({ to: '/templates' })}
          >
            返回模板列表
          </Button>
        </Group>

        <Title order={2}>创建代码生成模板</Title>

        <Text c="dimmed">
          创建代码生成模板，用于根据 Proto 文件自动生成客户端代码。 模板使用 JavaScript
          编写，需要定义一个 generate 函数，接收 API 数据并返回生成的代码。
        </Text>

        {error && (
          <Alert icon={<IconAlertCircle size="1rem" />} title="错误" color="red">
            {error}
          </Alert>
        )}

        <Paper shadow="xs" p="md" withBorder>
          <form onSubmit={form.onSubmit(handleCreateTemplate)}>
            <Stack gap="md">
              <Group grow>
                <TextInput
                  label="模板名称"
                  placeholder="输入模板名称"
                  required
                  {...form.getInputProps('title')}
                />

                <Select
                  label="语言"
                  placeholder="选择目标语言"
                  required
                  data={[
                    { value: 'TS', label: 'TypeScript' },
                    { value: 'JS', label: 'JavaScript' },
                  ]}
                  {...form.getInputProps('lang')}
                />
              </Group>

              <Group grow>
                <Select
                  label="权限"
                  placeholder="选择权限"
                  required
                  data={[
                    { value: 'PRIVATE', label: '私有' },
                    { value: 'PUBLIC', label: '公开' },
                    { value: 'OPEN', label: '开放（任何人可修改）' },
                  ]}
                  {...form.getInputProps('auth')}
                />

                <MultiSelect
                  label="绑定项目"
                  placeholder="选择要绑定的项目（可选）"
                  searchable
                  nothingFoundMessage="没有找到匹配的项目"
                  data={
                    projects?.map(project => ({
                      value: project.id.toString(),
                      label: project.title || project.name,
                    })) || []
                  }
                  rightSection={isLoadingProjects ? <Loader size="xs" /> : null}
                  disabled={isLoadingProjects}
                  {...form.getInputProps('projectIds')}
                />
              </Group>
              <Textarea
                label="描述"
                placeholder="输入模板描述（可选）"
                minRows={2}
                {...form.getInputProps('description')}
              />

              <Tabs value={activeTab} onChange={setActiveTab}>
                <Tabs.List>
                  <Tabs.Tab value="editor" leftSection={<IconCode size={14} />}>
                    编辑器
                  </Tabs.Tab>
                  <Tabs.Tab value="preview" leftSection={<IconBrandJavascript size={14} />}>
                    预览
                  </Tabs.Tab>
                </Tabs.List>

                <Tabs.Panel value="editor" pt="xs">
                  {/* <Textarea
                    label="模板脚本"
                    placeholder="输入模板脚本"
                    minRows={20}
                    required
                    {...form.getInputProps('script')}
                    styles={{ input: { fontFamily: 'monospace' } }}
                    autosize
                    maxRows={30}
                  /> */}
                  <div className="h-[calc(70vh+120px)] overflow-hidden">
                    <Editor
                      height="100%"
                      language={form.values.lang === 'TS' ? 'typescript' : 'javascript'}
                      value={form.values.script}
                      theme="vs-dark"
                      onChange={value => form.setFieldValue('script', value || '')}
                      options={{
                        padding: { bottom: 10, top: 10 },
                        theme: 'vs-dark',
                        minimap: { enabled: false },
                        scrollBeyondLastLine: false,
                        fontSize: 14,
                        tabSize: 2,
                        tabCompletion: 'on',
                        // 启用智能提示
                        quickSuggestions: true,
                        suggestOnTriggerCharacters: true,
                        acceptSuggestionOnEnter: 'on',
                      }}
                      beforeMount={monaco => {
                        // 确保在编辑器加载前配置 TypeScript
                        monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
                          noSemanticValidation: false,
                          noSyntaxValidation: false,
                        })
                        monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
                          noSemanticValidation: false,
                          noSyntaxValidation: false,
                        })
                      }}
                    />
                  </div>
                  <Button variant="light" mt="md" onClick={handlePreview}>
                    预览生成的代码
                  </Button>
                </Tabs.Panel>

                <Tabs.Panel value="preview" pt="xs">
                  {previewError ? (
                    <Alert icon={<IconAlertCircle size="1rem" />} title="预览错误" color="red">
                      {previewError}
                    </Alert>
                  ) : (
                    <>
                      <Text fw={500} mb="xs">
                        生成的代码预览：
                      </Text>
                      <Paper withBorder p="xs">
                        <CodeHighlight
                          code={previewCode || '// 点击"预览生成的代码"按钮查看预览'}
                          language={form.values.lang === 'typescript' ? 'typescript' : 'javascript'}
                          withCopyButton
                        />
                      </Paper>
                    </>
                  )}
                  <Button variant="light" mt="md" onClick={handlePreview}>
                    刷新预览
                  </Button>
                </Tabs.Panel>
              </Tabs>

              <Group justify="flex-end">
                <Button variant="default" onClick={() => navigate({ to: '/templates' })}>
                  取消
                </Button>
                <Button type="submit" loading={createTemplate.isPending}>
                  创建模板
                </Button>
              </Group>
            </Stack>
          </form>
        </Paper>
      </Stack>
    </Container>
  )
}
