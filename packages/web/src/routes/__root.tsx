import { Outlet, createRootRoute } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { AppLayout } from '@/components/layout/AppLayout'
import { ModalProvider } from '@/utils/modal'

export const Route = createRootRoute({
  component: () => (
    <ModalProvider>
      <AppLayout>
        <Outlet />
        <TanStackRouterDevtools
          position="bottom-right"
          toggleButtonProps={{
            style: { opacity: 0.5 },
          }}
        />
      </AppLayout>
    </ModalProvider>
  ),
})
