import { createFile<PERSON>out<PERSON>, getRoute<PERSON><PERSON>, use<PERSON>atches, useParams } from '@tanstack/react-router'
import {
  ActionIcon,
  Button,
  Card,
  Container,
  Group,
  Loader,
  Stack,
  Tabs,
  Text,
  Title,
  Badge,
  Alert,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { Link } from '@tanstack/react-router'
import {
  IconPlus,
  IconUsers,
  IconFileCode,
  IconAlertCircle,
  IconArrowLeft,
  IconRefresh,
  IconEdit,
  IconTrash,
  IconExternalLink,
} from '@tabler/icons-react'
import { z } from 'zod'
import { useState } from 'react'
import { notifications } from '@mantine/notifications'
import { MemberManagementModal } from '@/components/project/MemberManagementModal'
import { ProjectInfoModal } from '@/components/project/ProjectInfoModal'
import { DeleteProjectModal } from '@/components/project/DeleteProjectModal'
import { TemplateBindingModal } from '@/components/project/TemplateBindingModal'
import { NotFound } from '@/components/layout/NotFound'
import ApiDocViewer from '@/components/api/ApiDocViewer'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useModal } from '@/utils/modal'
import { InterfaceSettingsTab } from '@/components/project/InterfaceSettingsTab'
import { TemplateCard } from '@/components/project/TemplateCard'
import { UserAvatar } from '@/components/common/UserAvatar'

export const Route = createFileRoute('/$projectId')({
  component: RouteComponent,
})

function RouteComponent() {
  const { projectId } = Route.useParams()

  const projectIdNumber = z.number().safeParse(+projectId)
  if (!projectIdNumber.success) {
    return <NotFound />
  }

  return <ProjectDetailPage projectId={projectIdNumber.data} />
}
const sectionRoute = getRouteApi('/$projectId/$section')

function ProjectDetailPage({ projectId }: { projectId: number }) {
  const matches = useMatches()
  const section = matches.find(match => match.fullPath === sectionRoute.id)

  const activeSection = section?.params.section || 'api'

  const navigate = Route.useNavigate()

  // 使用 useModal 创建成员管理弹窗
  const memberManagementModal = useModal(MemberManagementModal)

  // 使用 useModal 创建项目信息编辑弹窗
  const projectInfoModal = useModal(ProjectInfoModal)

  // 使用 useModal 创建项目删除弹窗
  const deleteProjectModal = useModal(DeleteProjectModal)

  // 使用 useModal 创建模板绑定弹窗
  const templateBindingModal = useModal(TemplateBindingModal)

  // 获取项目详情
  const {
    data: project,
    isLoading,
    refetch,
    error: projectError,
  } = useQuery(trpc.project.info.queryOptions({ projectId }))

  const { data: user } = useQuery(trpc.user.getCurrentUser.queryOptions())

  // 获取最新的 Proto 文件更新时间
  const getLatestProtoUpdateTime = () => {
    if (!project?.files?.length) return null

    // 找出所有 Proto 文件中最新的修改时间
    const latestModifyTime = project.files
      .map(i => i.refreshAt)
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())[0]

    return latestModifyTime ? new Date(latestModifyTime) : null
  }

  const {
    data: parsedProtoJsonInfo,
    isLoading: isLoadingParsed,
    refetch: refetchParsed,
    error: parsedProtoJsonInfoError,
  } = useQuery(
    trpc.proto.getParsedContentByProject.queryOptions(
      { projectId },
      { enabled: activeSection === 'api' && !!project?.files.length },
    ),
  )

  const protoFileList = project?.files

  const members = useQuery(trpc.project.getMembers.queryOptions({ projectId }))

  // 获取项目绑定的脚本模板
  const {
    data: boundScriptTemplates,
    isLoading: isLoadingScriptTemplates,
    refetch: refetchScriptTemplates,
  } = useQuery(
    trpc.template.script.listByProject.queryOptions(
      { projectId },
      { enabled: activeSection === 'templates' },
    ),
  )

  // 获取项目绑定的AI模板
  const {
    data: boundAiTemplates,
    isLoading: isLoadingAiTemplates,
    refetch: refetchAiTemplates,
  } = useQuery(
    trpc.template.ai.listByProject.queryOptions(
      { projectId },
      { enabled: activeSection === 'templates' },
    ),
  )

  const syncProject = useMutation(trpc.project.sync.mutationOptions({ onSuccess: () => refetch() }))

  // 处理更新所有 Proto 文件
  const handleSyncProject = async () => {
    try {
      await syncProject.mutateAsync({
        projectId,
      })

      notifications.show({
        title: '更新成功',
        message: `已成功更新 ${project?.files.length} 个 Proto 文件`,
        color: 'green',
      })
    } catch (error) {
      console.error('更新 Proto 文件失败:', error)
      notifications.show({
        title: '更新失败',
        message: `无法更新 Proto 文件: ${(error as any)?.message}`,
        color: 'red',
      })
    }
  }

  if (isLoading) {
    return (
      <Container size="lg">
        <Group justify="center" p="xl">
          <Loader size="md" />
        </Group>
      </Container>
    )
  }

  if (!project) {
    return (
      <Container size="lg">
        <Stack gap="md" align="center" p="lg">
          <Text size="lg">{projectError?.message || '项目不存在或您没有访问权限'}</Text>
          <Button component={Link} to="/projects">
            返回项目列表
          </Button>
        </Stack>
      </Container>
    )
  }

  const canEdit = project.members.some(i => i.username === user?.username && i.role !== 'READ')
  const canDelete = project.members.some(i => i.username === user?.username && i.role === 'OWNER')

  return (
    <Container size="full" h="100%">
      <Stack gap="sm" h="100%">
        {/* 项目标题和操作按钮 */}
        <Group justify="space-between" align="center">
          <Link to="/projects">
            <IconArrowLeft size={24} />
          </Link>
          <div>
            <Title order={2}>{project.title || project.name}</Title>
          </div>
          <div className="mr-auto"></div>
          {/* <Group align="center">
            {getLatestProtoUpdateTime() && (
              <Text size="xs" c="dimmed">
                上次更新于{' '}
                {formatDistanceToNow(getLatestProtoUpdateTime()!, {
                  addSuffix: true,
                  locale: zhCN,
                })}
              </Text>
            )}
            <Button
              size="xs"
              variant="light"
              leftSection={<IconRefresh size={16} />}
              onClick={handleSyncProject}
              loading={syncProject.isPending}
            >
              更新 Proto 文件
            </Button>
          </Group> */}
        </Group>

        {/* 标签页导航 */}
        <Tabs
          className="flex-1 !flex !flex-col"
          value={activeSection}
          onChange={tab => {
            navigate({ to: '/$projectId/$section', params: { section: tab || 'api' } })
          }}
          defaultValue="api"
          keepMounted={false}
          classNames={{ panel: '!flex-1' }}
        >
          <Tabs.List>
            <Tabs.Tab value="api">API 文档</Tabs.Tab>
            <Tabs.Tab value="protos">Proto 文件</Tabs.Tab>
            <Tabs.Tab value="members">成员管理</Tabs.Tab>
            <Tabs.Tab value="templates">代码生成模板</Tabs.Tab>
            <Tabs.Tab value="interface-settings">接口设置</Tabs.Tab>
            <Tabs.Tab value="info">项目信息</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="api">
            <Stack gap="md" pt="md" className="h-full">
              {isLoadingParsed ? (
                <Group justify="center" p="xl">
                  <Loader size="md" />
                </Group>
              ) : (
                <>
                  {parsedProtoJsonInfoError ? (
                    <Alert icon={<IconAlertCircle size="1rem" />} title="无法解析" color="yellow">
                      {parsedProtoJsonInfoError?.message}
                    </Alert>
                  ) : parsedProtoJsonInfo ? (
                    <ApiDocViewer
                      parsedResultList={parsedProtoJsonInfo.list.map(i => i.json)}
                      extraParsedResultList={parsedProtoJsonInfo.extra.map(i => i.json)}
                      projectId={projectId}
                    />
                  ) : null}
                </>
              )}
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="info">
            <Stack gap="md" mt="md">
              <Group justify="space-between">
                <Title order={4}>项目基本信息</Title>
                <Group>
                  <Button
                    leftSection={<IconEdit size={16} />}
                    variant="light"
                    onClick={() => {
                      projectInfoModal.openModal({
                        projectId,
                        initialData: {
                          title: project.title || '',
                          desc: project.desc || '',
                        },
                        onSuccess: () => refetch(),
                      })
                    }}
                    disabled={!canEdit}
                  >
                    编辑
                  </Button>
                  <Button
                    color="red"
                    variant="light"
                    leftSection={<IconTrash size={16} />}
                    onClick={() =>
                      deleteProjectModal.openModal({
                        projectId: projectId,
                        projectName: project.name,
                        confirmText: project.name,
                        onFinish: () => {
                          // 成功删除后会自动导航到项目列表页
                        },
                      })
                    }
                    disabled={!canDelete}
                  >
                    删除项目
                  </Button>
                </Group>
              </Group>
              <Card shadow="sm" padding="lg" radius="md" withBorder>
                <Stack gap="xs">
                  <Group>
                    <Text fw={500}>项目名称:</Text>
                    <Text>{project.name}</Text>
                  </Group>
                  {project.title && (
                    <Group>
                      <Text fw={500}>项目标题:</Text>
                      <Text>{project.title}</Text>
                    </Group>
                  )}
                  <Group>
                    <Text fw={500}>团队信息:</Text>
                    <Text>{project.repository}</Text>
                  </Group>
                  {project.desc && (
                    <Group align="flex-start">
                      <Text fw={500}>项目描述:</Text>
                      <Text style={{ whiteSpace: 'pre-wrap' }}>{project.desc}</Text>
                    </Group>
                  )}
                  <Group>
                    <Text fw={500}>项目创建者:</Text>
                    <Text>{project.createdByUser.username}</Text>
                  </Group>
                </Stack>
              </Card>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="protos">
            <Stack gap="md" mt="md">
              <Group justify="space-between">
                <Title order={4}>Proto 文件列表</Title>
              </Group>

              <>
                {protoFileList && protoFileList.length > 0 ? (
                  <Stack gap="xs">
                    {protoFileList.map(proto => (
                      <Card key={proto.id} shadow="sm" padding="md" radius="md" withBorder>
                        <Group justify="space-between">
                          <div>
                            <Text fw={500}>{proto.name}</Text>

                            <Text size="sm" mt={5}>
                              {formatDistanceToNow(proto.latestCommit.updatedAt, {
                                addSuffix: true,
                                locale: zhCN,
                              })}{' '}
                              更新 by {proto.latestCommit.updatedBy}
                            </Text>
                          </div>
                          <Group>
                            <Button
                              rightSection={<IconExternalLink size={16} />}
                              component={'a'}
                              href={`https://git.woa.com/rick_proto/${project.repository}/blob/master/${project.name}/${proto.name}.proto`}
                              target="_blank"
                              variant="light"
                              size="sm"
                            >
                              查看
                            </Button>
                          </Group>
                        </Group>
                      </Card>
                    ))}
                  </Stack>
                ) : (
                  <Card shadow="sm" padding="lg" radius="md" withBorder>
                    <Stack align="center" gap="md">
                      <Text ta="center">该项目暂无关联的 Proto 文件</Text>
                    </Stack>
                  </Card>
                )}
              </>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="members">
            <Stack gap="md" mt="md">
              <Group justify="space-between">
                <Title order={4}>成员列表</Title>
                <Button
                  leftSection={<IconUsers size={16} />}
                  variant="light"
                  onClick={() => {
                    memberManagementModal.openModal({
                      projectId,
                      mode: 'add',
                      currentMembers: members.data || [],
                      onSuccess: () => members.refetch(),
                    })
                  }}
                  disabled={!canEdit}
                >
                  添加成员
                </Button>
              </Group>

              {members.isLoading ? (
                <Group justify="center" p="md">
                  <Loader size="sm" />
                </Group>
              ) : (
                <>
                  {members.data && members.data?.length > 0 ? (
                    <div className="grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-2">
                      {members.data.map(member => (
                        <Card key={member.username} shadow="sm" padding="xs" radius="md" withBorder>
                          <Group justify="space-between">
                            <div className="flex gap-2 items-center">
                              <UserAvatar username={member.username} size={28} />
                              <div>
                                <Text fw={500}>{member.username}</Text>
                                <Text size="xs">{member.user.chineseName}</Text>
                              </div>
                              <Badge
                                color={
                                  member.role === 'OWNER'
                                    ? 'blue'
                                    : member.role === 'WRITE'
                                      ? 'green'
                                      : 'gray'
                                }
                                size="sm"
                              >
                                {member.role === 'OWNER'
                                  ? '管理员'
                                  : member.role === 'WRITE'
                                    ? '可写'
                                    : '只读'}
                              </Badge>
                              {member.username === project.createdBy && (
                                <Badge color="red" size="sm">
                                  创建者
                                </Badge>
                              )}
                            </div>
                            <Group>
                              <ActionIcon
                                variant="light"
                                onClick={() => {
                                  memberManagementModal.openModal({
                                    projectId,
                                    mode: 'edit',
                                    initialData: {
                                      username: member.username,
                                      role: member.role as 'READ' | 'WRITE' | 'OWNER',
                                    },
                                    onSuccess: () => members.refetch(),
                                  })
                                }}
                                disabled={!canEdit}
                              >
                                <IconEdit size={16} />
                              </ActionIcon>
                              <ActionIcon
                                variant="light"
                                color="red"
                                onClick={() => {
                                  memberManagementModal.openModal({
                                    projectId,
                                    mode: 'delete',
                                    initialData: {
                                      username: member.username,
                                      role: member.role as 'READ' | 'WRITE' | 'OWNER',
                                    },
                                    onSuccess: () => members.refetch(),
                                  })
                                }}
                                disabled={!canEdit || member.username === project.createdBy}
                              >
                                <IconTrash size={16} />
                              </ActionIcon>
                            </Group>
                          </Group>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <Card shadow="sm" padding="lg" radius="md" withBorder>
                      <Stack align="center" gap="md">
                        <Text ta="center">该项目暂无其他成员</Text>
                        <Button
                          variant="light"
                          onClick={() => {
                            memberManagementModal.openModal({
                              projectId,
                              mode: 'add',
                              currentMembers: [],
                              onSuccess: () => members.refetch(),
                            })
                          }}
                        >
                          添加成员
                        </Button>
                      </Stack>
                    </Card>
                  )}
                </>
              )}
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="templates">
            <Stack gap="md" mt="md">
              <Group justify="space-between">
                <Title order={4}>代码生成模板</Title>
                <Group>
                  <Button
                    leftSection={<IconPlus size={16} />}
                    variant="light"
                    onClick={() => {
                      templateBindingModal.openModal({
                        projectId,
                        onSuccess: () => {
                          refetchScriptTemplates()
                          refetchAiTemplates()
                        },
                      })
                    }}
                  >
                    绑定模板
                  </Button>
                  <Button component={Link} to="/templates" variant="light">
                    浏览所有模板
                  </Button>
                </Group>
              </Group>

              <Tabs defaultValue="ai">
                <Tabs.List>
                  <Tabs.Tab value="ai" leftSection={<IconUsers size={14} />}>
                    AI 模板 ({boundAiTemplates?.length || 0})
                  </Tabs.Tab>
                  <Tabs.Tab value="script" leftSection={<IconFileCode size={14} />}>
                    高级模板 ({boundScriptTemplates?.length || 0})
                  </Tabs.Tab>
                </Tabs.List>

                <Tabs.Panel value="script" pt="md">
                  {isLoadingScriptTemplates ? (
                    <Group justify="center" p="xl">
                      <Loader size="md" />
                    </Group>
                  ) : boundScriptTemplates && boundScriptTemplates.length > 0 ? (
                    <div className="grid grid-cols-[repeat(auto-fill,minmax(300px,1fr))] gap-2">
                      {boundScriptTemplates.map(template => (
                        <TemplateCard
                          key={template.id}
                          template={template}
                          type="script"
                          projectId={projectId}
                          onUnbind={() => refetchScriptTemplates()}
                        />
                      ))}
                    </div>
                  ) : (
                    <Card shadow="sm" padding="lg" radius="md" withBorder>
                      <Stack align="center" gap="md">
                        <Text ta="center">该项目暂未绑定任何高级模板</Text>
                        <Button
                          variant="light"
                          onClick={() => {
                            templateBindingModal.openModal({
                              projectId,
                              onSuccess: () => {
                                refetchScriptTemplates()
                                refetchAiTemplates()
                              },
                            })
                          }}
                        >
                          绑定模板
                        </Button>
                      </Stack>
                    </Card>
                  )}
                </Tabs.Panel>

                <Tabs.Panel value="ai" pt="md">
                  {isLoadingAiTemplates ? (
                    <Group justify="center" p="xl">
                      <Loader size="md" />
                    </Group>
                  ) : boundAiTemplates && boundAiTemplates.length > 0 ? (
                    <div className="grid grid-cols-[repeat(auto-fill,minmax(300px,1fr))] gap-2">
                      {boundAiTemplates.map(template => (
                        <TemplateCard
                          key={template.id}
                          template={template}
                          type="ai"
                          projectId={projectId}
                          onUnbind={() => refetchAiTemplates()}
                        />
                      ))}
                    </div>
                  ) : (
                    <Card shadow="sm" padding="lg" radius="md" withBorder>
                      <Stack align="center" gap="md">
                        <Text ta="center">该项目暂未绑定任何 AI 模板</Text>
                        <Button
                          variant="light"
                          onClick={() => {
                            templateBindingModal.openModal({
                              projectId,
                              onSuccess: () => {
                                refetchScriptTemplates()
                                refetchAiTemplates()
                              },
                            })
                          }}
                        >
                          绑定模板
                        </Button>
                      </Stack>
                    </Card>
                  )}
                </Tabs.Panel>
              </Tabs>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="interface-settings">
            <Stack gap="md" mt="md">
              <InterfaceSettingsTab projectId={projectId} />
            </Stack>
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Container>
  )
}
