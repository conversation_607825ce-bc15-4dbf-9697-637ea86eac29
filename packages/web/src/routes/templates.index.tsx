import { createFileRoute } from '@tanstack/react-router'
import {
  <PERSON>ton,
  Card,
  Container,
  Group,
  Loader,
  Stack,
  Text,
  Title,
  Badge,
  Grid,
  Select,
  TextInput,
  Tabs,
  Menu,
  ActionIcon,
  Paper,
  Divider,
  Box,
  rem,
  ThemeIcon,
  Avatar,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { Link } from '@tanstack/react-router'
import {
  IconPlus,
  IconSearch,
  IconCode,
  IconBrandTypescript,
  IconBrandJavascript,
  IconChevronDown,
  IconDotsVertical,
  IconEdit,
  IconTrash,
  IconEye,
  IconTemplate,
  IconFilter,
  IconUsers,
  IconLock,
  IconWorld,
  IconEdit as IconEditOpen,
  IconBrandAdobeIllustrator,
} from '@tabler/icons-react'
import { useState } from 'react'
import { notifications } from '@mantine/notifications'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { $Enums } from '../../../server/prisma/prisma'
import { useModal } from '@/utils/modal'
import { AiTemplateModal } from '@/components/template/AiTemplateModal'
import { ScriptTemplateModal } from '@/components/template/ScriptTemplateModal'
import { modals } from '@mantine/modals'
import { UserAvatar } from '@/components/common/UserAvatar'

export const Route = createFileRoute('/templates/')({
  component: TemplateListPage,
})

function TemplateListPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedLang, setSelectedLang] = useState<string | null>(null)

  // 从URL中获取tab参数
  const search = new URLSearchParams(window.location.search)
  const tabParam = search.get('tab')
  const [activeTab, setActiveTab] = useState<string | null>(tabParam === 'script' ? 'script' : 'ai')

  // 当tab变化时更新URL
  const updateUrlWithTab = (tab: string) => {
    const url = new URL(window.location.href)
    url.searchParams.set('tab', tab)
    window.history.replaceState({}, '', url.toString())
    setActiveTab(tab)
  }

  // 获取脚本模板列表
  const {
    data: scriptTemplates,
    isLoading: isLoadingScriptTemplates,
    refetch: refetchScriptTemplates,
  } = useQuery(trpc.template.script.listAll.queryOptions())

  // 获取AI模板列表
  const {
    data: aiTemplates,
    isLoading: isLoadingAiTemplates,
    refetch: refetchAiTemplates,
  } = useQuery(trpc.template.ai.listAll.queryOptions())

  // 过滤脚本模板
  const filteredScriptTemplates = scriptTemplates?.filter(template => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        template.title.toLowerCase().includes(query) ||
        template.createdBy.toLowerCase().includes(query) ||
        (template.desc && template.desc.toLowerCase().includes(query))
      )
    }
    return true
  })

  // 过滤AI模板
  const filteredAiTemplates = aiTemplates?.filter(template => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        template.title.toLowerCase().includes(query) ||
        template.createdBy.toLowerCase().includes(query) ||
        (template.desc && template.desc.toLowerCase().includes(query))
      )
    }
    return true
  })

  // 获取语言图标
  const getLangIcon = (lang: $Enums.ScriptLang) => {
    switch (lang) {
      case 'TS':
        return <IconBrandTypescript size={16} />
      case 'JS':
        return <IconBrandJavascript size={16} />
    }
  }

  // 获取权限图标和颜色
  const getAuthInfo = (auth: $Enums.TemplateAuth) => {
    switch (auth) {
      case 'PRIVATE':
        return { icon: <IconLock size={14} />, color: 'gray', label: '私有' }
      case 'PUBLIC':
        return { icon: <IconWorld size={14} />, color: 'blue', label: '公开' }
      case 'OPEN':
        return { icon: <IconEditOpen size={14} />, color: 'green', label: '开放' }
      default:
        return { icon: <IconWorld size={14} />, color: 'blue', label: '公开' }
    }
  }

  // 使用 useModal 创建添加和编辑模板弹框
  const aiTemplateModal = useModal(AiTemplateModal)
  const scriptTemplateModal = useModal(ScriptTemplateModal)

  // 处理添加AI模板
  const handleAddAiTemplate = () => {
    aiTemplateModal.openModal({
      mode: 'add',
      onFinish: data => {
        notifications.show({
          title: '添加成功',
          message: 'AI 模板已成功创建',
          color: 'green',
        })
        refetchAiTemplates()
      },
    })
  }

  // 处理添加脚本模板
  const handleAddScriptTemplate = () => {
    scriptTemplateModal.openModal({
      mode: 'add',
      onFinish: data => {
        notifications.show({
          title: '添加成功',
          message: '脚本模板已成功创建',
          color: 'green',
        })
        refetchScriptTemplates()
      },
    })
  }

  // 处理编辑AI模板
  const handleEditAiTemplate = (template: any) => {
    aiTemplateModal.openModal({
      mode: 'edit',
      templateId: template.id,
      initialValues: {
        title: template.title,
        prompt: template.prompt,
        description: template.desc || '',
        auth: template.auth,
      },
      onFinish: () => {
        notifications.show({
          title: '更新成功',
          message: 'AI 模板已成功更新',
          color: 'green',
        })
        refetchAiTemplates()
      },
    })
  }

  // 处理编辑脚本模板
  const handleEditScriptTemplate = (template: any) => {
    scriptTemplateModal.openModal({
      mode: 'edit',
      templateId: template.id,
      initialValues: {
        title: template.title,
        script: template.script,
        description: template.desc || '',
        auth: template.auth,
        lang: template.lang,
        optimizePrompt: template.optimizePrompt || '',
      },
      onFinish: () => {
        notifications.show({
          title: '更新成功',
          message: '脚本模板已成功更新',
          color: 'green',
        })
        refetchScriptTemplates()
      },
    })
  }

  // 删除AI模板
  const deleteAiTemplate = useMutation(trpc.template.ai.delete.mutationOptions())

  // 处理删除AI模板
  const handleDeleteAiTemplate = (template: any) => {
    modals.openConfirmModal({
      title: '确认删除',
      children: (
        <Text size="sm">
          确定要删除模板 <strong>{template.title}</strong> 吗？此操作不可撤销。
        </Text>
      ),
      labels: { confirm: '删除', cancel: '取消' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          await deleteAiTemplate.mutateAsync({ id: template.id })
          notifications.show({
            title: '删除成功',
            message: '模板已成功删除',
            color: 'green',
          })
          refetchAiTemplates()
        } catch (error: any) {
          notifications.show({
            title: '删除失败',
            message: error.message || '删除模板失败，请重试',
            color: 'red',
          })
        }
      },
    })
  }

  // 删除脚本模板
  const deleteScriptTemplate = useMutation(trpc.template.script.delete.mutationOptions())

  // 处理删除脚本模板
  const handleDeleteScriptTemplate = (template: any) => {
    modals.openConfirmModal({
      title: '确认删除',
      children: (
        <Text size="sm">
          确定要删除模板 <strong>{template.title}</strong> 吗？此操作不可撤销。
        </Text>
      ),
      labels: { confirm: '删除', cancel: '取消' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          await deleteScriptTemplate.mutateAsync({ id: template.id })
          notifications.show({
            title: '删除成功',
            message: '模板已成功删除',
            color: 'green',
          })
          refetchScriptTemplates()
        } catch (error: any) {
          notifications.show({
            title: '删除失败',
            message: error.message || '删除模板失败，请重试',
            color: 'red',
          })
        }
      },
    })
  }

  return (
    <Container size="xl" py="xl">
      <Stack gap="xl">
        <Group justify="space-between" align="center">
          <Group>
            <ThemeIcon size="xl" radius="xl" color="indigo" variant="light">
              <IconTemplate style={{ width: rem(24), height: rem(24) }} />
            </ThemeIcon>
            <Title order={2}>模板中心</Title>
          </Group>
          <Menu shadow="md" width={200}>
            <Menu.Target>
              <Button
                leftSection={<IconPlus size={16} />}
                rightSection={<IconChevronDown size={16} />}
              >
                创建新模板
              </Button>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Label>选择模板类型</Menu.Label>
              <Menu.Item
                leftSection={<IconBrandAdobeIllustrator size={16} />}
                onClick={handleAddAiTemplate}
              >
                AI 模板
              </Menu.Item>
              <Menu.Item leftSection={<IconCode size={16} />} onClick={handleAddScriptTemplate}>
                高级模板
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>

        <Tabs value={activeTab} onChange={tab => updateUrlWithTab(tab || '')} radius="md">
          <Tabs.List>
            <Tabs.Tab value="ai" leftSection={<IconBrandAdobeIllustrator size={14} />}>
              AI 模板
            </Tabs.Tab>
            <Tabs.Tab value="script" leftSection={<IconCode size={14} />}>
              高级模板
            </Tabs.Tab>
          </Tabs.List>

          {/* 脚本模板标签页 */}
          <Tabs.Panel value="script" pt="md">
            {isLoadingScriptTemplates ? (
              <Group justify="center" p="xl">
                <Loader size="md" />
              </Group>
            ) : (
              <>
                {filteredScriptTemplates && filteredScriptTemplates.length > 0 ? (
                  <Grid>
                    {filteredScriptTemplates.map(template => {
                      const authInfo = getAuthInfo(template.auth)
                      return (
                        <Grid.Col key={template.id} span={{ base: 12, sm: 6, md: 4 }}>
                          <Card
                            shadow="sm"
                            padding="lg"
                            radius="md"
                            withBorder
                            style={{
                              height: '100%',
                              display: 'flex',
                              flexDirection: 'column',
                              cursor: 'pointer',
                            }}
                            component={Link}
                            to={`/templates/detail/script/${template.id}`}
                          >
                            <Stack gap="md" style={{ flex: 1 }}>
                              <Group justify="space-between" wrap="nowrap">
                                <Title order={4} lineClamp={1}>
                                  {template.title}
                                </Title>
                                <Menu shadow="md" width={150} position="bottom-end">
                                  <Menu.Target>
                                    <ActionIcon
                                      size="md"
                                      variant="subtle"
                                      onClick={e => e.preventDefault()}
                                    >
                                      <IconDotsVertical size={16} />
                                    </ActionIcon>
                                  </Menu.Target>
                                  <Menu.Dropdown>
                                    <Menu.Item
                                      leftSection={<IconEdit size={16} />}
                                      onClick={e => {
                                        e.preventDefault()
                                        handleEditScriptTemplate(template)
                                      }}
                                    >
                                      编辑模板
                                    </Menu.Item>
                                    <Menu.Divider />
                                    <Menu.Item
                                      leftSection={<IconTrash size={16} />}
                                      color="red"
                                      onClick={e => {
                                        e.preventDefault()
                                        handleDeleteScriptTemplate(template)
                                      }}
                                    >
                                      删除模板
                                    </Menu.Item>
                                  </Menu.Dropdown>
                                </Menu>
                              </Group>

                              <Group gap="xs">
                                <Badge
                                  leftSection={getLangIcon(template.lang)}
                                  variant="light"
                                  radius="md"
                                >
                                  {template.lang}
                                </Badge>
                                <Badge
                                  leftSection={authInfo.icon}
                                  variant="light"
                                  color={authInfo.color}
                                  radius="md"
                                >
                                  {authInfo.label}
                                </Badge>
                              </Group>

                              <Text size="sm" c="dimmed" lineClamp={2}>
                                {template.desc || '暂无描述'}
                              </Text>

                              <Group mt="auto" justify="space-between" align="center">
                                <Group gap="xs" align="center">
                                  <UserAvatar username={template.createdBy} size="sm" />
                                  <div>
                                    <Text size="xs" fw={500}>
                                      {template.createdBy}
                                    </Text>
                                    <Text size="xs" c="dimmed">
                                      {formatDistanceToNow(new Date(template.createdAt), {
                                        addSuffix: true,
                                        locale: zhCN,
                                      })}
                                    </Text>
                                  </div>
                                </Group>
                              </Group>
                            </Stack>
                          </Card>
                        </Grid.Col>
                      )
                    })}
                  </Grid>
                ) : (
                  <Paper shadow="sm" p="xl" radius="md" withBorder>
                    <Stack align="center" gap="md">
                      <ThemeIcon size="xl" radius="xl" color="gray" variant="light">
                        <IconCode size={32} opacity={0.8} />
                      </ThemeIcon>
                      <Title order={3}>暂无脚本模板</Title>
                      <Text ta="center" c="dimmed">
                        {searchQuery || selectedLang
                          ? '没有找到匹配的模板，请尝试其他搜索条件'
                          : '系统中还没有任何脚本模板，点击"创建新模板"按钮开始创建。'}
                      </Text>
                      {!searchQuery && !selectedLang && (
                        <Button
                          onClick={handleAddScriptTemplate}
                          leftSection={<IconPlus size={16} />}
                          mt="md"
                          variant="light"
                        >
                          创建高级模板
                        </Button>
                      )}
                    </Stack>
                  </Paper>
                )}
              </>
            )}
          </Tabs.Panel>

          {/* AI模板标签页 */}
          <Tabs.Panel value="ai" pt="md">
            {isLoadingAiTemplates ? (
              <Group justify="center" p="xl">
                <Loader size="md" />
              </Group>
            ) : (
              <>
                {filteredAiTemplates && filteredAiTemplates.length > 0 ? (
                  <Grid>
                    {filteredAiTemplates.map(template => {
                      const authInfo = getAuthInfo(template.auth)
                      return (
                        <Grid.Col key={template.id} span={{ base: 12, sm: 6, md: 4 }}>
                          <Card
                            shadow="sm"
                            padding="lg"
                            radius="md"
                            withBorder
                            style={{
                              height: '100%',
                              display: 'flex',
                              flexDirection: 'column',
                              cursor: 'pointer',
                            }}
                            component={Link}
                            to={`/templates/detail/ai/${template.id}`}
                          >
                            <Stack gap="md" style={{ flex: 1 }}>
                              <Group justify="space-between" wrap="nowrap">
                                <Title order={4} lineClamp={1}>
                                  {template.title}
                                </Title>
                                <Menu shadow="md" width={150} position="bottom-end">
                                  <Menu.Target>
                                    <ActionIcon
                                      size="md"
                                      variant="subtle"
                                      onClick={e => e.preventDefault()}
                                    >
                                      <IconDotsVertical size={16} />
                                    </ActionIcon>
                                  </Menu.Target>
                                  <Menu.Dropdown>
                                    <Menu.Item
                                      leftSection={<IconEdit size={16} />}
                                      onClick={e => {
                                        e.preventDefault()
                                        handleEditAiTemplate(template)
                                      }}
                                    >
                                      编辑模板
                                    </Menu.Item>
                                    <Menu.Divider />
                                    <Menu.Item
                                      leftSection={<IconTrash size={16} />}
                                      color="red"
                                      onClick={e => {
                                        e.preventDefault()
                                        handleDeleteAiTemplate(template)
                                      }}
                                    >
                                      删除模板
                                    </Menu.Item>
                                  </Menu.Dropdown>
                                </Menu>
                              </Group>

                              <Group gap="xs">
                                <Badge
                                  leftSection={<IconBrandAdobeIllustrator size={16} />}
                                  variant="light"
                                  color="green"
                                  radius="md"
                                >
                                  AI
                                </Badge>
                                <Badge
                                  leftSection={authInfo.icon}
                                  variant="light"
                                  color={authInfo.color}
                                  radius="md"
                                >
                                  {authInfo.label}
                                </Badge>
                              </Group>

                              <Text size="sm" c="dimmed" lineClamp={2}>
                                {template.desc || '暂无描述'}
                              </Text>

                              <Group mt="auto" justify="space-between" align="center">
                                <Group gap="xs" align="center">
                                  <UserAvatar username={template.createdBy} size="sm" />
                                  <div>
                                    <Text size="xs" fw={500}>
                                      {template.createdBy}
                                    </Text>
                                    <Text size="xs" c="dimmed">
                                      {formatDistanceToNow(new Date(template.createdAt), {
                                        addSuffix: true,
                                        locale: zhCN,
                                      })}
                                    </Text>
                                  </div>
                                </Group>
                              </Group>
                            </Stack>
                          </Card>
                        </Grid.Col>
                      )
                    })}
                  </Grid>
                ) : (
                  <Paper shadow="sm" p="xl" radius="md" withBorder>
                    <Stack align="center" gap="md">
                      <ThemeIcon size="xl" radius="xl" color="green" variant="light">
                        <IconBrandAdobeIllustrator size={32} opacity={0.8} />
                      </ThemeIcon>
                      <Title order={3}>暂无 AI 模板</Title>
                      <Text ta="center" c="dimmed">
                        {searchQuery
                          ? '没有找到匹配的模板，请尝试其他搜索条件'
                          : '系统中还没有任何 AI 模板，点击"创建新模板"按钮开始创建。'}
                      </Text>
                      {!searchQuery && (
                        <Button
                          onClick={handleAddAiTemplate}
                          leftSection={<IconPlus size={16} />}
                          mt="md"
                          variant="light"
                          color="green"
                        >
                          创建 AI 模板
                        </Button>
                      )}
                    </Stack>
                  </Paper>
                )}
              </>
            )}
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Container>
  )
}
