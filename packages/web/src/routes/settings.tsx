import { createFileRoute, Link } from '@tanstack/react-router'
import {
  Container,
  Stack,
  Title,
  Paper,
  Text,
  Button,
  Group,
  Box,
  Badge,
  ThemeIcon,
  rem,
  ActionIcon,
  Tooltip,
  useMantineColorScheme,
  Input,
} from '@mantine/core'
import { modals } from '@mantine/modals'
import { UserAvatar } from '@/components/common/UserAvatar'
import { useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { GIT_OAUTH_URL } from '@/configs/git'
import {
  IconSettings,
  IconRefresh,
  IconKey,
  IconCopy,
  IconTrash,
  IconPlus,
  IconEye,
  IconEyeOff,
  IconExternalLink,
} from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { useModal } from '@/utils/modal'
import { GitUnbindConfirmModal } from '@/components/git/GitUnbindConfirmModal'
import gitSvgDark from '@/assets/git_woa.dark.svg'
import gitSvgLight from '@/assets/git_woa.light.svg'
import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'

export const Route = createFileRoute('/settings')({
  component: SettingsPage,
})

function SettingsPage() {
  const { colorScheme } = useMantineColorScheme()
  const gitSvg = colorScheme === 'dark' ? gitSvgDark : gitSvgLight

  // 获取当前用户信息
  const {
    data: currentUser,
    refetch: refetchUser,
    isLoading,
  } = useQuery(trpc.user.getCurrentUser.queryOptions())

  // 获取API Token信息
  const { data: tokenInfo, refetch: refetchToken } = useQuery(trpc.user.getApiToken.queryOptions())

  // 创建解绑确认模态框
  const gitUnbindModal = useModal(GitUnbindConfirmModal)

  // Token相关状态
  const [showToken, setShowToken] = useState(false)

  const handleRefresh = async () => {
    try {
      await refetchUser()
      notifications.show({
        title: '刷新成功',
        message: '用户信息已更新',
        color: 'green',
      })
    } catch (error) {
      notifications.show({
        title: '刷新失败',
        message: '获取用户信息失败，请重试',
        color: 'red',
      })
    }
  }

  const handleGitBind = () => {
    window.location.href = GIT_OAUTH_URL
  }

  const handleGitUnbind = () => {
    gitUnbindModal.openModal({
      onSuccess: () => {
        refetchUser()
      },
    })
  }

  // API Token mutations
  const generateTokenMutation = useMutation(
    trpc.user.generateApiToken.mutationOptions({
      onSuccess: () => {
        setShowToken(true)
        refetchToken()
        notifications.show({
          title: '生成成功',
          message: 'API Token已生成，请妥善保存',
          color: 'green',
        })
      },
      onError: () => {
        notifications.show({
          title: '生成失败',
          message: '生成API Token失败，请重试',
          color: 'red',
        })
      },
    }),
  )

  const deleteTokenMutation = useMutation(
    trpc.user.deleteApiToken.mutationOptions({
      onSuccess: () => {
        setShowToken(false)
        refetchToken()
        notifications.show({
          title: '删除成功',
          message: 'API Token已删除',
          color: 'green',
        })
      },
      onError: () => {
        notifications.show({
          title: '删除失败',
          message: '删除API Token失败，请重试',
          color: 'red',
        })
      },
    }),
  )

  const handleGenerateToken = () => {
    generateTokenMutation.mutate()
  }

  const handleDeleteToken = () => {
    modals.openConfirmModal({
      title: '删除API Token',
      children: <Text size="sm">确定要删除API Token吗？删除后需要重新生成。</Text>,
      labels: { confirm: '删除', cancel: '取消' },
      confirmProps: { color: 'red' },
      onConfirm: () => deleteTokenMutation.mutate(),
    })
  }

  const handleCopyToken = () => {
    if (tokenInfo?.token) {
      navigator.clipboard.writeText(tokenInfo.token)
      notifications.show({
        title: '复制成功',
        message: 'Token已复制到剪贴板',
        color: 'green',
      })
    }
  }

  const handleToggleToken = () => {
    setShowToken(!showToken)
  }

  return (
    <Container size="md" py="xl">
      <Stack gap="lg">
        <Group align="center" mb="xl">
          <ThemeIcon size="xl" radius="xl" color="blue" variant="light">
            <IconSettings style={{ width: rem(24), height: rem(24) }} />
          </ThemeIcon>
          <Title order={2}>账号设置</Title>
        </Group>

        {/* 用户信息概览卡片 */}
        <Paper p="lg" withBorder shadow="sm" radius="md" mb="md">
          <Group align="flex-start" gap="lg">
            <UserAvatar username={currentUser?.username} size="xl" color="blue" radius="400" />
            <Box style={{ flex: 1 }}>
              <Group justify="space-between" align="flex-start" mb="md">
                <Box>
                  <Group gap="md" align="center">
                    <Title order={3}>{currentUser?.username || '用户'}</Title>
                    {currentUser?.is_admin && (
                      <Badge size="sm" color="red" variant="light">
                        管理员
                      </Badge>
                    )}
                  </Group>
                  <Text size="sm" c="dimmed" mt={4}>
                    {currentUser?.chineseName || '未设置中文名'}
                  </Text>
                </Box>
                <Tooltip label="刷新用户信息">
                  <ActionIcon variant="light" size="lg" onClick={handleRefresh} loading={isLoading}>
                    <IconRefresh size={18} />
                  </ActionIcon>
                </Tooltip>
              </Group>

              <Group gap="xl">
                <Box>
                  <Text size="xs" c="dimmed" mb={2}>
                    部门
                  </Text>
                  <Text size="sm" fw={500}>
                    {currentUser?.deptName || '未设置'}
                  </Text>
                </Box>
                {/* <Box>
                  <Text size="xs" c="dimmed" mb={2}>
                    员工ID
                  </Text>
                  <Text size="sm" fw={500}>
                    {currentUser?.staffId || '未设置'}
                  </Text>
                </Box> */}
              </Group>
            </Box>
          </Group>
        </Paper>

        {/* Git账号绑定功能区 */}
        <Paper p="lg" withBorder shadow="sm" radius="md">
          <Group gap="lg" align="center" wrap="nowrap">
            <img src={gitSvg} alt="Git" style={{ height: rem(64), width: 'auto' }} />
            <Box style={{ flex: 1 }}>
              <Title order={5} mb={4}>
                工蜂账号授权
              </Title>
              <Text size="xs" c="dimmed" mb="md">
                {currentUser?.isBindGit
                  ? '您的账号已成功连接到 git.woa.com，可以管理代码仓库和Proto项目。'
                  : '连接您的 git.woa.com 账号以管理代码仓库和Proto项目。'}
              </Text>
              <Group gap="sm" align="center">
                <Badge
                  size="xs"
                  color={currentUser?.isBindGit ? 'green' : 'gray'}
                  variant={currentUser?.isBindGit ? 'light' : 'outline'}
                >
                  {currentUser?.isBindGit ? '已授权' : '未授权'}
                </Badge>
                {currentUser?.isBindGit && (
                  <Text size="xs" c="dimmed">
                    git.woa.com
                  </Text>
                )}
              </Group>
            </Box>
            <Box>
              {
                !currentUser?.isBindGit ? (
                  <Button color="blue" onClick={handleGitBind}>
                    账号授权
                  </Button>
                ) : null
                // <Button
                //   size="sm"
                //   variant="light"
                //   color="red"
                //   leftSection={<IconUnlink size={18} />}
                //   onClick={handleGitUnbind}
                // >
                //   取消授权
                // </Button>
              }
            </Box>
          </Group>
        </Paper>

        {/* API Token管理功能区 */}
        <Paper p="lg" withBorder shadow="sm" radius="md">
          <Group gap="lg" align="flex-start" wrap="nowrap">
            <ThemeIcon size={64} radius="xl" color="orange" variant="light">
              <IconKey style={{ width: rem(36), height: rem(36) }} />
            </ThemeIcon>
            <Box style={{ flex: 1 }}>
              <Title order={5} mb={4}>
                API Token
              </Title>
              <Text size="xs" c="dimmed" mb="md">
                生成API Token用于直接调用后端接口，绕过智能网关登录。每个用户只能生成一个Token。
              </Text>

              {tokenInfo?.hasToken ? (
                <Stack gap="sm">
                  <Group gap="sm" align="center">
                    <Badge size="xs" color="green" variant="light">
                      已生成
                    </Badge>
                    {tokenInfo.createdAt && (
                      <Text size="xs" c="dimmed">
                        创建于 {new Date(tokenInfo.createdAt).toLocaleString()}
                      </Text>
                    )}
                  </Group>

                  {showToken && tokenInfo?.token && (
                    <Box>
                      <Text size="xs" c="dimmed" mb={4}>
                        Token内容：
                      </Text>
                      <Group gap="xs">
                        <Input
                          readOnly
                          size="xs"
                          ff="monospace"
                          style={{ borderRadius: 4, wordBreak: 'break-all', flex: 1 }}
                          value={tokenInfo.token}
                        ></Input>
                        <ActionIcon
                          variant="light"
                          color="blue"
                          onClick={handleCopyToken}
                          title="复制Token"
                        >
                          <IconCopy size={16} />
                        </ActionIcon>
                      </Group>
                    </Box>
                  )}

                  <Box>
                    <Text size="xs" c="dimmed" mb="xs">
                      使用方式：在请求头中添加 Authorization: Bearer YOUR_TOKEN
                      <br />
                      API地址：{window.location.origin}/api/trpc
                    </Text>
                    <Button
                      component={Link}
                      to="/api-docs"
                      size="xs"
                      variant="light"
                      color="blue"
                      leftSection={<IconExternalLink size={14} />}
                    >
                      查看API接口文档
                    </Button>
                    <Text size="xs" c="dimmed" mt="xs">
                      💡 API文档包含所有接口的详细参数说明、调用示例和curl命令，
                      适用于第三方系统集成、自动化脚本开发等场景
                    </Text>
                  </Box>
                </Stack>
              ) : (
                <Group gap="sm" align="center">
                  <Badge size="xs" color="gray" variant="outline">
                    未生成
                  </Badge>
                </Group>
              )}
            </Box>
            <Box>
              {!tokenInfo?.hasToken ? (
                <Button
                  color="blue"
                  leftSection={<IconPlus size={18} />}
                  onClick={handleGenerateToken}
                  loading={generateTokenMutation.isPending}
                >
                  生成
                </Button>
              ) : (
                <Stack gap="xs">
                  <Button
                    size="sm"
                    variant="light"
                    color="blue"
                    leftSection={showToken ? <IconEyeOff size={18} /> : <IconEye size={18} />}
                    onClick={handleToggleToken}
                  >
                    {showToken ? '隐藏' : '查看'}
                  </Button>
                  <Button
                    size="sm"
                    variant="light"
                    color="red"
                    leftSection={<IconTrash size={18} />}
                    onClick={handleDeleteToken}
                    loading={deleteTokenMutation.isPending}
                  >
                    删除
                  </Button>
                </Stack>
              )}
            </Box>
          </Group>
        </Paper>
      </Stack>
    </Container>
  )
}
