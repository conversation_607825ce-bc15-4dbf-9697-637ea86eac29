import { createFileRoute } from '@tanstack/react-router'
import {
  Container,
  Stack,
  Title,
  Paper,
  Text,
  Button,
  Group,
  Box,
  Badge,
  ThemeIcon,
  rem,
  ActionIcon,
  Tooltip,
  useMantineColorScheme,
} from '@mantine/core'
import { UserAvatar } from '@/components/common/UserAvatar'
import { useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { GIT_OAUTH_URL } from '@/configs/git'
import {
  IconBrandGithub,
  IconSettings,
  IconRefresh,
  IconCheck,
  IconUnlink,
} from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { useModal } from '@/utils/modal'
import { GitUnbindConfirmModal } from '@/components/git/GitUnbindConfirmModal'
import gitSvgDark from '@/assets/git_woa.dark.svg'
import gitSvgLight from '@/assets/git_woa.light.svg'

export const Route = createFileRoute('/settings')({
  component: SettingsPage,
})

function SettingsPage() {
  const { colorScheme } = useMantineColorScheme()
  const gitSvg = colorScheme === 'dark' ? gitSvgDark : gitSvgLight
  // 获取当前用户信息
  const {
    data: currentUser,
    refetch: refetchUser,
    isLoading,
  } = useQuery(trpc.user.getCurrentUser.queryOptions())

  // 创建解绑确认模态框
  const gitUnbindModal = useModal(GitUnbindConfirmModal)

  const handleRefresh = async () => {
    try {
      await refetchUser()
      notifications.show({
        title: '刷新成功',
        message: '用户信息已更新',
        color: 'green',
      })
    } catch (error) {
      notifications.show({
        title: '刷新失败',
        message: '获取用户信息失败，请重试',
        color: 'red',
      })
    }
  }

  const handleGitBind = () => {
    window.location.href = GIT_OAUTH_URL
  }

  const handleGitUnbind = () => {
    gitUnbindModal.openModal({
      onSuccess: () => {
        refetchUser()
      },
    })
  }

  return (
    <Container size="md" py="xl">
      <Stack gap="lg">
        <Group align="center" mb="xl">
          <ThemeIcon size="xl" radius="xl" color="blue" variant="light">
            <IconSettings style={{ width: rem(24), height: rem(24) }} />
          </ThemeIcon>
          <Title order={2}>账号设置</Title>
        </Group>

        {/* 用户信息概览卡片 */}
        <Paper p="lg" withBorder shadow="sm" radius="md" mb="md">
          <Group align="flex-start" gap="lg">
            <UserAvatar username={currentUser?.username} size="xl" color="blue" radius="400" />
            <Box style={{ flex: 1 }}>
              <Group justify="space-between" align="flex-start" mb="md">
                <Box>
                  <Group gap="md" align="center">
                    <Title order={3}>{currentUser?.username || '用户'}</Title>
                    {currentUser?.is_admin && (
                      <Badge size="sm" color="red" variant="light">
                        管理员
                      </Badge>
                    )}
                  </Group>
                  <Text size="sm" c="dimmed" mt={4}>
                    {currentUser?.chineseName || '未设置中文名'}
                  </Text>
                </Box>
                <Tooltip label="刷新用户信息">
                  <ActionIcon variant="light" size="lg" onClick={handleRefresh} loading={isLoading}>
                    <IconRefresh size={18} />
                  </ActionIcon>
                </Tooltip>
              </Group>

              <Group gap="xl">
                <Box>
                  <Text size="xs" c="dimmed" mb={2}>
                    部门
                  </Text>
                  <Text size="sm" fw={500}>
                    {currentUser?.deptName || '未设置'}
                  </Text>
                </Box>
                {/* <Box>
                  <Text size="xs" c="dimmed" mb={2}>
                    员工ID
                  </Text>
                  <Text size="sm" fw={500}>
                    {currentUser?.staffId || '未设置'}
                  </Text>
                </Box> */}
              </Group>
            </Box>
          </Group>
        </Paper>

        {/* Git账号绑定功能区 */}
        <Paper p="lg" withBorder shadow="sm" radius="md">
          <Group gap="lg" align="center" wrap="nowrap">
            <img src={gitSvg} alt="Git" style={{ height: rem(64), width: 'auto' }} />
            <Box style={{ flex: 1 }}>
              <Title order={5} mb={4}>
                工蜂账号授权
              </Title>
              <Text size="xs" c="dimmed" mb="md">
                {currentUser?.isBindGit
                  ? '您的账号已成功连接到 git.woa.com，可以管理代码仓库和Proto项目。'
                  : '连接您的 git.woa.com 账号以管理代码仓库和Proto项目。'}
              </Text>
              <Group gap="sm" align="center">
                <Badge
                  size="xs"
                  color={currentUser?.isBindGit ? 'green' : 'gray'}
                  variant={currentUser?.isBindGit ? 'light' : 'outline'}
                >
                  {currentUser?.isBindGit ? '已授权' : '未授权'}
                </Badge>
                {currentUser?.isBindGit && (
                  <Text size="xs" c="dimmed">
                    git.woa.com
                  </Text>
                )}
              </Group>
            </Box>
            <Box>
              {!currentUser?.isBindGit ? (
                <Button size="sm" color="blue" onClick={handleGitBind}>
                  账号授权
                </Button>
              ) : null
              // <Button
              //   size="sm"
              //   variant="light"
              //   color="red"
              //   leftSection={<IconUnlink size={18} />}
              //   onClick={handleGitUnbind}
              // >
              //   取消授权
              // </Button>
              }
            </Box>
          </Group>
        </Paper>
      </Stack>
    </Container>
  )
}
