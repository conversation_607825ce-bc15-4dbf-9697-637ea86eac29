import { createFileRoute, useNavigate } from '@tanstack/react-router'
import {
  Button,
  Container,
  Group,
  Stack,
  Title,
  Text,
  Paper,
  Badge,
  Loader,
  Alert,
  Divider,
  ActionIcon,
  Card,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import { IconAlertCircle, IconArrowLeft, IconEdit, IconTrash } from '@tabler/icons-react'
import { useState } from 'react'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useModal } from '@/utils/modal'
import { AiTemplateModal } from '@/components/template/AiTemplateModal'
import { modals } from '@mantine/modals'
import { CodeHighlight } from '@mantine/code-highlight'
import { ProjectBindingManager } from '@/components/template/ProjectBindingManager'

export const Route = createFileRoute('/templates/detail/ai/$id')({
  component: AiTemplateDetailPage,
})

function AiTemplateDetailPage() {
  const navigate = useNavigate()
  const params = Route.useParams()
  const templateId = parseInt(params.id)
  const [error, setError] = useState<string | null>(null)

  // 获取模板详情
  const {
    data: template,
    isLoading,
    refetch,
  } = useQuery(
    trpc.template.ai.getById.queryOptions(
      { id: templateId },
      { enabled: !isNaN(templateId), retry: false },
    ),
  )

  // 获取项目列表
  const { data: projects = [] } = useQuery(trpc.project.list.queryOptions())

  // 获取项目绑定关系
  const { data: projectBindings } = useQuery(
    trpc.template.ai.listByProject.queryOptions(
      { projectId: projects[0]?.id || 0 },
      { enabled: projects.length > 0 && !!template, retry: false },
    ),
  )

  // 删除模板的 mutation
  const deleteTemplate = useMutation(trpc.template.ai.delete.mutationOptions())

  // 使用 useModal 创建编辑模板弹框
  const editModal = useModal(AiTemplateModal)

  // 处理编辑模板
  const handleEdit = () => {
    if (!template) return

    editModal.openModal({
      mode: 'edit',
      templateId: template.id,
      initialValues: {
        title: template.title,
        prompt: template.prompt,
        description: template.desc || '',
        auth: template.auth,
        projectIds: [], // 编辑时不需要预填项目
      },
      onFinish: () => {
        refetch()
        notifications.show({
          title: '更新成功',
          message: '模板已成功更新',
          color: 'green',
        })
      },
    })
  }

  // 处理删除模板
  const handleDelete = () => {
    if (!template) return

    modals.openConfirmModal({
      title: '确认删除',
      children: (
        <Text size="sm">
          确定要删除模板 <strong>{template.title}</strong> 吗？此操作不可撤销。
        </Text>
      ),
      labels: { confirm: '删除', cancel: '取消' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          await deleteTemplate.mutateAsync({ id: template.id })
          notifications.show({
            title: '删除成功',
            message: '模板已成功删除',
            color: 'green',
          })
          navigate({ to: '/templates', search: { tab: 'ai' } })
        } catch (error: any) {
          notifications.show({
            title: '删除失败',
            message: error.message || '删除模板失败，请重试',
            color: 'red',
          })
        }
      },
    })
  }

  if (isLoading) {
    return (
      <Container size="lg" py="xl">
        <Stack align="center" py="xl">
          <Loader size="lg" />
          <Text>加载模板详情...</Text>
        </Stack>
      </Container>
    )
  }

  if (!template) {
    return (
      <Container size="lg" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="未找到模板"
          color="red"
          variant="filled"
        >
          找不到ID为 {templateId} 的模板，可能已被删除或您没有访问权限。
        </Alert>
        <Button
          mt="md"
          variant="subtle"
          leftSection={<IconArrowLeft size={16} />}
          onClick={() => navigate({ to: '/templates', search: { tab: 'ai' } })}
        >
          返回模板列表
        </Button>
      </Container>
    )
  }

  return (
    <Container size="lg" py="xl">
      <Stack gap="xl">
        <Group>
          <Button
            size="md"
            variant="subtle"
            leftSection={<IconArrowLeft size={16} />}
            onClick={() => navigate({ to: '/templates', search: { tab: 'ai' } })}
          >
            返回模板列表
          </Button>
        </Group>

        {error && (
          <Alert icon={<IconAlertCircle size="1rem" />} title="错误" color="red">
            {error}
          </Alert>
        )}

        <Paper shadow="xs" p="md" withBorder>
          <Stack gap="md">
            <Group justify="space-between">
              <Title order={2}>{template.title}</Title>
              <Group>
                <ActionIcon variant="light" color="blue" onClick={handleEdit}>
                  <IconEdit size={18} />
                </ActionIcon>
                <ActionIcon variant="light" color="red" onClick={handleDelete}>
                  <IconTrash size={18} />
                </ActionIcon>
              </Group>
            </Group>

            <Group>
              <Badge color={template.auth === 'PRIVATE' ? 'gray' : 'blue'}>
                {template.auth === 'PRIVATE'
                  ? '私有'
                  : template.auth === 'PUBLIC'
                    ? '公开'
                    : '开放'}
              </Badge>
              <Text size="sm" c="dimmed">
                创建者: {template.createdBy}
              </Text>
              <Text size="sm" c="dimmed">
                创建于{' '}
                {formatDistanceToNow(new Date(template.createdAt), {
                  addSuffix: true,
                  locale: zhCN,
                })}
              </Text>
            </Group>

            {template.desc && (
              <Text size="sm" c="dimmed">
                {template.desc}
              </Text>
            )}

            <Divider />

            <ProjectBindingManager
              templateId={templateId}
              templateType="ai"
              onUpdate={() => refetch()}
            />

            <Divider />

            <Title order={4}>提示词</Title>
            <Card withBorder>
              <CodeHighlight code={template.prompt} language="markdown" />
            </Card>
          </Stack>
        </Paper>
      </Stack>
    </Container>
  )
}
