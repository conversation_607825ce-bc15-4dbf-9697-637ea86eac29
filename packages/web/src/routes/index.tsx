import { createFileRoute } from '@tanstack/react-router'
import {
  Button,
  Container,
  Grid,
  Group,
  Title,
  Text,
  Card,
  Stack,
  Loader,
  Badge,
  SimpleGrid,
  Paper,
  ThemeIcon,
  rem,
  Divider,
  Box,
} from '@mantine/core'
import { useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { Link } from '@tanstack/react-router'
import {
  IconFolders,
  IconCode,
  IconPlus,
  IconArrowRight,
  IconBriefcase,
  IconTemplate,
  IconChartBar,
  IconRocket,
  IconUsers,
} from '@tabler/icons-react'

export const Route = createFileRoute('/')({
  component: DashboardPage,
})

function DashboardPage() {
  // 获取当前用户信息
  const { data: currentUser, isLoading: isLoadingUser } = useQuery(
    trpc.auth.getCurrentUser.queryOptions(),
  )

  // 获取项目列表
  const { data: projects, isLoading: isLoadingProjects } = useQuery(
    trpc.project.list.queryOptions(undefined, {
      // 只有在用户已登录时才获取项目
      enabled: !!currentUser,
    }),
  )

  // 获取脚本模板列表
  const { data: scriptTemplates, isLoading: isLoadingScriptTemplates } = useQuery(
    trpc.template.script.listAll.queryOptions(),
  )

  // 获取AI模板列表
  const { data: aiTemplates, isLoading: isLoadingAiTemplates } = useQuery(
    trpc.template.ai.listAll.queryOptions(),
  )

  // 计算统计数据
  const stats = {
    projectCount: projects?.length || 0,
    scriptTemplateCount: scriptTemplates?.length || 0,
    aiTemplateCount: aiTemplates?.length || 0,
    totalTemplateCount: (scriptTemplates?.length || 0) + (aiTemplates?.length || 0),
  }

  return (
    <Container size="xl" py="xl">
      <Stack gap="xl">
        {/* 欢迎区域 */}
        <Box>
          <Group justify="space-between" align="flex-start" mb="md">
            <Box>
              <Title order={1} mb="xs">
                欢迎使用 Proto API
              </Title>
              <Text size="md" c="dimmed">
                高效的 API 管理平台，实现 Proto 文档可视化，快速生成接口代码
              </Text>
            </Box>

            {/* 快速操作按钮 */}
            {/* <Group gap="sm">
              <Button
                component={Link}
                to="/projects"
                leftSection={<IconPlus size={16} />}
                variant="light"
              >
                创建项目
              </Button>
              <Button
                component={Link}
                to="/templates"
                leftSection={<IconTemplate size={16} />}
                variant="filled"
              >
                创建模板
              </Button>
            </Group> */}
          </Group>

          {/* 统计卡片 */}
          <SimpleGrid cols={{ base: 2, sm: 4 }} spacing="md">
            <Paper shadow="xs" p="md" radius="md" withBorder>
              <Group gap="xs">
                <ThemeIcon size="lg" radius="md" color="blue" variant="light">
                  <IconBriefcase style={{ width: rem(20), height: rem(20) }} />
                </ThemeIcon>
                <Box>
                  <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                    项目
                  </Text>
                  <Text size="xl" fw={700}>
                    {stats.projectCount}
                  </Text>
                </Box>
              </Group>
            </Paper>

            <Paper shadow="xs" p="md" radius="md" withBorder>
              <Group gap="xs">
                <ThemeIcon size="lg" radius="md" color="violet" variant="light">
                  <IconTemplate style={{ width: rem(20), height: rem(20) }} />
                </ThemeIcon>
                <Box>
                  <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                    AI 模板
                  </Text>
                  <Text size="xl" fw={700}>
                    {stats.aiTemplateCount}
                  </Text>
                </Box>
              </Group>
            </Paper>

            <Paper shadow="xs" p="md" radius="md" withBorder>
              <Group gap="xs">
                <ThemeIcon size="lg" radius="md" color="green" variant="light">
                  <IconCode style={{ width: rem(20), height: rem(20) }} />
                </ThemeIcon>
                <Box>
                  <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                    高级模板
                  </Text>
                  <Text size="xl" fw={700}>
                    {stats.scriptTemplateCount}
                  </Text>
                </Box>
              </Group>
            </Paper>

            <Paper shadow="xs" p="md" radius="md" withBorder>
              <Group gap="xs">
                <ThemeIcon size="lg" radius="md" color="orange" variant="light">
                  <IconChartBar style={{ width: rem(20), height: rem(20) }} />
                </ThemeIcon>
                <Box>
                  <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                    总模板
                  </Text>
                  <Text size="xl" fw={700}>
                    {stats.totalTemplateCount}
                  </Text>
                </Box>
              </Group>
            </Paper>
          </SimpleGrid>
        </Box>

        {/* 核心功能导航 */}
        <Box>
          <Title order={3} mb="md">
            核心功能
          </Title>
          <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="lg">
            <Card shadow="sm" padding="xl" radius="md" withBorder>
              <Stack gap="md">
                <Group wrap="nowrap">
                  <ThemeIcon size="xl" radius="xl" color="blue" variant="light">
                    <IconFolders style={{ width: rem(28), height: rem(28) }} />
                  </ThemeIcon>
                  <Box>
                    <Title order={4}>项目管理</Title>
                    <Text size="sm" c="dimmed">
                      创建和管理项目，组织您的 Proto 文件和团队成员
                    </Text>
                  </Box>
                </Group>
                <Text>
                  在项目中管理您的 Protocol Buffers 文件，与团队成员协作， 跟踪 API
                  变更历史，确保版本控制的一致性。
                </Text>
                <Group justify="space-between" mt="auto">
                  <Badge size="lg" variant="light" color="blue">
                    {stats.projectCount} 个项目
                  </Badge>
                  <Button
                    component={Link}
                    to="/projects"
                    variant="light"
                    rightSection={<IconArrowRight size={16} />}
                  >
                    管理项目
                  </Button>
                </Group>
              </Stack>
            </Card>

            <Card shadow="sm" padding="xl" radius="md" withBorder>
              <Stack gap="md">
                <Group wrap="nowrap">
                  <ThemeIcon size="xl" radius="xl" color="green" variant="light">
                    <IconCode style={{ width: rem(28), height: rem(28) }} />
                  </ThemeIcon>
                  <Box>
                    <Title order={4}>代码生成</Title>
                    <Text size="sm" c="dimmed">
                      创建和使用代码生成模板，自动生成客户端代码
                    </Text>
                  </Box>
                </Group>
                <Text>基于 AI 大模型能力快速生成客户端代码， 提高开发效率。</Text>
                <Group justify="space-between" mt="auto">
                  <Badge size="lg" variant="light" color="green">
                    {stats.totalTemplateCount} 个模板
                  </Badge>
                  <Button
                    component={Link}
                    to="/templates"
                    variant="light"
                    rightSection={<IconArrowRight size={16} />}
                  >
                    管理模板
                  </Button>
                </Group>
              </Stack>
            </Card>
          </SimpleGrid>
        </Box>

        {/* 最近的项目 */}
        <Box>
          <Group justify="space-between" mb="md">
            <Title order={3}>最近的项目</Title>
            <Button
              component={Link}
              to="/projects"
              variant="subtle"
              rightSection={<IconArrowRight size={16} />}
            >
              查看全部
            </Button>
          </Group>

          {isLoadingProjects ? (
            <Group justify="center" p="xl">
              <Loader size="md" />
            </Group>
          ) : (
            <>
              {projects && projects.length > 0 ? (
                <Grid>
                  {projects
                    .filter(i => i.canView)
                    .slice(0, 3)
                    .map(project => (
                      <Grid.Col key={project.id} span={{ base: 12, sm: 6, md: 4 }}>
                        <Card
                          shadow="sm"
                          padding="lg"
                          radius="md"
                          withBorder
                          component={Link}
                          to={`/${project.id}/api`}
                          style={{
                            textDecoration: 'none',
                            color: 'inherit',
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                          }}
                        >
                          <Stack gap="md" style={{ flex: 1 }}>
                            <Group justify="space-between" wrap="nowrap">
                              <Group gap="sm">
                                <ThemeIcon size="md" radius="xl" color="blue" variant="light">
                                  <IconBriefcase style={{ width: rem(16), height: rem(16) }} />
                                </ThemeIcon>
                                <Title order={4} lineClamp={1}>
                                  {project.title || project.name}
                                </Title>
                              </Group>
                            </Group>

                            <Text size="sm" c="dimmed" lineClamp={2}>
                              {project.desc || '暂无描述'}
                            </Text>

                            <Group justify="space-between" mt="auto">
                              <Badge
                                leftSection={<IconUsers size={12} />}
                                size="sm"
                                variant="light"
                                color="blue"
                              >
                                {project.files?.length || 0} 个文件
                              </Badge>
                              <Text size="xs" c="dimmed">
                                {project.repository}
                              </Text>
                            </Group>
                          </Stack>
                        </Card>
                      </Grid.Col>
                    ))}
                </Grid>
              ) : (
                <Paper shadow="sm" p="xl" radius="md" withBorder>
                  <Stack align="center" gap="md">
                    <ThemeIcon size="xl" radius="xl" color="gray" variant="light">
                      <IconRocket style={{ width: rem(32), height: rem(32) }} />
                    </ThemeIcon>
                    <Title order={4}>开始您的第一个项目</Title>
                    <Text ta="center" c="dimmed" maw={400}>
                      创建项目来管理您的 Protocol Buffers 文件， 与团队成员协作，并生成客户端代码。
                    </Text>
                    <Button
                      component={Link}
                      to="/projects"
                      leftSection={<IconPlus size={16} />}
                      size="md"
                    >
                      创建新项目
                    </Button>
                  </Stack>
                </Paper>
              )}
            </>
          )}
        </Box>
      </Stack>
    </Container>
  )
}
