import { createFileRoute, useParams } from '@tanstack/react-router'
import {
  Button,
  Card,
  Container,
  Group,
  Loader,
  Stack,
  Tabs,
  Text,
  Title,
  Alert,
  ActionIcon,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { Link } from '@tanstack/react-router'
import {
  IconAlertCircle,
  IconCode,
  IconBrandGithub,
  IconApi,
  IconArrowLeft,
  IconListDetails,
} from '@tabler/icons-react'
import { CodeHighlight } from '@mantine/code-highlight'
import ProtoStructureViewer from '@/components/proto/ProtoStructureViewer'
import ApiDocViewer from '@/components/api/ApiDocViewer'
import { z } from 'zod'
import { zodValidator } from '@tanstack/zod-adapter'

const searchSchema = z.object({
  service: z.string().optional(),
  method: z.string().optional(),
})

export const Route = createFileRoute('/protos/$pbId/$section')({
  component: ProtoDetailPage,
  validateSearch: zodValidator(searchSchema),
})

function ProtoDetailPage() {
  const { pbId, section } = useParams({ from: '/protos/$pbId/$section' })

  // 获取 Proto 文件元数据
  const { data: protoMeta, isLoading: isLoadingMeta } = useQuery(
    trpc.protoFile.getById.queryOptions({
      pbInfoId: Number(pbId),
    }),
  )

  // 获取 Proto 文件解析数据
  const { data: parsedData, isLoading: isLoadingParsed } = useQuery(
    trpc.protoFile.getParsedData.queryOptions(
      {
        pbInfoId: Number(pbId),
      },
      {
        enabled: section === 'structure' || section === 'api',
      },
    ),
  )

  // 获取 Proto 文件内容
  const { data: content, isLoading: isLoadingContent } = useQuery(
    trpc.protoFile.getContent.queryOptions(
      {
        pbInfoId: Number(pbId),
      },
      {
        enabled: section === 'source',
      },
    ),
  )

  const isLoading =
    isLoadingMeta ||
    ((section === 'structure' || section === 'api') && isLoadingParsed) ||
    (section === 'source' && isLoadingContent)

  // const deleteProtoFile = useMutation(trpc.protoFile.delete.mutationOptions())

  // 处理删除 Proto 文件
  // const handleDelete = async () => {
  //   if (!window.confirm('确定要删除此 Proto 文件吗？此操作不可撤销。')) {
  //     return
  //   }

  //   try {
  //     await deleteProtoFile.mutateAsync({ package: protoPackage })

  //     // 重定向到 Proto 列表页
  //     window.location.href = '/protos'
  //   } catch (error) {
  //     console.error('删除 Proto 文件失败:', error)
  //     alert('删除失败: ' + (error instanceof Error ? error.message : '未知错误'))
  //   }
  // }

  if (isLoadingMeta) {
    return (
      <Container size="lg">
        <Group justify="center" p="xl">
          <Loader size="md" />
        </Group>
      </Container>
    )
  }

  if (!protoMeta) {
    return (
      <Container size="lg">
        <Alert icon={<IconAlertCircle size="1rem" />} title="找不到 Proto 文件" color="red">
          <Text>Proto 文件不存在或您没有访问权限</Text>
          <Button component={Link} to="/protos" variant="light" mt="md">
            返回列表
          </Button>
        </Alert>
      </Container>
    )
  }

  return (
    <Container size="full">
      <Stack gap="sm">
        {/* 标题和操作按钮 */}
        <Group justify="space-between" align="flex-start">
          <div className="flex items-center gap-4">
            <ActionIcon component={Link} to="/protos" variant="transparent" size="lg">
              <IconArrowLeft size={24} />
            </ActionIcon>
            <Title order={2}>{protoMeta.detail.pbName}</Title>
            <Text c="dimmed">包名: {protoMeta.detail.projectFullPath}</Text>
            {protoMeta.detail.pbInfoId && (
              <Text c="dimmed">Rick ID: {protoMeta.detail.pbInfoId}</Text>
            )}
          </div>
          <Group>
            <Button component={Link} to="/protos" variant="default">
              返回列表
            </Button>
            {/* <Button color="red" variant="light" onClick={handleDelete}>
              删除
            </Button> */}
          </Group>
        </Group>

        {/* 标签页导航 */}
        <Tabs
          value={section}
          // onChange={tab => navigate({ to: '/protos/$package/$tab' })}
          keepMounted={false}
        >
          <Tabs.List>
            <Tabs.Tab
              value="overview"
              leftSection={<IconListDetails size="0.8rem" />}
              component={Link}
              {...{ to: '/protos/$pbId/overview' }}
            >
              概览
            </Tabs.Tab>
            {/* <Tabs.Tab
              value="api"
              leftSection={<IconApi size="0.8rem" />}
              component={Link}
              {...{ to: '/protos/$pbId/api' }}
            >
              API 文档
            </Tabs.Tab> */}
            <Tabs.Tab
              value="structure"
              leftSection={<IconBrandGithub size="0.8rem" />}
              component={Link}
              {...{ to: '/protos/$pbId/structure' }}
            >
              Proto 结构
            </Tabs.Tab>
            <Tabs.Tab
              value="source"
              leftSection={<IconCode size="0.8rem" />}
              component={Link}
              {...{ to: '/protos/$pbId/source' }}
            >
              源代码
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="overview">
            <Stack gap="md" mt="md">
              <Card shadow="sm" padding="lg" radius="md" withBorder>
                <Stack gap="xs">
                  <Group>
                    <Text fw={500}>名称:</Text>
                    <Text>{protoMeta.detail.pbName}</Text>
                  </Group>
                  <Group>
                    <Text fw={500}>包名:</Text>
                    <Text>{protoMeta.detail.projectFullPath}</Text>
                  </Group>

                  <Group>
                    <Text fw={500}>Rick ID:</Text>
                    <Text>{protoMeta.detail.pbInfoId}</Text>
                  </Group>

                  <Group>
                    <Text fw={500}>创建时间:</Text>
                    <Text>{new Date(protoMeta.createdAt).toLocaleString()}</Text>
                  </Group>
                  <Group>
                    <Text fw={500}>更新时间:</Text>
                    <Text>{new Date(protoMeta.updatedAt).toLocaleString()}</Text>
                  </Group>
                </Stack>
              </Card>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="structure">
            <Stack gap="md" mt="md">
              {isLoadingParsed ? (
                <Group justify="center" p="xl">
                  <Loader size="md" />
                </Group>
              ) : (
                <>
                  {parsedData ? (
                    <ProtoStructureViewer parsedData={parsedData} />
                  ) : (
                    <Alert icon={<IconAlertCircle size="1rem" />} title="无法解析" color="yellow">
                      无法获取 Proto 文件的结构数据
                    </Alert>
                  )}
                </>
              )}
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="source">
            <Stack gap="md" mt="md">
              {isLoadingContent ? (
                <Group justify="center" p="xl">
                  <Loader size="md" />
                </Group>
              ) : (
                <>
                  {content ? (
                    <Card shadow="sm" padding="lg" radius="md" withBorder>
                      <CodeHighlight
                        language="protobuf"
                        code={
                          typeof content === 'string' ? content : JSON.stringify(content, null, 2)
                        }
                        withCopyButton
                        // withLineNumbers={false}
                      />
                    </Card>
                  ) : (
                    <Alert
                      icon={<IconAlertCircle size="1rem" />}
                      title="无法获取源码"
                      color="yellow"
                    >
                      无法获取 Proto 文件的源代码
                    </Alert>
                  )}
                </>
              )}
            </Stack>
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Container>
  )
}
