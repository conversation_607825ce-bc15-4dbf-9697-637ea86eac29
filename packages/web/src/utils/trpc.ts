import type { AppRouter } from '../../../server/src/routes'
import { httpBatchLink, createTRPCClient, httpSubscriptionLink } from '@trpc/client'
import type { inferRouterInputs, inferRouterOutputs } from '@trpc/server'
import { ServerUrl } from './env'
import { createTRPCOptionsProxy } from '@trpc/tanstack-react-query'
import { QueryClient } from '@tanstack/react-query'
import { useAuthStore } from '../store/authStore'

export type ApiParam = inferRouterInputs<AppRouter>
export type ApiData = inferRouterOutputs<AppRouter>

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: 1000 * 60,
    },
  },
})

export const trpcClient = createTRPCClient<AppRouter>({
  links: [
    httpBatchLink({
      url: `${ServerUrl}/trpc`,
      headers: () => {
        const token = useAuthStore.getState().token
        return token ? { Authorization: `Bearer ${token}` } : {}
      },
    }),
  ],
})

export const trpcSubscriptionClient = createTRPCClient<AppRouter>({
  links: [
    httpSubscriptionLink({
      url: `${ServerUrl}/trpc`,
    }),
  ],
})

export const trpc = createTRPCOptionsProxy<AppRouter>({
  client: trpcClient,
  queryClient,
})
