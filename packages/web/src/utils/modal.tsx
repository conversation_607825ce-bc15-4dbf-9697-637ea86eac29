import { nanoid } from 'nanoid'
import {
  createContext,
  forwardRef,
  type JSX,
  type ReactNode,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'

// 定义弹窗管理上下文的类型
interface ModalContextType {
  addModal: (key: string, modal: ReactNode) => void
  removeModal: (key: string) => void
  modals: Map<string, ReactNode>
}

// 创建Context
const ModalContext = createContext<ModalContextType | null>(null)

// 创建Provider组件
export const ModalProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [modals, setModals] = useState<Map<string, ReactNode>>(new Map())

  const contextValue = useMemo(
    () => ({
      modals,
      addModal: (key: string, modal: ReactNode) => {
        setModals(prev => {
          const newMap = new Map(prev)
          newMap.set(key, modal)
          return newMap
        })
      },
      removeModal: (key: string) => {
        setModals(prev => {
          const newMap = new Map(prev)
          newMap.delete(key)
          return newMap
        })
      },
    }),
    [modals],
  )

  return (
    <ModalContext.Provider value={contextValue}>
      {children}
      <ModalRoot />
    </ModalContext.Provider>
  )
}

// 自定义Hook，用于在组件中访问ModalContext
const useModalContext = () => {
  const context = useContext(ModalContext)
  if (!context) {
    throw new Error('useModalContext必须在ModalProvider内部使用')
  }
  return context
}

export interface BaseOptions<FinishData = void> {
  /**
   * 弹框通过 finish 方法关闭时执行
   * - 默认没有参数，但可以通过第一个参数返回成功数据。使用 TS 时，需要在 useModal 中指定第二个泛型 FinishData
   */
  onFinish?: (data: FinishData) => void
  /**
   * 弹框关闭时执行，正常的关闭均会执行此方法
   * - 注意： 因外部组件销毁引起的弹框移除，不会执行此方法
   */
  onClose?: () => void
  /**
   * 弹框取消时执行
   * - 调用 finish 关闭的弹框，不会触发
   */
  onCancel?: () => void
}
export type { ModalComponent as MC }
export type ModalComponent<CustomOptions extends Record<string, any> = {}, FinishData = void> = (
  props: CustomOptions & {
    /**
     * 完成弹框逻辑，返回数据，同时弹出成功提示文本。
     *
     * - TS 中需要返回数据时，需要定义 useModal 的第二个泛型 FinishData。
     * - 不需要返回数据时，data 不传，或者传 undefined
     *
     * @param data 返回的数据
     * @param successMessage 顶部成功提示文本，可选
     */
    finish: (data: FinishData, successMessage?: ReactNode) => void
    /**
     * 取消弹框
     */
    cancel: () => void
    /**
     * 用于绑定 antd 的 modal
     * @example
     * ```jsx
     * <Modal {...props.bind} title="弹框">
     *  ...
     * </Modal>
     * ```
     */
    bind: {
      visible: boolean
      // onCancel: () => void
      // afterClose: () => void
      onClose: () => void
      // onExited: () => void
    }
  },
) => JSX.Element

/**
 * 通过 Hooks 的形式，封装弹框逻辑，高度契合 AntD 的 Modal 组件
 *
 * - 特点：
 *   1. 主动调用弹框，符合思维直觉
 *   2. 每次调用，均会重新实例化弹框内容
 *   3. 调用弹框时，可以直接传入自定义参数，可以添加完成、取消、关闭等事件的回调。完成事件还可以返回参数
 *   4. 外部组件销毁时，弹框也会自行移除
 *
 * @param Component 弹框的内容组件
 */
export function useModal<
  CustomOptions extends Record<string, any> = {},
  FinishData = void,
  Options extends BaseOptions<FinishData> & CustomOptions = BaseOptions<FinishData> & CustomOptions,
>(Component: ModalComponent<CustomOptions, FinishData>) {
  // 获取ModalContext
  const modalContext = useModalContext()

  // 提供 openModal 调用栈缓冲区，避免 MyModal 实例化之前的调用被忽略
  const callStack = useRef([] as (() => void)[])
  useEffect(() => {
    if (MyModalRef.current && callStack.current.length) {
      callStack.current.forEach(i => i())
      callStack.current = []
    }
  })

  const MyModalRef = useRef<{
    openModal: (options: Options) => void
    clear: () => void
  } | null>(null)

  const [outerVisible, setOuterVisible] = useState(false)
  const optionsRef = useRef<Options | null>(null)

  const MyModal = useMemo(
    () =>
      forwardRef(({}, ref) => {
        const [render, setRender] = useState(outerVisible)
        const [visible, setVisible] = useState(outerVisible)

        useEffect(() => {
          if (render && !visible) {
            setTimeout(() => {
              setVisible(true)
            }, 0)
          }
          if (!render && visible) setVisible(false)
        }, [render])

        useEffect(() => {
          setOuterVisible(visible)
        }, [visible])

        useImperativeHandle(ref, () => ({
          openModal: (options: Options) => {
            if (render) {
              console.error('useModal error: openModal called before last modal close!')
              setRender(false)
              setTimeout(() => {
                optionsRef.current = options
                setRender(true)
              }, 50)
            } else {
              optionsRef.current = options
              setRender(true)
            }
          },
          clear: () => {
            if (render && visible) {
              const options = optionsRef.current ?? ({} as Options)
              setVisible(false)
              options.onCancel?.()
              options.onClose?.()
            }
          },
        }))

        if (!render) return null
        const options = optionsRef.current ?? ({} as Options)
        const { onCancel, onClose, onFinish, ...otherProps } = options
        const rest = { ...otherProps } as unknown as CustomOptions
        const props = {
          ...rest,
          finish: (data: FinishData, successMessage?: ReactNode) => {
            setVisible(false)
            // successMessage && message.success({ content: successMessage })
            successMessage && console.log(`Success: ${successMessage}`)
            options.onFinish?.(data)
            options.onClose?.()
          },
          cancel: () => {
            setVisible(false)
            options.onCancel?.()
            options.onClose?.()
          },
          bind: {
            visible: visible,
            onClose: () => {
              setVisible(false)
              options.onCancel?.()
              options.onClose?.()
              // model 上没有 onExited，所以需要延迟自动移除
              setTimeout(() => {
                setRender(false)
              }, 500)
            },
            // onExited: () => {
            //   setRender(false)
            // },
          },
        }
        return <Component {...props}></Component>
      }),
    [outerVisible],
  )

  useEffect(() => {
    const key = nanoid()
    const modalElement = <MyModal key={key} ref={MyModalRef} />

    // 添加模态框到Context
    modalContext.addModal(key, modalElement)

    return () => {
      MyModalRef.current = null
      // 从Context中移除模态框
      modalContext.removeModal(key)
    }
  }, [])

  return {
    /**
     * 打开弹框
     * @param options 实例化弹框的参数，可以提供内置的回调，也可以提供自定义的参数
     */
    openModal: (options: {} extends Options ? void | Options : Options) => {
      if (MyModalRef.current) {
        MyModalRef.current.openModal((options ?? {}) as Options)
      } else {
        callStack.current.push(() => MyModalRef.current?.openModal((options ?? {}) as Options))
      }
    },
    /** 当前弹框是否打开 */
    visible: outerVisible,
    /** 主动关闭弹框 */
    clear: () => MyModalRef.current?.clear(),
  }
}

// 渲染所有注册的模态框
export const ModalRoot = () => {
  const { modals } = useModalContext()
  return <>{Array.from(modals.values())}</>
}
