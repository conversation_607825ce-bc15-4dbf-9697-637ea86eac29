declare interface ApiDataInfo {
  packageName: string
  serviceName: string
  serviceComment: string | null | undefined
  methodName: string
  methodComment: string | null | undefined
  requestType: string
  responseType: string
  messages: Array<{
    name: string
    comment: string | null | undefined
    fields: Record<string, { type: string; id: number; comment: string | null }>
  }>
  enums: Array<{
    name: string
    comment: string | null | undefined
    values: Record<string, number>
    comments: Record<string, string | null>
  }>
}
