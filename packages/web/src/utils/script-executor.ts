import { Interpreter } from 'eval5'
import { notifications } from '@mantine/notifications'

/**
 * 使用 eval5 执行编译后的脚本
 * @param code 编译后的 ES5 代码
 * @param apiData API 数据
 * @returns 生成的代码
 */
export async function executeScript(code: string, apiData: any): Promise<string> {
  console.log('apiData', apiData)
  try {
    // 创建 eval5 解释器
    const interpreter = new Interpreter(undefined, {
      timeout: 10000, // 10秒超时
      rootContext: {
        console: console, // 允许使用控制台
        Promise: Promise, // 允许使用 Promise
      },
      globalContextInFunction: {}, // 禁止访问全局上下文
    })

    // 执行脚本代码
    interpreter.evaluate(code)

    // 获取执行函数
    const executeScriptFn = (apiData: any) => {
      const code = `executeScript(${JSON.stringify(apiData)})`
      return interpreter.evaluate(code)
    }

    if (typeof executeScriptFn !== 'function') {
      throw new Error('脚本中未找到 executeScript 函数')
    }

    // 调用执行函数
    const result = executeScriptFn(apiData)

    // 处理可能的 Promise 结果
    if (result && typeof result.then === 'function') {
      return await result
    }

    console.log('脚本执行结果:', result)

    return result
  } catch (error) {
    console.error('脚本执行错误:', error)
    notifications.show({
      title: '脚本执行错误',
      message: error instanceof Error ? error.message : '未知错误',
      color: 'red',
    })
    throw error
  }
}
