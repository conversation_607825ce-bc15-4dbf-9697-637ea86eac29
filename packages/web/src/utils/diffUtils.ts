/**
 * 变更类型
 */
export type ChangeType = 'added' | 'modified' | 'deleted' | 'unchanged' | 'unknown'

/**
 * 变更时间范围 (兼容旧版本)
 */
export type TimeRange = 'day' | 'week' | 'month' | number

/**
 * 对比设置
 */
export interface ComparisonSettings {
  enabled: boolean
  days: number
  showChangedOnly: boolean
}

/**
 * 默认对比天数选项
 */
export const DEFAULT_COMPARISON_DAYS = [3, 7, 14, 30, 60, 90, 180, 365]

/**
 * 本地存储键名
 */
export const COMPARISON_STORAGE_KEY = 'proto_api_comparison_settings'

/**
 * 获取对比设置
 */
export function getComparisonSettings(): ComparisonSettings {
  try {
    const stored = localStorage.getItem(COMPARISON_STORAGE_KEY)
    if (stored) {
      return JSON.parse(stored)
    }
  } catch (error) {
    console.error('Failed to parse comparison settings from localStorage', error)
  }

  return { enabled: false, days: 7, showChangedOnly: false }
}

/**
 * 保存对比设置
 */
export function saveComparisonSettings(settings: ComparisonSettings): void {
  try {
    localStorage.setItem(COMPARISON_STORAGE_KEY, JSON.stringify(settings))
  } catch (error) {
    console.error('Failed to save comparison settings to localStorage', error)
  }
}

/**
 * 获取变更类型对应的颜色
 */
export function getChangeTypeColor(changeType: ChangeType, isDark: boolean = false): string {
  switch (changeType) {
    case 'added':
      return isDark ? 'rgba(76, 175, 80, 0.3)' : '#e8f5e9' // 绿色
    case 'modified':
      return isDark ? 'rgba(33, 150, 243, 0.3)' : '#e3f2fd' // 蓝色
    case 'deleted':
      return isDark ? 'rgba(244, 67, 54, 0.3)' : '#ffebee' // 红色
    case 'unchanged':
    default:
      return 'transparent'
  }
}

/**
 * 获取变更类型对应的文本颜色
 */
export function getChangeTypeTextColor(changeType: ChangeType, isDark: boolean = false): string {
  switch (changeType) {
    case 'added':
      return isDark ? '#81c784' : '#2e7d32' // 绿色
    case 'modified':
      return isDark ? '#64b5f6' : '#1565c0' // 蓝色
    case 'deleted':
      return isDark ? '#e57373' : '#c62828' // 红色
    case 'unchanged':
    default:
      return isDark ? '#ffffff' : '#000000'
  }
}

/**
 * 获取变更类型对应的边框颜色
 */
export function getChangeTypeBorderColor(changeType: ChangeType): string {
  switch (changeType) {
    case 'added':
      return '#4caf50' // 绿色
    case 'modified':
      return '#2196f3' // 蓝色
    case 'deleted':
      return '#f44336' // 红色
    case 'unchanged':
    default:
      return 'transparent'
  }
}

/**
 * 生成内联差异显示
 * @param oldText 旧文本
 * @param newText 新文本
 * @returns 带有差异标记的 HTML
 */
export function generateInlineDiff(oldText: string, newText: string): string {
  if (!oldText && !newText) return ''
  if (!oldText) return `<span class="diff-added">${newText}</span>`
  if (!newText) return `<span class="diff-deleted">${oldText}</span>`

  // 简单的词级别差异比较
  const oldWords = oldText.split(/\s+/)
  const newWords = newText.split(/\s+/)

  let result = ''
  let i = 0,
    j = 0

  while (i < oldWords.length || j < newWords.length) {
    if (i >= oldWords.length) {
      // 剩余的新词
      result += ` <span class="diff-added">${newWords.slice(j).join(' ')}</span>`
      break
    }

    if (j >= newWords.length) {
      // 剩余的旧词
      result += ` <span class="diff-deleted">${oldWords.slice(i).join(' ')}</span>`
      break
    }

    if (oldWords[i] === newWords[j]) {
      // 相同的词
      result += ` ${oldWords[i]}`
      i++
      j++
    } else {
      // 尝试查找下一个匹配点
      let foundMatch = false

      // 向前查找最多 3 个词
      for (let lookAhead = 1; lookAhead <= 3 && j + lookAhead < newWords.length; lookAhead++) {
        if (oldWords[i] === newWords[j + lookAhead]) {
          // 找到匹配，标记中间的词为新增
          result += ` <span class="diff-added">${newWords.slice(j, j + lookAhead).join(' ')}</span>`
          j += lookAhead
          foundMatch = true
          break
        }
      }

      if (!foundMatch) {
        // 向前查找最多 3 个词
        for (let lookAhead = 1; lookAhead <= 3 && i + lookAhead < oldWords.length; lookAhead++) {
          if (oldWords[i + lookAhead] === newWords[j]) {
            // 找到匹配，标记中间的词为删除
            result += ` <span class="diff-deleted">${oldWords.slice(i, i + lookAhead).join(' ')}</span>`
            i += lookAhead
            foundMatch = true
            break
          }
        }

        if (!foundMatch) {
          // 没有找到匹配，标记当前词为修改
          result += ` <span class="diff-deleted">${oldWords[i]}</span>`
          result += ` <span class="diff-added">${newWords[j]}</span>`
          i++
          j++
        }
      }
    }
  }

  return result.trim()
}

/**
 * 获取时间范围的显示文本
 */
export function getTimeRangeText(timeRange: TimeRange): string {
  if (typeof timeRange === 'number') {
    return `最近 ${timeRange} 天`
  }

  switch (timeRange) {
    case 'day':
      return '最近一天'
    case 'week':
      return '最近一周'
    case 'month':
      return '最近一个月'
    default:
      return '未知时间范围'
  }
}

/**
 * 将时间范围转换为天数
 */
export function timeRangeToDays(timeRange: TimeRange): number {
  if (typeof timeRange === 'number') {
    return timeRange
  }

  switch (timeRange) {
    case 'day':
      return 1
    case 'week':
      return 7
    case 'month':
      return 30
    default:
      return 7
  }
}
