import { loader } from '@monaco-editor/react'

import * as monaco from 'monaco-editor'
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker'
import jsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker'
import cssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker'
import htmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker'
import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker'

// 导入 API 类型定义
import apiTypesContent from './template-api-data.d.ts?raw'

self.MonacoEnvironment = {
  getWorker(_, label) {
    if (label === 'json') {
      return new jsonWorker()
    }
    if (label === 'css' || label === 'scss' || label === 'less') {
      return new cssWorker()
    }
    if (label === 'html' || label === 'handlebars' || label === 'razor') {
      return new htmlWorker()
    }
    if (label === 'typescript' || label === 'javascript') {
      return new tsWorker()
    }
    return new editorWorker()
  },
}

loader.config({ monaco })

// 配置 TypeScript 编译器选项
const configureTypeScript = (monaco: typeof import('monaco-editor')) => {
  // 获取 TypeScript 默认编译选项
  const compilerOptions: monaco.languages.typescript.CompilerOptions = {
    target: monaco.languages.typescript.ScriptTarget.ES2020,
    allowNonTsExtensions: true,
    moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
    module: monaco.languages.typescript.ModuleKind.CommonJS,
    noEmit: true,
    esModuleInterop: true,
    jsx: monaco.languages.typescript.JsxEmit.React,
    reactNamespace: 'React',
    allowJs: false,
    typeRoots: ['node_modules/@types'],
    strict: true,
    // skipLibCheck: true,
  }

  // 更新 TypeScript 默认库
  monaco.languages.typescript.typescriptDefaults.setCompilerOptions(compilerOptions)

  // 添加自定义类型定义
  const libUrl = 'ts:filename/template-api-data.d.ts'
  if (!monaco.languages.typescript.typescriptDefaults.getExtraLibs()[libUrl]) {
    monaco.languages.typescript.typescriptDefaults.addExtraLib(apiTypesContent, libUrl)
    monaco.editor.createModel(apiTypesContent, 'typescript', monaco.Uri.parse(libUrl))
  }
  if (!monaco.languages.typescript.javascriptDefaults.getExtraLibs()[libUrl]) {
    monaco.languages.typescript.javascriptDefaults.addExtraLib(apiTypesContent, libUrl)
    // 不为JavaScript创建重复的模型，以避免错误
    // monaco.editor.createModel(apiTypesContent, 'javascript', monaco.Uri.parse(libUrl))
  }

  monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
    noSemanticValidation: false,
    noSyntaxValidation: false,
  })

  // 添加导入语句的自动补全
  monaco.languages.typescript.typescriptDefaults.setEagerModelSync(true)
}

const init = loader.init().then(() => {
  configureTypeScript(monaco)
})

const initMonaco = async () => {
  // 确保只初始化一次
  await init
}

export { monaco, initMonaco }
