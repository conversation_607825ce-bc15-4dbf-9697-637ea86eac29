import type { ExtractComponentsResult } from '@/components/api/common'

/**
 * 生成TypeScript接口定义
 * @param config 配置参数
 * @returns 生成的TypeScript接口代码
 */
export const generateTsInterface = (config: {
  data: ExtractComponentsResult
  projectId: number
  /** 希望输出的接口，可以多个，如：['GetUserFansReq', 'GetUserFansRsp'] */
  structNames: string[]
  /** 是否希望输出接口字段中嵌套的接口，如果不输出，嵌套字段只保留类型名称，用户自行去导入（可以从其他地方复用）或实现，如果输出，则嵌套字段会输出完整的接口定义, 嵌套的类型单独定义 Interface */
  deep: boolean
}) => {
  const { data, structNames, deep } = config
  const { messages, enums } = data
  const extraStructNames: string[] = []
  const processedTypes = new Set<string>()

  let code = ''

  // 处理指定的结构体
  for (const structName of structNames) {
    const { item: message } = findMessageOrEnum(structName, messages)
    if (message) {
      code += generateMessageInterface(
        structName,
        message,
        messages,
        enums,
        processedTypes,
        extraStructNames,
        deep ? 1 : 0,
      )
    } else {
      const { item: enumItem } = findMessageOrEnum(structName, enums)
      if (enumItem) {
        code += generateEnumDefinition(structName, enumItem)
      }
    }
  }

  // 处理额外的依赖类型（如果deep为true）
  if (deep) {
    let currentExtraStructs = [...extraStructNames]

    while (currentExtraStructs.length > 0) {
      const nextExtraStructs: string[] = []

      for (const extraStructName of currentExtraStructs) {
        if (processedTypes.has(extraStructName)) continue

        const { item: message } = findMessageOrEnum(extraStructName, messages)
        if (message) {
          code += generateMessageInterface(
            extraStructName,
            message,
            messages,
            enums,
            processedTypes,
            nextExtraStructs,
            deep ? 1 : 0,
          )
        } else {
          const { item: enumItem } = findMessageOrEnum(extraStructName, enums)
          if (enumItem) {
            code += generateEnumDefinition(extraStructName, enumItem)
          }
        }

        processedTypes.add(extraStructName)
      }

      currentExtraStructs = nextExtraStructs
    }
  }

  return code
}

/**
 * 查找消息或枚举类型
 */
const findMessageOrEnum = (name: string, collection: Record<string, any>) => {
  // 1. 直接匹配
  if (collection[name]) {
    return { item: collection[name], key: name }
  }

  // 外部类型，必须全匹配
  if (name.includes('.')) {
    return { item: null, key: null }
  }

  // 2. 尝试移除包名前缀
  const shortName = name.split('.').pop() || ''
  if (shortName && collection[shortName]) {
    return { item: collection[shortName], key: shortName }
  }

  // 3. 尝试在所有消息中查找匹配的完全限定名称
  const matchingKey = Object.keys(collection).find(key => {
    // 完全匹配
    if (key === name) return true

    // 短名称匹配
    const keyParts = key.split('.')
    const nameParts = name.split('.')
    return keyParts[keyParts.length - 1] === nameParts[nameParts.length - 1]
  })

  if (matchingKey) {
    return { item: collection[matchingKey], key: matchingKey }
  }

  return { item: null, key: null }
}

/**
 * 生成消息接口定义
 */
const generateMessageInterface = (
  messageName: string,
  message: any,
  messages: Record<string, any>,
  enums: Record<string, any>,
  processedTypes: Set<string>,
  extraStructNames: string[],
  deep: number,
) => {
  if (!message || !message.fields) return ''
  if (processedTypes.has(messageName)) return ''

  processedTypes.add(messageName)

  // 获取简短名称（移除包名）
  const shortName = messageName.split('.').pop() || messageName

  // 生成注释
  let code = message.comment ? `/**\n * ${message.comment}\n */\n` : ''

  // 生成接口定义
  code += `export interface ${shortName} {\n`

  // 添加字段
  for (const [fieldName, fieldData] of Object.entries<any>(message.fields)) {
    const { type, rule, comment } = fieldData

    // 检查是否是嵌套类型，如果是且deep > 0，添加到extraStructNames
    if (deep > 0) {
      const { item: nestedMessage } = findMessageOrEnum(type, messages)
      if (nestedMessage) {
        extraStructNames.push(type)
      } else {
        const { item: enumItem } = findMessageOrEnum(type, enums)
        if (enumItem) {
          extraStructNames.push(type)
        }
      }
    }

    // 添加字段注释
    if (comment) {
      code += `  /** ${comment} */\n`
    }

    // 确定字段类型
    let tsType = mapProtoTypeToTs(type, messages, enums)

    // 处理重复字段（数组）
    if (rule === 'repeated') {
      tsType = `${tsType}[]`
    }

    // 添加字段定义
    code += `  ${fieldName}: ${tsType};\n`
  }

  code += `}\n\n`
  return code
}

/**
 * 生成枚举定义
 */
const generateEnumDefinition = (enumName: string, enumData: any) => {
  if (!enumData || !enumData.values) return ''

  // 获取简短名称（移除包名）
  const shortName = enumName.split('.').pop() || enumName

  // 生成注释
  let code = enumData.comment ? `/**\n * ${enumData.comment}\n */\n` : ''

  // 生成枚举定义
  code += `export enum ${shortName} {\n`

  // 添加枚举值
  for (const [valueName, value] of Object.entries<any>(enumData.values)) {
    const comment =
      enumData.comments && enumData.comments[valueName]
        ? `  /** ${enumData.comments[valueName]} */\n`
        : ''

    code += comment
    code += `  ${valueName} = ${value},\n`
  }

  code += `}\n\n`
  return code
}

/**
 * 将Proto类型映射到TypeScript类型
 */
const mapProtoTypeToTs = (
  protoType: string,
  messages: Record<string, any>,
  enums: Record<string, any>,
): string => {
  // 基本类型映射
  const typeMap: Record<string, string> = {
    double: 'number',
    float: 'number',
    int32: 'number',
    int64: 'number',
    uint32: 'number',
    uint64: 'number',
    sint32: 'number',
    sint64: 'number',
    fixed32: 'number',
    fixed64: 'number',
    sfixed32: 'number',
    sfixed64: 'number',
    bool: 'boolean',
    string: 'string',
    bytes: 'Uint8Array',
  }

  // 检查是否是基本类型
  if (typeMap[protoType]) {
    return typeMap[protoType]
  }

  // 检查是否是消息类型
  const { item: message, key: messageKey } = findMessageOrEnum(protoType, messages)
  if (message) {
    return messageKey?.split('.').pop() || protoType
  }

  // 检查是否是枚举类型
  const { item: enumItem, key: enumKey } = findMessageOrEnum(protoType, enums)
  if (enumItem) {
    return enumKey?.split('.').pop() || protoType
  }

  // 默认返回any
  return 'any'
}
