/* API 文档查看器样式 */
.apiDocContainer {
  display: flex;
  gap: 20px;
}

.sidebarContainer {
  width: 300px;
  flex-shrink: 0;
  border-right: 1px solid var(--mantine-color-dark-4);
}

.mainContainer {
  flex: 1;
}

.methodItem {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.methodItem:hover {
  background-color: var(--mantine-color-dark-6);
}

.methodItemActive {
  background-color: var(--mantine-color-blue-9);
}

.fieldRow {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--mantine-color-dark-4);
}

.fieldIcon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.fieldName {
  font-weight: 500;
  margin-right: 8px;
}

.fieldType {
  font-size: 0.8rem;
  color: var(--mantine-color-dimmed);
  margin-right: 8px;
}

.nestedField {
  margin-left: 20px;
  border-left: 1px solid var(--mantine-color-blue-5);
  padding-left: 10px;
}

.objectField {
  border-left-color: var(--mantine-color-orange-5);
}

.arrayField {
  border-left-color: var(--mantine-color-pink-5);
}

.enumField {
  border-left-color: var(--mantine-color-yellow-5);
}

/* 数据类型颜色 */
.typeString {
  color: var(--mantine-color-blue-5);
}

.typeNumber {
  color: var(--mantine-color-green-5);
}

.typeBool {
  color: var(--mantine-color-orange-5);
}

.typeArray {
  color: var(--mantine-color-pink-5);
}

.typeObject {
  color: var(--mantine-color-indigo-5);
}

.typeEnum {
  color: var(--mantine-color-yellow-5);
}
