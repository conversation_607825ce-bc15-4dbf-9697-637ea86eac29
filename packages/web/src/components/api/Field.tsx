import { useMantineColorScheme, Box, Group, Tooltip, Badge, Text } from '@mantine/core'
import { notifications } from '@mantine/notifications'
import type React from 'react'
import { findMessageOrEnum } from './ApiDocViewer'
import { getFieldTypeColor } from './common'
import { BiBracesAsterisk, MaterialSymbolsDataArray } from '../common/icons'
import {
  AntDesignFieldStringOutlined,
  RadixIconsComponentBoolean,
  MaterialSymbolsDataObject,
} from '../common/icons'
import {
  IconList,
  IconNumber,
  IconQuestionMark,
  IconCopy,
  IconPlus,
  IconEdit,
  IconTrash,
} from '@tabler/icons-react'
import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { getMainType, type MainType } from './common'
import { generateTsInterface } from '@/utils/generate'
import {
  type ChangeType,
  type TimeRange,
  getChangeTypeColor,
  generateInlineDiff,
} from '@/utils/diffUtils'
// 定义字段类型对应的图标和颜色
export const typeIcons: Record<
  MainType,
  { icon: React.ReactNode; closeIcon?: React.ReactNode; color: string }
> = {
  number: { icon: <IconNumber size={20} />, color: '#4caf50' },
  string: { icon: <AntDesignFieldStringOutlined className="text-[20px]" />, color: '#2196f3' },
  boolean: { icon: <RadixIconsComponentBoolean className="text-[20px]" />, color: '#ff9800' },
  object: {
    icon: <MaterialSymbolsDataObject className="text-[20px] scale-x-85" />,
    color: '#795548',
    closeIcon: <BiBracesAsterisk className="text-[20px] scale-x-85" />,
  },
  unknown: {
    icon: <IconQuestionMark className="text-[20px]" />,
    color: '#607d8b',
  },
  enum: {
    icon: <IconList size={20} />,
    color: '#ff9800',
  },
}

export interface FieldProps {
  name: string
  type: string
  comment?: string
  rule?: string
  id?: number
  fields?: Record<string, any>
  messages: Record<string, any>
  enums: Record<string, any>
  level?: number
  isLast?: boolean
  isFirst?: boolean
  parentType?: 'object' | 'array' | 'enum' | 'root'
  maxNameLetters: number
  defaultCollapsed: boolean | 'auto'
  timeRange?: TimeRange | null
  changeType?: ChangeType
  oldType?: string
  oldComment?: string
  getFieldChangeType?: (messageName: string, fieldName: string) => ChangeType
  messageName?: string
} // 获取类型对应的图标和颜色

export const Field: React.FC<FieldProps> = ({
  name,
  type,
  comment,
  rule,
  id,
  fields,
  messages,
  enums,
  level = 0,
  isLast = false,
  isFirst = false,
  parentType = 'root',
  maxNameLetters,
  defaultCollapsed,
  timeRange,
  changeType: propChangeType,
  oldType,
  oldComment,
  getFieldChangeType,
  messageName,
}) => {
  // 查找嵌套消息和枚举
  const { item: messageItem } = findMessageOrEnum(type, messages)
  const { item: enumItem } = findMessageOrEnum(type, enums)

  const isNested = messageItem || fields
  const isEnum = enumItem
  const isRepeated = rule === 'repeated'

  const { colorScheme } = useMantineColorScheme()
  const isDark = colorScheme === 'dark'

  const [isOpen, setIsOpen] = useState(defaultCollapsed === 'auto' ? level < 2 : !defaultCollapsed)

  useEffect(() => {
    if (defaultCollapsed === 'auto') return
    setIsOpen(!defaultCollapsed)
  }, [defaultCollapsed])

  // 获取字段的变更状态
  const changeType =
    propChangeType ||
    (timeRange && getFieldChangeType && messageName
      ? getFieldChangeType(messageName, name)
      : 'unchanged')

  // 获取变更类型对应的图标
  const getChangeTypeIcon = (changeType: ChangeType) => {
    switch (changeType) {
      case 'added':
        return <IconPlus size={14} color={isDark ? '#4caf50' : '#2e7d32'} />
      case 'modified':
        return <IconEdit size={14} color={isDark ? '#2196f3' : '#1565c0'} />
      case 'deleted':
        return <IconTrash size={14} color={isDark ? '#f44336' : '#c62828'} />
      default:
        return null
    }
  }

  // 获取字段背景色
  const getFieldBackgroundColor = () => {
    if (!timeRange || changeType === 'unchanged') return 'transparent'
    return getChangeTypeColor(changeType, isDark)
  }

  const { icon } = getTypeIconAndColor(type, rule === 'repeated', isNested, isEnum, isOpen)
  // 获取连线颜色
  const lineColor = getFieldTypeColor(type, rule, isEnum, isNested)

  // 构建连线样式
  const getLineStyle = () => {
    const baseStyle: React.CSSProperties = {
      position: 'relative',
      marginLeft: level * 28,
      paddingLeft: 19,
    }

    return baseStyle
  }

  const itemLine = (
    <>
      <div
        className="absolute w-[10px] h-[1px] bg-gray-500 top-[12px]"
        style={{ left: level * 28 - 0 }}
      ></div>

      {!isLast && (
        <div
          className="absolute w-[1px] h-full bg-gray-500 left-[-20px] top-[12px]"
          style={{ left: level * 28 - 0 }}
        ></div>
      )}
      {isFirst && (
        <div
          className="absolute w-[1px] h-[12px] bg-gray-500 left-[-20px] top-[0px]"
          style={{ left: level * 28 - 0 }}
        ></div>
      )}
    </>
  )

  // 如果是嵌套类型，递归渲染子字段
  const nestedFields = fields || (messageItem && messageItem.fields)
  const nestedEntries = nestedFields ? Object.entries(nestedFields) : []

  const mainType = getMainType(type, isRepeated, isNested, isEnum)

  const shortType = messages[type] ? type.split('.').at(-1) : type

  return (
    <Box className="relative">
      {itemLine}
      <Box
        py={2}
        style={{
          ...getLineStyle(),
          backgroundColor: getFieldBackgroundColor(),
          borderRadius: timeRange && changeType !== 'unchanged' ? 4 : 0,
          textDecoration: timeRange && changeType === 'deleted' ? 'line-through' : 'none',
        }}
      >
        <Group className="relative" wrap="nowrap">
          <Group style={{ minWidth: maxNameLetters * 7 + 80 }}>
            <Tooltip label={type} withArrow color="gray">
              <Box
                style={{ color: lineColor }}
                mr={5}
                onClick={() => isNested && setIsOpen(!isOpen)}
              >
                {icon}
              </Box>
            </Tooltip>
            <Badge
              fw={500}
              c={colorScheme === 'dark' ? 'white' : 'dark'}
              className="!text-[12px]"
              ff="monospace"
              variant="light"
              tt="none"
            >
              {name}
            </Badge>

            {['object', 'unknown', 'enum'].includes(mainType) && (
              <Badge
                variant="light"
                tt="none"
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  // 生成 TypeScript 接口定义
                  const data = {
                    messages,
                    enums,
                    services: {},
                    serviceNames: [],
                    shortServices: {},
                    shortServiceNames: [],
                    serviceProtoMapping: {},
                  }

                  const tsCode = generateTsInterface({
                    data,
                    projectId: 0,
                    structNames: [type],
                    deep: false, // 不包含嵌套类型
                  })

                  // 复制到剪贴板
                  navigator.clipboard.writeText(tsCode)

                  // 显示通知
                  notifications.show({
                    title: '复制成功',
                    message: `已复制 ${shortType} 的 TypeScript 类型定义`,
                    color: 'teal',
                  })
                }}
              >
                {shortType}
              </Badge>
            )}

            {isEnum && (
              <Tooltip
                label={
                  <Box>
                    {enumItem &&
                      Object.entries(enumItem.values).map(([enumName, enumValue]) => (
                        <Text key={enumName} size="xs" ff="monospace" className="whitespace-nowrap">
                          {enumName}: {String(enumValue)}
                        </Text>
                      ))}
                  </Box>
                }
                position="right"
                multiline
                w={200}
                withArrow
              >
                <Badge size="xs" color="orange" variant="light">
                  {Object.keys(enumItem.values).length} 枚举
                </Badge>
              </Tooltip>
            )}

            {/* 显示变更图标 */}
            {timeRange && changeType !== 'unchanged' && (
              <Box ml={5}>
                {changeType === 'added' && (
                  <IconPlus size={14} color={isDark ? '#4caf50' : '#2e7d32'} />
                )}
                {changeType === 'modified' && (
                  <IconEdit size={14} color={isDark ? '#2196f3' : '#1565c0'} />
                )}
                {changeType === 'deleted' && (
                  <IconTrash size={14} color={isDark ? '#f44336' : '#c62828'} />
                )}
              </Box>
            )}
          </Group>

          <Text size="xs" c="dimmed">
            {/* 如果有旧注释和新注释，显示内联差异 */}
            {timeRange && changeType === 'modified' && oldComment && comment ? (
              <div
                dangerouslySetInnerHTML={{
                  __html: generateInlineDiff(oldComment, comment),
                }}
                className="diff-comment"
              />
            ) : (
              comment ||
              (enumItem &&
                Object.entries(enumItem?.values)
                  .map(([enumName, enumValue]) => `${String(enumValue)}: ${enumName}`)
                  .join('; '))
            )}

            {/* 如果类型发生变化，显示旧类型 */}
            {timeRange && changeType === 'modified' && oldType && oldType !== type && (
              <Text size="xs" c="dimmed" ml={5} span>
                (原类型:{' '}
                <Text span c="red" td="line-through">
                  {oldType}
                </Text>
                )
              </Text>
            )}
          </Text>
        </Group>
      </Box>

      <motion.div
        className="list relative overflow-hidden"
        initial={{ height: isOpen ? 'auto' : 0 }}
        animate={{ height: isOpen ? 'auto' : 0 }}
      >
        {(isOpen || true ? nestedEntries : []).map(
          ([fieldName, fieldData]: [string, any], index, all) => {
            const nextMaxNameLetters = Math.max(
              ...all.map(([name]) => name.length),
              maxNameLetters - 4,
            )
            return (
              <Field
                key={fieldName}
                name={fieldName}
                type={fieldData.type}
                comment={fieldData.comment}
                rule={fieldData.rule}
                id={fieldData.id}
                fields={fieldData.fields}
                messages={messages}
                enums={enums}
                level={level + 1}
                isLast={index === nestedEntries.length - 1}
                isFirst={index === 0}
                parentType={isRepeated ? 'array' : 'object'}
                maxNameLetters={nextMaxNameLetters}
                defaultCollapsed={defaultCollapsed}
              />
            )
          },
        )}
      </motion.div>
    </Box>
  )
} // 字段组件

export const getTypeIconAndColor = (
  type: string,
  isRepeated: boolean,
  isNested: boolean,
  isEnum: boolean,
  isOpen: boolean,
) => {
  const mainType = getMainType(type, isRepeated, isNested, isEnum)
  const match = typeIcons[mainType]
  const result = { ...match }
  let icon = !isOpen ? result.closeIcon || result.icon : result.icon
  if (isRepeated) {
    icon = (
      <div className="flex items-center w-[20px] *:flex-none">
        {icon}
        <MaterialSymbolsDataArray className="text-[20px] ml-[-5px] scale-x-85" />
      </div>
    )
  }
  return { ...result, icon }
}
