import {
  Paper,
  Text,
  Stack,
  ActionIcon,
  Divider,
  Tooltip,
  CopyButton,
  Badge,
  Group,
} from '@mantine/core'

import type React from 'react'
import { findMessageOrEnum } from './ApiDocViewer'
import { type MessageStructureProps } from './common'
import { Field } from './Field'
import {
  FileIconsTypescript,
  MdiArrowCollapseVertical,
  MdiArrowExpandVertical,
} from '../common/icons'
import { useState } from 'react'
import { IconCheck, IconPlus, IconEdit, IconTrash } from '@tabler/icons-react'
import { generateTsInterface } from '@/utils/generate'
import { type ChangeType, getChangeTypeColor } from '@/utils/diffUtils'

export const MessageStructure: React.FC<MessageStructureProps> = ({
  messageName,
  messages,
  enums,
  timeRange,
  getMessageChangeType,
  getFieldChangeType,
}) => {
  // 使用通用的查找函数
  const { item: message } = findMessageOrEnum(messageName, messages)

  if (!message || !message.fields) {
    return (
      <Paper p="md" withBorder>
        <Text c="dimmed">无字段或消息不存在: {messageName}</Text>
      </Paper>
    )
  }

  const [collapseMode, setCollapseMode] = useState<'collapse' | 'expand' | 'auto'>('auto')

  // 获取消息的变更状态
  const messageChangeType =
    timeRange && getMessageChangeType ? getMessageChangeType(messageName) : 'unchanged'

  // 获取变更类型对应的图标
  const getChangeTypeIcon = (changeType: ChangeType) => {
    switch (changeType) {
      case 'added':
        return <IconPlus size={14} color="#4caf50" />
      case 'modified':
        return <IconEdit size={14} color="#2196f3" />
      case 'deleted':
        return <IconTrash size={14} color="#f44336" />
      default:
        return null
    }
  }

  // 生成 TypeScript 接口定义
  const generateTypeScriptInterface = () => {
    const data = {
      messages,
      enums,
      services: {},
      serviceNames: [],
      shortServices: {},
      shortServiceNames: [],
      serviceProtoMapping: {},
    }

    const tsCode = generateTsInterface({
      data,
      projectId: 0,
      structNames: [messageName],
      deep: true,
    })

    return tsCode
  }

  // 使用 CopyButton 组件自动处理复制功能

  return (
    <Paper p="md" withBorder className="relative">
      <div className="absolute right-4 top-4 z-10 flex gap-[2px]">
        <CopyButton value={generateTypeScriptInterface()}>
          {({ copied, copy }) => (
            <Tooltip label="复制 TypeScript 类型定义">
              <ActionIcon
                variant="subtle"
                size="md"
                color={copied ? 'teal' : 'gray'}
                onClick={copy}
              >
                {copied ? (
                  <IconCheck className="text-[16px]" />
                ) : (
                  <FileIconsTypescript className="text-[16px]" />
                )}
              </ActionIcon>
            </Tooltip>
          )}
        </CopyButton>
        <Divider orientation="vertical" mx="xs" />
        <Tooltip label="全部展开">
          <ActionIcon
            variant="subtle"
            size="md"
            color="gray"
            onClick={() => setCollapseMode('expand')}
          >
            <MdiArrowExpandVertical className="text-[16px]" />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="全部折叠">
          <ActionIcon
            variant="subtle"
            size="md"
            color="gray"
            onClick={() => setCollapseMode('collapse')}
          >
            <MdiArrowCollapseVertical className="text-[16px]" />
          </ActionIcon>
        </Tooltip>
      </div>
      <Stack gap="0">
        {timeRange && messageChangeType !== 'unchanged' && (
          <Group gap="xs" mb="xs">
            <Badge
              size="sm"
              color={
                messageChangeType === 'added'
                  ? 'green'
                  : messageChangeType === 'modified'
                    ? 'blue'
                    : messageChangeType === 'deleted'
                      ? 'red'
                      : 'gray'
              }
            >
              {messageChangeType === 'added'
                ? '新增消息'
                : messageChangeType === 'modified'
                  ? '修改消息'
                  : messageChangeType === 'deleted'
                    ? '删除消息'
                    : ''}
            </Badge>
          </Group>
        )}

        {Object.entries(message.fields).map(([fieldName, fieldData]: [string, any], index, arr) => {
          const maxNameLetters = Math.max(...arr.map(([name]) => name.length))

          // 获取字段变更信息
          const fieldChangeType =
            timeRange && getFieldChangeType
              ? getFieldChangeType(messageName, fieldName)
              : 'unchanged'

          // 获取旧类型和旧注释
          const oldType =
            timeRange && fieldChangeType === 'modified' && getFieldChangeType
              ? getFieldChangeType(messageName, fieldName + '_oldType') || undefined
              : undefined

          const oldComment =
            timeRange && fieldChangeType === 'modified' && getFieldChangeType
              ? getFieldChangeType(messageName, fieldName + '_oldComment') || undefined
              : undefined

          return (
            <Field
              name={fieldName}
              type={fieldData.type}
              comment={fieldData.comment}
              rule={fieldData.rule}
              id={fieldData.id}
              fields={fieldData.fields}
              messages={messages}
              enums={enums}
              isLast={index === arr.length - 1}
              isFirst={index === 0}
              maxNameLetters={maxNameLetters}
              defaultCollapsed={collapseMode === 'auto' ? 'auto' : collapseMode === 'collapse'}
              key={fieldName}
              timeRange={timeRange}
              changeType={fieldChangeType}
              oldType={oldType}
              oldComment={oldComment}
              getFieldChangeType={getFieldChangeType}
              messageName={messageName}
            />
          )
        })}
        {!Object.keys(message.fields).length && (
          <Text c="dimmed" size="sm">
            无字段
          </Text>
        )}
      </Stack>
    </Paper>
  )
}
