import { type ChangeType, type TimeRange } from '@/utils/diffUtils'

// 获取字段类型的颜色
export const getFieldTypeColor = (
  type: string,
  rule?: string,
  isEnum?: boolean,
  isNested?: boolean,
) => {
  // if (rule === 'repeated') return '#e91e63' // 数组类型 - 粉色
  if (isEnum) return '#ff9800' // 枚举类型 - 橙色
  if (isNested) return '#795548' // 对象类型 - 棕色
  if (isNumberType(type)) return '#4caf50' // 数字类型 - 绿色
  if (type === 'string') return '#2196f3' // 字符串类型 - 蓝色
  if (type === 'bool') return '#ff9800' // 布尔类型 - 橙色
  return '#607d8b' // 默认 - 灰色
} // 消息结构组件

export interface MessageStructureProps {
  messageName: string
  messages: Record<string, any>
  enums: Record<string, any>
  timeRange?: TimeRange | null
  getMessageChangeType?: (messageName: string) => ChangeType
  getFieldChangeType?: (messageName: string, fieldName: string) => ChangeType
} // 方法详情组件
export interface MethodDetailProps {
  serviceName: string
  methodName: string
  method: any
  messages: Record<string, any>
  enums: Record<string, any>
  timeRange?: TimeRange | null
  getMessageChangeType?: (messageName: string) => ChangeType
  getFieldChangeType?: (messageName: string, fieldName: string) => ChangeType
  projectId: number
  services: Record<string, any>
  serviceNames: string[]
  shortServices: Record<string, any>
  shortServiceNames: string[]
  serviceProtoMapping?: Record<string, { fileId: number; fileName: string }>
}

export type MainType = 'number' | 'string' | 'boolean' | 'object' | 'enum' | 'unknown'

export const isNumberType = (type: string) => {
  return (
    type.startsWith('int') ||
    type.startsWith('float') ||
    type.startsWith('double') ||
    type.startsWith('uint')
  )
}

export const getMainType = (
  type: string,
  isRepeated: boolean,
  isNested: boolean,
  isEnum: boolean,
): MainType => {
  if (isNumberType(type)) return 'number'
  if (type === 'string') return 'string'
  if (type === 'bool') return 'boolean'
  if (isNested) return 'object'
  if (isEnum) return 'enum'
  return 'unknown'
}

// export type ExtractComponentsResult = {
//   messages: Record<string, any>
//   enums: Record<string, any>
//   services: Record<string, any>
//   serviceNames: string[]
//   shortServices: Record<string, any>
//   shortServiceNames: string[]
// }
// /**
//  * 递归提取所有消息、枚举和服务
//  * @param nested 嵌套对象
//  * @param prefix 前缀
//  * @returns 包含所有消息、枚举和服务的对象
//  */
// export const extractComponents = (nested: Record<string, any>, prefix = '') => {
//   let messages: Record<string, any> = {}
//   let enums: Record<string, any> = {}
//   let services: Record<string, any> = {}
//   let shortServices: Record<string, any> = {}
//   let serviceNames: string[] = []
//   let shortServiceNames: string[] = []

//   for (const key in nested) {
//     const fullName = prefix ? `${prefix}.${key}` : key
//     const item = nested[key]

//     if (item.fields) {
//       // 这是一个消息
//       messages[fullName] = item
//     } else if (item.values) {
//       // 这是一个枚举
//       enums[fullName] = item
//     } else if (item.methods) {
//       // 这是一个服务
//       services[fullName] = item
//       serviceNames.push(fullName)
//       shortServiceNames.push(key)
//       shortServices[key] = item
//     }

//     // 递归处理嵌套项
//     if (item.nested) {
//       const {
//         messages: nestedMessages,
//         enums: nestedEnums,
//         services: nestedServices,
//         serviceNames: nestedServiceNames,
//         shortServices: nestedShortServices,
//         shortServiceNames: nestedShortServiceNames,
//       } = extractComponents(item.nested, fullName)

//       messages = { ...messages, ...nestedMessages }
//       enums = { ...enums, ...nestedEnums }
//       services = { ...services, ...nestedServices }
//       serviceNames = [...serviceNames, ...nestedServiceNames]
//       shortServices = { ...shortServices, ...nestedShortServices }
//       shortServiceNames = [...shortServiceNames, ...nestedShortServiceNames]
//     }
//   }

//   return { messages, enums, services, serviceNames, shortServices, shortServiceNames }
// }

// 方法详情组件的属性
// export interface MethodDetailProps {
//   serviceName: string
//   methodName: string
//   method: any
//   messages: Record<string, any>
//   enums: Record<string, any>
//   timeRange: any
//   getMessageChangeType?: (messageName: string) => ChangeType
//   getFieldChangeType?: (messageName: string, fieldName: string) => ChangeType
//   projectId: number
// }

// 提取组件结果的类型
export interface ExtractComponentsResult {
  messages: Record<string, any>
  enums: Record<string, any>
  services: Record<string, any>
  serviceNames: string[]
  shortServices: Record<string, any>
  shortServiceNames: string[]
  // 新增: 服务名称到 Proto 文件信息的映射
  serviceProtoMapping: Record<string, { fileId: number; fileName: string }>
}

// 提取组件函数
export function extractComponents(
  nested: Record<string, any>,
  fileInfo?: { fileId: number; fileName: string },
): ExtractComponentsResult {
  const messages: Record<string, any> = {}
  const enums: Record<string, any> = {}
  const services: Record<string, any> = {}
  const serviceNames: string[] = []
  const shortServices: Record<string, any> = {}
  const shortServiceNames: string[] = []
  // 新增: 服务名称到 Proto 文件信息的映射
  const serviceProtoMapping: Record<string, { fileId: number; fileName: string }> = {}

  // 递归处理嵌套对象
  function processNested(obj: Record<string, any>, prefix = '') {
    for (const key in obj) {
      const item = obj[key]
      const fullName = prefix ? `${prefix}.${key}` : key

      if (item.fields) {
        // 这是一个消息类型
        messages[fullName] = item
        // 短名称也添加到消息映射中
        // messages[key] = item
      } else if (item.values) {
        // 这是一个枚举类型
        enums[fullName] = item
        // 短名称也添加到枚举映射中
        // enums[key] = item
      } else if (item.methods) {
        // 这是一个服务
        services[fullName] = item
        serviceNames.push(fullName)
        // 短名称也添加到服务映射中
        shortServices[key] = item
        shortServiceNames.push(key)

        // 记录服务所属的 Proto 文件
        if (fileInfo) {
          serviceProtoMapping[fullName] = fileInfo
          serviceProtoMapping[key] = fileInfo // 同时记录短名称的映射
        }
      }
      if (item.nested) {
        // 递归处理嵌套对象
        processNested(item.nested, fullName)
      }
    }
  }

  processNested(nested)

  return {
    messages,
    enums,
    services,
    serviceNames,
    shortServices,
    shortServiceNames,
    serviceProtoMapping,
  }
}
