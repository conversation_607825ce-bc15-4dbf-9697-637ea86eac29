import { Button, Input } from '@mantine/core'
import {
  Spotlight,
  type SpotlightActionData,
  type SpotlightActionGroupData,
  spotlight,
} from '@mantine/spotlight'
import { IconHome, IconDashboard, IconFileText, IconSearch } from '@tabler/icons-react'
import { memo, useMemo, type FC } from 'react'
import type { ExtractComponentsResult } from './common'
import { useNavigate } from '@tanstack/react-router'

export const SearchSpotlight: FC<{
  data: ExtractComponentsResult
  projectId: number
}> = memo(({ data, projectId }) => {
  const navigate = useNavigate()
  const actions: (SpotlightActionGroupData | SpotlightActionData)[] = useMemo(() => {
    // 生成 Spotlight 操作项
    return Object.entries(data.shortServices).map(([serviceName, service]) => {
      return {
        group: serviceName,
        actions: Object.entries(service.methods).map(([methodName, method]) => {
          return {
            id: methodName,
            label: (method as any)?.comment
              ? `${(method as any)?.comment} (${serviceName}/${methodName})`
              : `${serviceName}/${methodName}`,
            // description: (method as any)?.comment,
            onClick: () => {
              navigate({
                to: '/$projectId/api/$service/$method',
                params: { projectId: projectId + '', service: serviceName, method: methodName },
              })
            },
          }
        }),
      }
    })
  }, [data])

  return (
    <>
      <Input
        leftSection={<IconSearch size={20} stroke={1.5} />}
        placeholder="Search..."
        readOnly
        onClick={spotlight.open}
        size="xs"
      />
      <Spotlight
        actions={actions}
        nothingFound="Nothing found..."
        highlightQuery
        limit={10}
        searchProps={{
          leftSection: <IconSearch size={20} stroke={1.5} />,
          placeholder: 'Search...',
        }}
      />
    </>
  )
})
