import { useState, useEffect } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'

interface UseInterfaceMetadataProps {
  projectId?: number
  protoFileId: number
  serviceName: string
  methodName: string
  hasValidProtoFile: boolean
}

export function useInterfaceMetadata({
  projectId,
  protoFileId,
  serviceName,
  methodName,
  hasValidProtoFile,
}: UseInterfaceMetadataProps) {
  // 状态管理
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([])
  const [selectedPathRuleId, setSelectedPathRuleId] = useState<string | null>(null)
  const [isUsingDefaultRule, setIsUsingDefaultRule] = useState<boolean>(false)
  const [customPath, setCustomPath] = useState<string | null>(null)

  // 查询接口元数据
  const {
    data: interfaceData,
    isLoading: isLoadingInterface,
    refetch: refetchInterface,
  } = useQuery(
    trpc.interfaceMetadata.getInterfaceMetadata.queryOptions({
      projectId: projectId || 0,
      protoFileId,
      serviceName,
      methodName,
    }),
  )

  // 查询项目标签
  const { data: tags = [], isLoading: isLoadingTags } = useQuery(
    trpc.interfaceMetadata.getProjectTags.queryOptions({
      projectId: projectId || 0,
    }),
  )

  // 查询项目路径规则
  const { data: pathRules = [], isLoading: isLoadingPathRules } = useQuery(
    trpc.interfaceMetadata.getProjectPathRules.queryOptions({
      projectId: projectId || 0,
    }),
  )

  // 更新接口元数据
  const updateMetadata = useMutation(
    trpc.interfaceMetadata.updateInterfaceMetadata.mutationOptions({
      onSuccess: () => {
        refetchInterface()
        notifications.show({ title: '更新成功', message: '接口设置已更新', color: 'green' })
      },
      onError: error => {
        notifications.show({
          title: '更新失败',
          message: error.message || '无法更新接口设置',
          color: 'red',
        })
      },
    }),
  )

  // 初始化选中的标签和路径规则
  useEffect(() => {
    if (interfaceData) {
      // 设置当前选中的标签
      const currentTagIds = interfaceData.tags?.map(tag => tag.id.toString()) || []
      setSelectedTagIds(currentTagIds)

      // 设置当前选中的路径规则
      setSelectedPathRuleId(
        interfaceData.metadata?.pathRuleId ? interfaceData.metadata.pathRuleId.toString() : null,
      )

      // 设置规则应用后的路径
      setCustomPath(interfaceData.path)

      // 检查是否使用默认规则
      setIsUsingDefaultRule(!interfaceData.metadata?.pathRuleId && !!interfaceData.defaultPathRule)
    }
  }, [interfaceData])

  // 处理标签变更
  const handleTagsChange = (values: string[]) => {
    setSelectedTagIds(values)

    if (hasValidProtoFile && projectId) {
      updateMetadata.mutate({
        projectId,
        protoFileId,
        serviceName,
        methodName,
        tagIds: values.map(id => parseInt(id)),
        pathRuleId: selectedPathRuleId ? parseInt(selectedPathRuleId) : null,
      })
    }
  }

  // 处理路径规则变更
  const handlePathRuleChange = (value: string | null) => {
    setSelectedPathRuleId(value)

    if (hasValidProtoFile && projectId) {
      updateMetadata.mutate({
        projectId,
        protoFileId,
        serviceName,
        methodName,
        pathRuleId: value ? parseInt(value) : null,
        tagIds: selectedTagIds.map(id => parseInt(id)),
      })
    }
  }

  // 标签选择器选项
  const tagOptions = tags.map(tag => ({
    value: tag.id.toString(),
    label: tag.name,
    color: tag.color || undefined,
  }))

  // 获取当前选中的标签对象列表
  const selectedTags = tags.filter(tag => selectedTagIds.includes(tag.id.toString()))

  // 标签展示样式
  const pathRuleDisplayColor = isUsingDefaultRule ? 'blue' : 'green'
  const pathRuleDisplayText = isUsingDefaultRule
    ? `默认${interfaceData?.defaultPathRule?.name ? `: ${interfaceData.defaultPathRule.name}` : ''}`
    : pathRules.find(opt => opt.id.toString() === selectedPathRuleId)?.name || '默认'

  return {
    // 数据
    interfaceData,
    tags,
    pathRules,
    selectedTags,
    selectedTagIds,
    selectedPathRuleId,
    customPath,
    isUsingDefaultRule,

    // 状态
    isLoadingInterface,
    isLoadingTags,
    isLoadingPathRules,
    isUpdating: updateMetadata.isPending,

    // 选项
    tagOptions,
    pathRuleDisplayColor,
    pathRuleDisplayText,

    // 方法
    handleTagsChange,
    handlePathRuleChange,
    setCustomPath,
  }
}
