import React, { useEffect, useState } from 'react'
import {
  Group,
  Tooltip,
  Text,
  Menu,
  ActionIcon,
  Indicator,
  Divider,
  Switch,
  NumberInput,
  Button,
} from '@mantine/core'
import { IconGitCompare } from '@tabler/icons-react'
import {
  type TimeRange,
  type ComparisonSettings,
  DEFAULT_COMPARISON_DAYS,
  getComparisonSettings,
  saveComparisonSettings,
} from '@/utils/diffUtils'

interface TimeRangeSelectorProps {
  value: TimeRange | null
  onChange: (value: TimeRange | null) => void
  isLoading?: boolean
  comparisonSettings?: ComparisonSettings
  onComparisonChange?: (settings: ComparisonSettings) => void
}

export const TimeRangeSelector: React.FC<TimeRangeSelectorProps> = ({
  value,
  onChange,
  isLoading = false,
  comparisonSettings: externalComparisonSettings,
  onComparisonChange,
}) => {
  // 内部状态，如果没有提供外部状态
  const [internalComparisonSettings, setInternalComparisonSettings] =
    useState<ComparisonSettings>(getComparisonSettings())

  // 使用外部状态或内部状态
  const comparisonSettings = externalComparisonSettings || internalComparisonSettings

  // 初始化时从 localStorage 加载设置
  useEffect(() => {
    if (!externalComparisonSettings) {
      setInternalComparisonSettings(getComparisonSettings())
    }

    // 如果当前有值，初始化自定义天数
    if (typeof value === 'number') {
      setCustomDays(value)
    }
  }, [externalComparisonSettings, value])

  // 更新对比设置
  const updateComparisonSettings = (settings: ComparisonSettings) => {
    if (onComparisonChange) {
      onComparisonChange(settings)
    } else {
      setInternalComparisonSettings(settings)
      saveComparisonSettings(settings)
    }
  }

  // 切换对比功能
  const toggleComparison = () => {
    updateComparisonSettings({
      ...comparisonSettings,
      enabled: !comparisonSettings.enabled,
    })
  }

  // 切换仅显示变更接口
  const toggleShowChangedOnly = () => {
    updateComparisonSettings({
      ...comparisonSettings,
      showChangedOnly: !comparisonSettings.showChangedOnly,
    })
  }

  // 设置对比天数
  const setComparisonDays = (days: number) => {
    updateComparisonSettings({
      enabled: true,
      days,
      showChangedOnly: comparisonSettings.showChangedOnly,
    })

    // 同时更新时间范围
    onChange(days)
  }

  // 自定义天数输入
  const [customDays, setCustomDays] = useState<number | ''>(7)

  // 处理自定义天数变更
  const handleCustomDaysChange = (value: string | number) => {
    setCustomDays(value === '' ? '' : Number(value))
  }

  // 应用自定义天数
  const applyCustomDays = () => {
    if (typeof customDays === 'number' && customDays > 0) {
      setComparisonDays(customDays)
    }
  }

  return (
    <Group gap={4} wrap="nowrap">
      {/* <SegmentedControl
        size="xs"
        value={value || 'none'}
        onChange={val => onChange(val === 'none' ? null : (val as TimeRange))}
        data={[
          {
            label: <IconCalendarOff size={16} stroke={1.5} />,
            value: 'none',
          },
          {
            label: <IconCalendar size={16} stroke={1.5} />,
            value: 'day',
          },
          {
            label: <IconCalendarWeek size={16} stroke={1.5} />,
            value: 'week',
          },
          {
            label: <IconCalendarMonth size={16} stroke={1.5} />,
            value: 'month',
          },
        ]}
        disabled={isLoading}
      />
      {isLoading && <Loader size="xs" />} */}

      {/* 对比功能按钮 */}
      <Menu position="right-start" withArrow>
        <Menu.Target>
          <Tooltip label={`展示近期 ${comparisonSettings.days} 天内的 API 变更`}>
            <Indicator
              disabled={!comparisonSettings.enabled}
              label={comparisonSettings.enabled ? `${comparisonSettings.days}天` : null}
              size={16}
              color="gray"
              withBorder
            >
              <ActionIcon
                variant="subtle"
                size="md"
                color={comparisonSettings.enabled ? 'blue' : 'gray'}
                aria-label="对比设置"
              >
                <IconGitCompare size={20} stroke={1.5} />
              </ActionIcon>
            </Indicator>
          </Tooltip>
        </Menu.Target>

        <Menu.Dropdown>
          <Menu.Label>对比设置</Menu.Label>

          <Menu.Item closeMenuOnClick={false}>
            <Group justify="space-between">
              <Text size="sm">启用对比</Text>
              <Switch checked={comparisonSettings.enabled} onChange={toggleComparison} size="sm" />
            </Group>
          </Menu.Item>

          <Menu.Item closeMenuOnClick={false}>
            <Group justify="space-between">
              <Text size="sm">仅显示变更接口</Text>
              <Switch
                checked={comparisonSettings.showChangedOnly}
                onChange={toggleShowChangedOnly}
                size="sm"
                disabled={!comparisonSettings.enabled}
              />
            </Group>
          </Menu.Item>

          <Divider />

          <Menu.Label>对比天数</Menu.Label>
          {DEFAULT_COMPARISON_DAYS.map(days => (
            <Menu.Item
              key={days}
              onClick={() => setComparisonDays(days)}
              rightSection={
                comparisonSettings.days === days && comparisonSettings.enabled ? '✓' : null
              }
            >
              对比近 {days} 天
            </Menu.Item>
          ))}

          <Menu.Item closeMenuOnClick={false} component="div">
            <Group gap="xs">
              <NumberInput
                size="xs"
                min={1}
                max={365}
                value={customDays}
                onChange={handleCustomDaysChange}
                style={{ width: 80 }}
                placeholder="自定义"
              />
              <Button size="xs" onClick={applyCustomDays} component="div">
                应用
              </Button>
            </Group>
          </Menu.Item>

          <Divider />

          <Menu.Item
            color="red"
            onClick={() =>
              updateComparisonSettings({
                enabled: false,
                days: comparisonSettings.days,
                showChangedOnly: comparisonSettings.showChangedOnly,
              })
            }
            disabled={!comparisonSettings.enabled}
          >
            关闭对比
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>

      {/* 帮助提示 */}
      {/* <Tooltip
        label={
          <Box maw={250}>
            <Text size="xs" fw={500} mb={5}>
              变更时间范围
            </Text>
            <Text size="xs">
              - 无图标: 最新版本
              <br />
              - <IconCalendar size={12} stroke={1.5} /> 最近一天
              <br />
              - <IconCalendarWeek size={12} stroke={1.5} /> 最近一周
              <br />
              - <IconCalendarMonth size={12} stroke={1.5} /> 最近一月
              <br />
              <br />
              变更颜色:
              <br />
              - 绿色: 新增的内容
              <br />
              - 蓝色: 修改的内容
              <br />- 红色: 删除的内容
              <br />
              <br />
              对比功能:
              <br />
              - 点击对比图标可以设置对比天数
              <br />- 红色数字表示当前对比的天数
            </Text>
          </Box>
        }
        position="bottom"
        withArrow
        multiline
      >
        <IconInfoCircle size={16} style={{ cursor: 'help' }} />
      </Tooltip> */}
    </Group>
  )
}

export default TimeRangeSelector
