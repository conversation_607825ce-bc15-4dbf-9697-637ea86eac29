import React from 'react'
import { useInterfaceMetadata } from '../hooks/useInterfaceMetadata'
import { TagsEditor } from './TagsEditor'

interface StandaloneTagsEditorProps {
  projectId?: number
  protoFileId: number
  serviceName: string
  methodName: string
  hasValidProtoFile: boolean
}

export const StandaloneTagsEditor: React.FC<StandaloneTagsEditorProps> = ({
  projectId,
  protoFileId,
  serviceName,
  methodName,
  hasValidProtoFile,
}) => {
  const { selectedTags, tagOptions, isLoadingTags, isUpdating, handleTagsChange } =
    useInterfaceMetadata({
      projectId,
      protoFileId,
      serviceName,
      methodName,
      hasValidProtoFile,
    })

  if (!hasValidProtoFile || !projectId) {
    return null
  }

  return (
    <TagsEditor
      selectedTags={selectedTags}
      tagOptions={tagOptions}
      isLoading={isLoadingTags}
      isUpdating={isUpdating}
      onTagsChange={handleTagsChange}
    />
  )
}
