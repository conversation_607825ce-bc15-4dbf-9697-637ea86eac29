import React from 'react'
import { Group, Text, Code, Paper } from '@mantine/core'
import { IconInfoCircle } from '@tabler/icons-react'
import { TagsEditor } from './TagsEditor'
import { PathRuleEditor } from './PathRuleEditor'
import { useInterfaceMetadata } from '../hooks/useInterfaceMetadata'

interface InterfaceMetadataEditorProps {
  projectId?: number
  protoFileId: number
  serviceName: string
  methodName: string
  hasValidProtoFile: boolean
}

export const InterfaceMetadataEditor: React.FC<InterfaceMetadataEditorProps> = ({
  projectId,
  protoFileId,
  serviceName,
  methodName,
  hasValidProtoFile,
}) => {
  const {
    selectedTags,
    tagOptions,
    pathRuleOptions,
    selectedPathRuleId,
    customPath,
    isUsingDefaultRule,
    pathRuleDisplayColor,
    pathRuleDisplayText,
    isLoadingTags,
    isLoadingPathRules,
    isUpdating,
    handleTagsChange,
    handlePathRuleChange,
  } = useInterfaceMetadata({
    projectId,
    protoFileId,
    serviceName,
    methodName,
    hasValidProtoFile,
  })

  if (!hasValidProtoFile || !projectId) {
    return (
      <>
        <Code>{customPath || `/${serviceName}/${methodName}`}</Code>
        {projectId && !hasValidProtoFile && (
          <Paper withBorder p="xs" mt="md">
            <Group gap="sm">
              <IconInfoCircle size={18} color="blue" />
              <Text size="sm">
                无法加载接口设置，系统无法确定此接口所属的 Proto
                文件。您可以在"接口设置"标签页中为此接口配置标签和路径规则。
              </Text>
            </Group>
          </Paper>
        )}
      </>
    )
  }

  return (
    <>
      {/* 标签编辑器 */}
      <TagsEditor
        selectedTags={selectedTags}
        tagOptions={tagOptions}
        isLoading={isLoadingTags}
        isUpdating={isUpdating}
        onTagsChange={handleTagsChange}
      />

      {/* 路径编辑器 */}
      <Group align="center">
        <Text fw={500}>路径:</Text>
        <PathRuleEditor
          selectedPathRuleId={selectedPathRuleId}
          pathRuleOptions={pathRuleOptions}
          customPath={customPath}
          isUsingDefaultRule={isUsingDefaultRule}
          pathRuleDisplayColor={pathRuleDisplayColor}
          pathRuleDisplayText={pathRuleDisplayText}
          isLoading={isLoadingPathRules}
          isUpdating={isUpdating}
          onPathRuleChange={handlePathRuleChange}
        />
      </Group>
    </>
  )
}
