import React, { useState } from 'react'
import { Group, Badge, Popover, ActionIcon, MultiSelect } from '@mantine/core'
import { IconEdit } from '@tabler/icons-react'

export interface Tag {
  id: number
  name: string
  color: string | null
  icon?: string | null
  projectId?: number
  createdAt?: string
  updatedAt?: string
}

export interface TagOption {
  value: string
  label: string
  color?: string
}

interface TagsEditorProps {
  selectedTags: Tag[]
  tagOptions: TagOption[]
  isLoading: boolean
  isUpdating: boolean
  onTagsChange: (values: string[]) => void
}

export const TagsEditor: React.FC<TagsEditorProps> = ({
  selectedTags,
  tagOptions,
  isLoading,
  isUpdating,
  onTagsChange,
}) => {
  const [popoverOpened, setPopoverOpened] = useState<boolean>(false)
  const selectedTagIds = selectedTags.map(tag => tag.id.toString())

  return (
    <Group gap="xs">
      {selectedTags.map(tag => (
        <Badge key={tag.id} color={tag.color || 'blue'} size="sm" variant="filled">
          {tag.name}
        </Badge>
      ))}

      <Popover
        position="bottom"
        withArrow
        shadow="md"
        opened={popoverOpened}
        onChange={setPopoverOpened}
      >
        <Popover.Target>
          <Badge
            size="xs"
            color="gray"
            variant="light"
            onClick={() => setPopoverOpened(o => !o)}
            title="编辑标签"
          >
            <IconEdit size={12} />
          </Badge>
        </Popover.Target>
        <Popover.Dropdown>
          <MultiSelect
            data={tagOptions}
            value={selectedTagIds}
            onChange={values => {
              onTagsChange(values)
              setPopoverOpened(false)
            }}
            placeholder="选择标签"
            searchable
            clearable
            disabled={isLoading || isUpdating}
            style={{ minWidth: 200 }}
            nothingFoundMessage="没有找到标签"
          />
        </Popover.Dropdown>
      </Popover>
    </Group>
  )
}
