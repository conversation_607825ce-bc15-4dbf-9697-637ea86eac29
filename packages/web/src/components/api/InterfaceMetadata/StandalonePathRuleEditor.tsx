import React from 'react'
import { Group, Text, Code, Paper } from '@mantine/core'
import { IconInfoCircle } from '@tabler/icons-react'
import { useInterfaceMetadata } from '../hooks/useInterfaceMetadata'
import { PathRuleEditor } from './PathRuleEditor'

interface StandalonePathRuleEditorProps {
  projectId?: number
  protoFileId: number
  serviceName: string
  methodName: string
  hasValidProtoFile: boolean
}

export const StandalonePathRuleEditor: React.FC<StandalonePathRuleEditorProps> = ({
  projectId,
  protoFileId,
  serviceName,
  methodName,
  hasValidProtoFile,
}) => {
  const {
    customPath,
    pathRules,
    selectedPathRuleId,
    isUsingDefaultRule,
    pathRuleDisplayColor,
    pathRuleDisplayText,
    isLoadingPathRules,
    isUpdating,
    handlePathRuleChange,
  } = useInterfaceMetadata({
    projectId,
    protoFileId,
    serviceName,
    methodName,
    hasValidProtoFile,
  })

  if (!hasValidProtoFile || !projectId) {
    return (
      <>
        <Code>{customPath || `/${serviceName}/${methodName}`}</Code>
        {projectId && !hasValidProtoFile && (
          <Paper withBorder p="xs" mt="md">
            <Group gap="sm">
              <IconInfoCircle size={18} color="blue" />
              <Text size="sm">
                无法加载接口设置，系统无法确定此接口所属的 Proto
                文件。您可以在"接口设置"标签页中为此接口配置标签和路径规则。
              </Text>
            </Group>
          </Paper>
        )}
      </>
    )
  }

  return (
    <Group align="center">
      {/* <Text fw={500}>路径:</Text> */}
      <PathRuleEditor
        selectedPathRuleId={selectedPathRuleId}
        pathRules={pathRules}
        customPath={customPath || `/${serviceName}/${methodName}`}
        isUsingDefaultRule={isUsingDefaultRule}
        pathRuleDisplayColor={pathRuleDisplayColor}
        pathRuleDisplayText={pathRuleDisplayText}
        isLoading={isLoadingPathRules}
        isUpdating={isUpdating}
        onPathRuleChange={handlePathRuleChange}
      />
    </Group>
  )
}
