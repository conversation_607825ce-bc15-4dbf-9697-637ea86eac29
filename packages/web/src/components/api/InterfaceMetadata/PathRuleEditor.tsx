import React, { useState } from 'react'
import { Group, Badge, Popover, Text, Code, Select, Menu } from '@mantine/core'
import { IconCheck, IconRoute } from '@tabler/icons-react'
import type { ApiData } from '@/utils/trpc'

export interface PathRule {
  id: number
  name: string
}

interface PathRuleEditorProps {
  selectedPathRuleId: string | null
  pathRules: ApiData['pathRule']['getPathRules']
  customPath: string | null
  isUsingDefaultRule: boolean
  pathRuleDisplayColor: string
  pathRuleDisplayText: string
  isLoading: boolean
  isUpdating: boolean
  onPathRuleChange: (value: string | null) => void
}

export const PathRuleEditor: React.FC<PathRuleEditorProps> = ({
  selectedPathRuleId,
  customPath,
  pathRules,
  isUsingDefaultRule,
  pathRuleDisplayColor,
  pathRuleDisplayText,
  isLoading,
  isUpdating,
  onPathRuleChange,
}) => {
  const [popoverOpened, setPopoverOpened] = useState<boolean>(false)

  return (
    <Group gap="xs" wrap="nowrap">
      <Menu
        position="bottom"
        withArrow
        shadow="md"
        opened={popoverOpened}
        onChange={setPopoverOpened}
      >
        <Menu.Target>
          <Badge
            color={pathRuleDisplayColor}
            variant="light"
            style={{ cursor: 'pointer' }}
            onClick={() => setPopoverOpened(o => !o)}
            leftSection={<IconRoute size={12} />}
          >
            {pathRuleDisplayText}
          </Badge>
        </Menu.Target>
        <Menu.Dropdown>
          <Menu.Label>选择接口路径规则</Menu.Label>
          <Menu.Item
            onClick={() => {
              onPathRuleChange(null)
              setPopoverOpened(false)
            }}
          >
            默认
          </Menu.Item>
          <Menu.Divider />
          {pathRules.map(option => (
            <Menu.Item
              key={option.id}
              value={option.id}
              rightSection={
                selectedPathRuleId === option.id.toString() ? <IconCheck size={12} /> : null
              }
              onClick={() => {
                onPathRuleChange(option.id.toString())
                setPopoverOpened(false)
              }}
            >
              <div>
                <Text>{option.name}</Text>
                <Text size="xs" c="dimmed">
                  {option.pattern}
                </Text>
              </div>
            </Menu.Item>
          ))}
          {pathRules.length === 0 && (
            <Menu.Item>
              <Text size="xs" c="dimmed">
                项目暂未配置可用的路径规则
              </Text>
            </Menu.Item>
          )}
        </Menu.Dropdown>
      </Menu>

      {customPath && <Code>{customPath}</Code>}
    </Group>
  )
}
