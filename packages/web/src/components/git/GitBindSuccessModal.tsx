import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, But<PERSON>, Alert, ThemeIcon, Group } from '@mantine/core'
import { useNavigate } from '@tanstack/react-router'
import { IconCheck, IconBrandGithub, IconPlus, IconSettings } from '@tabler/icons-react'
import type { MC } from '@/utils/modal'

// 定义模态框选项类型
interface GitBindSuccessModalOptions {
  onNavigateToProjects?: () => void
  onNavigateToSettings?: () => void
}

// Git绑定成功模态框组件
export const GitBindSuccessModal: MC<GitBindSuccessModalOptions> = ({
  onNavigateToProjects,
  onNavigateToSettings,
  finish,
  bind,
}) => {
  const navigate = useNavigate()

  const handleNavigateToProjects = () => {
    finish(undefined)
    if (onNavigateToProjects) {
      onNavigateToProjects()
    } else {
      navigate({ to: '/projects' })
    }
  }

  const handleNavigateToSettings = () => {
    finish(undefined)
    if (onNavigateToSettings) {
      onNavigateToSettings()
    } else {
      navigate({ to: '/settings' })
    }
  }

  return (
    <Modal
      opened={bind.visible}
      onClose={bind.onClose}
      title={
        <Group gap="sm">
          <ThemeIcon size="sm" color="green" variant="light">
            <IconCheck size={16} />
          </ThemeIcon>
          <Text fw={500}>Git账号绑定成功</Text>
        </Group>
      }
      size="md"
      centered
    >
      <Stack gap="lg">
        <Alert
          icon={<IconBrandGithub size="1.5rem" />}
          title="恭喜！绑定成功完成"
          color="green"
          variant="light"
        >
          <Stack gap="xs">
            <Text>您的Git账号已成功绑定到系统中。</Text>
            <Text size="sm" c="dimmed">
              现在您可以开始添加和管理Proto项目，系统将能够：
            </Text>
            <ul style={{ margin: 0, paddingLeft: '1.2rem', fontSize: '0.875rem' }}>
              <li>从Git仓库同步Proto文件</li>
              <li>跟踪文件变更历史</li>
              <li>管理项目权限和成员</li>
              <li>生成API文档和接口定义</li>
            </ul>
          </Stack>
        </Alert>

        <Stack gap="sm">
          <Button
            variant="filled"
            color="blue"
            leftSection={<IconPlus size={16} />}
            onClick={handleNavigateToProjects}
            fullWidth
          >
            开始添加项目
          </Button>

          <Button
            variant="subtle"
            leftSection={<IconSettings size={16} />}
            onClick={handleNavigateToSettings}
            fullWidth
          >
            返回设置页面
          </Button>
        </Stack>
      </Stack>
    </Modal>
  )
}
