import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Alert, ThemeIcon, Group, List } from '@mantine/core'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { IconAlertTriangle, IconBrandGithub, IconX } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { trpc } from '@/utils/trpc'
import type { MC } from '@/utils/modal'

// 定义模态框选项类型
interface GitUnbindConfirmModalOptions {
  onSuccess?: () => void
}

// Git解绑确认模态框组件
export const GitUnbindConfirmModal: MC<GitUnbindConfirmModalOptions> = ({
  onSuccess,
  finish,
  cancel,
  bind,
}) => {
  const queryClient = useQueryClient()

  // 解绑Git账号的mutation
  const unbindGitAccount = useMutation(trpc.user.unbindGitAccount.mutationOptions())

  const handleConfirmUnbind = async () => {
    try {
      await unbindGitAccount.mutateAsync()

      // 刷新用户信息
      await queryClient.invalidateQueries({
        queryKey: [['user', 'getCurrentUser']],
      })

      notifications.show({
        title: '解绑成功',
        message: 'Git账号已成功解绑',
        color: 'green',
      })

      if (onSuccess) {
        onSuccess()
      }

      finish(undefined)
    } catch (error: any) {
      console.error('Git账号解绑失败:', error)
      notifications.show({
        title: '解绑失败',
        message: error.message || 'Git账号解绑失败，请重试',
        color: 'red',
      })
    }
  }

  return (
    <Modal
      opened={bind.visible}
      onClose={bind.onClose}
      title={
        <Group gap="sm">
          <ThemeIcon size="sm" color="orange" variant="light">
            <IconAlertTriangle size={16} />
          </ThemeIcon>
          <Text fw={500}>确认解绑Git账号</Text>
        </Group>
      }
      size="md"
      centered
    >
      <Stack gap="lg">
        <Alert
          icon={<IconBrandGithub size="1.5rem" />}
          title="解绑前请注意"
          color="orange"
          variant="light"
        >
          <Stack gap="xs">
            <Text>解绑Git账号后，以下功能将无法使用：</Text>
            <List size="sm" spacing="xs">
              <List.Item>无法添加新的Proto项目</List.Item>
              <List.Item>无法同步现有项目的最新文件</List.Item>
              <List.Item>无法查看文件变更历史</List.Item>
              <List.Item>无法访问私有仓库</List.Item>
            </List>
            <Text size="sm" c="dimmed" mt="xs">
              现有项目数据不会丢失，但需要重新绑定Git账号才能继续使用相关功能。
            </Text>
          </Stack>
        </Alert>

        <Stack gap="sm">
          <Button
            variant="filled"
            color="red"
            leftSection={<IconX size={16} />}
            onClick={handleConfirmUnbind}
            loading={unbindGitAccount.isPending}
            fullWidth
          >
            确认解绑
          </Button>

          <Button variant="subtle" onClick={cancel} disabled={unbindGitAccount.isPending} fullWidth>
            取消
          </Button>
        </Stack>
      </Stack>
    </Modal>
  )
}
