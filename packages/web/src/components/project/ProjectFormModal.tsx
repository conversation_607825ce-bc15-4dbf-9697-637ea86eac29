import {
  Button,
  Group,
  Stack,
  Select,
  Text,
  Loader,
  Alert,
  Paper,
  Code,
  Divider,
  Box,
  useMantineTheme,
  useMantineColorScheme,
} from '@mantine/core'
import { notifications } from '@mantine/notifications'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useForm, zodResolver } from '@mantine/form'
import { z } from 'zod'
import { trpc } from '@/utils/trpc'
import { Modal } from '@mantine/core'
import { type MC } from '@/utils/modal'
import { useState, useEffect } from 'react'
import { IconAlertCircle, IconInfoCircle, IconArrowRight } from '@tabler/icons-react'

// 定义表单 schema
const formSchema = z.object({
  repository: z.string().min(1, '仓库名称不能为空'),
  module: z.string().min(1, '模块名称不能为空'),
})

// 定义自定义选项类型
interface ProjectFormOptions {
  mode: 'create' | 'update'
  projectId?: number
  initialData?: {
    title: string
    desc?: string
  }
}

// 定义返回数据类型
interface ProjectData {
  id: number
  repository: string
  project: string
}

// 项目表单模态框组件
const ProjectFormModal: MC<ProjectFormOptions, ProjectData> = props => {
  const { mode } = props
  const [selectedRepository, setSelectedRepository] = useState<string>('')

  // 创建项目的 mutation
  const addOrUpdateModule = useMutation(trpc.project.add.mutationOptions())

  // 获取 rick_proto 项目组下的所有项目
  const {
    data: groupData,
    isLoading: isLoadingProjects,
    error: groupDataError,
  } = useQuery(
    trpc.repository.getAvailableRepositories.queryOptions(undefined, { staleTime: 1000 * 60 }),
  )

  const modules = useQuery(
    trpc.repository.getRepositoryAvailableProjects.queryOptions(
      { repository: selectedRepository },
      {
        enabled: !!selectedRepository,
      },
    ),
  )

  // 初始化表单
  const form = useForm({
    initialValues: {
      repository: '',
      module: '',
    },
    validate: zodResolver(formSchema),
  })

  // 当选择仓库时，更新状态并清空模块选择
  const handleRepositoryChange = (value: string | null) => {
    if (!value) return

    form.setFieldValue('repository', value)
    form.setFieldValue('module', '')
    setSelectedRepository(value)
  }

  // 处理表单提交
  const handleSubmit = async (values: typeof form.values) => {
    try {
      if (mode === 'create') {
        // 创建新项目
        await addOrUpdateModule.mutateAsync({
          repository: values.repository,
          project: values.module,
        })

        // 调用 finish 方法关闭弹窗并返回数据
        props.finish(
          {
            id: 0, // 实际ID会在列表刷新时获取
            repository: values.repository,
            project: values.module,
          },
          '项目已成功创建',
        )
      }
      // 编辑功能暂时注释掉，后续实现
      // else if (mode === 'update' && projectId) {
      //   // 更新项目
      //   const updatedProject = await updateProject.mutateAsync({
      //     id: projectId,
      //     title: values.title,
      //     desc: values.desc,
      //   })

      //   // 调用 finish 方法关闭弹窗并返回数据
      //   props.finish(
      //     {
      //       id: updatedProject.id,
      //       title: updatedProject.title,
      //       desc: updatedProject.desc,
      //     },
      //     '项目信息已成功更新',
      //   )
      // }

      // 重置表单
      form.reset()
      setSelectedRepository('')
    } catch (error) {
      console.error(`${mode === 'create' ? '创建' : '更新'}项目失败:`, error)
      notifications.show({
        title: `${mode === 'create' ? '创建' : '更新'}失败`,
        message:
          (error as any)?.message || `无法${mode === 'create' ? '创建项目' : '更新项目信息'}`,
        color: 'red',
      })
    }
  }

  // 准备仓库选项数据
  const repositoryOptions =
    groupData?.map(path => ({
      value: path,
      label: path,
    })) || []

  // 准备模块选项数据
  const moduleOptions =
    modules.data?.map(module => ({
      value: module,
      label: module,
    })) || []

  const modalTitle = mode === 'create' ? '导入新项目' : '编辑项目信息'
  const submitButtonText = mode === 'create' ? '导入' : '保存'
  const isLoading = addOrUpdateModule.isPending

  const colorScheme = useMantineColorScheme()
  const isDark = colorScheme.colorScheme === 'dark'

  return (
    <Modal opened={props.bind.visible} onClose={props.bind.onClose} title={modalTitle} size="lg">
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          {/* 添加详细说明区域 */}
          <Paper p="md" bg={isDark ? 'gray.8' : 'blue.0'} radius="md">
            <Group gap="xs" mb="sm">
              <IconInfoCircle size="1.2rem" color="var(--mantine-color-blue-6)" />
              <Text fw={600} c="blue.7">
                导入说明
              </Text>
            </Group>

            <Text size="sm" c="dimmed" mb="md">
              根据您的 Proto 文件中的 package 声明来填写下面的表单。Package 格式遵循以下规则：
            </Text>

            <Box mb="md">
              <Group gap="0" align="center" mb="xs">
                <Code c="blue.7" fw={600}>
                  trpc
                </Code>
                <Text c="dimmed">.</Text>
                <Code c="green.7" fw={600}>
                  仓库名
                </Code>
                <Text c="dimmed">.</Text>
                <Code c="orange.7" fw={600}>
                  模块名
                </Code>
              </Group>
              <Text size="xs" c="dimmed">
                固定前缀 + 仓库标识 + 模块标识
              </Text>
            </Box>

            <Divider my="sm" />

            <Text size="sm" fw={500} mb="xs">
              示例：
            </Text>
            <Group gap="md" mb="sm">
              <Box>
                <Text size="xs" c="dimmed" mb={2}>
                  Proto Package:
                </Text>
                <Code>trpc.user_service.auth</Code>
              </Box>
              <IconArrowRight size="1rem" color="var(--mantine-color-gray-5)" />
              <Box>
                <Text size="xs" c="dimmed" mb={2}>
                  表单填写:
                </Text>
                <Group gap="xs">
                  <Text size="xs">
                    仓库: <Code c="green.7">user_service</Code>
                  </Text>
                  <Text size="xs">
                    模块: <Code c="orange.7">auth</Code>
                  </Text>
                </Group>
              </Box>
            </Group>

            {/* 实时预览当前选择 */}
            {(form.values.repository || form.values.module) && (
              <>
                <Divider my="sm" />
                <Box>
                  <Text size="xs" c="dimmed" mb={4}>
                    当前选择对应的 Package:
                  </Text>
                  <Code c="blue.7" fw={600}>
                    trpc
                    {form.values.repository && (
                      <>
                        .
                        <span style={{ color: 'var(--mantine-color-green-7)' }}>
                          {form.values.repository}
                        </span>
                      </>
                    )}
                    {form.values.module && (
                      <>
                        .
                        <span style={{ color: 'var(--mantine-color-orange-7)' }}>
                          {form.values.module}
                        </span>
                      </>
                    )}
                  </Code>
                </Box>
              </>
            )}
          </Paper>

          {groupDataError && (
            <Alert icon={<IconAlertCircle size="1rem" />} color="red">
              {groupDataError.message}
            </Alert>
          )}

          <Select
            label="仓库"
            description="选择 Proto Package 中第二段的仓库标识符"
            placeholder="例如：user_service, order_service, payment_service"
            data={repositoryOptions}
            searchable
            required
            value={form.values.repository}
            onChange={handleRepositoryChange}
            error={form.errors.repository}
            limit={50}
            rightSection={isLoadingProjects ? <Loader size="xs" /> : null}
          />

          <Select
            label="模块"
            description="选择 Proto Package 中第三段的模块标识符"
            placeholder={
              !selectedRepository
                ? '请先选择仓库'
                : modules.isFetching
                  ? '加载中...'
                  : '例如：auth, user, profile'
            }
            data={moduleOptions}
            searchable
            required
            disabled={!selectedRepository || modules.isFetching}
            {...form.getInputProps('module')}
          />

          <Group justify="flex-end">
            <Button variant="default" onClick={props.cancel}>
              取消
            </Button>
            <Button
              type="submit"
              loading={isLoading}
              disabled={!form.values.repository || !form.values.module}
            >
              {submitButtonText}
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  )
}

export { ProjectFormModal }
