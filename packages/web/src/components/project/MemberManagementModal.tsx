import { type ReactNode } from 'react'
import { useState, useEffect } from 'react'
import {
  Modal,
  Button,
  Group,
  Text,
  Stack,
  TextInput,
  Select,
  Alert,
  Textarea,
  Divider,
  Checkbox,
  Badge,
  Box,
  Tooltip,
  Tabs,
  MultiSelect,
  Loader,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import {
  IconAlertCircle,
  IconTrash,
  IconEdit,
  IconCheck,
  IconX,
  IconUsers,
  IconFileImport,
} from '@tabler/icons-react'
import { z } from 'zod'
import { useForm, zodResolver } from '@mantine/form'
import type { MC } from '@/utils/modal'

// 定义角色类型
type Role = 'READ' | 'WRITE' | 'OWNER'

// 定义成员类型
interface Member {
  username: string
  role: Role
  chineseName?: string | null
}

// 定义模态框属性
interface MemberManagementModalProps {
  projectId: number
  mode: 'add' | 'edit' | 'delete'
  initialData?: Member
  currentMembers?: Member[]
  onSuccess?: () => void
}

// 修改批量添加表单类型，添加 selectedUsers 字段
interface BatchAddForm {
  usernames: string
  role: Role
  selectedUsers: string[] // 新增字段，用于下拉选择
}

// 定义编辑表单类型
interface EditForm {
  username: string
  role: Role
}

// 定义删除表单类型
interface DeleteForm {
  username: string
  confirmDelete: boolean
}

// 解析用户名的返回类型
interface ParsedUser {
  username: string
  chineseName?: string | null
}

// 成员管理模态框组件
export const MemberManagementModal: MC<MemberManagementModalProps> = ({
  projectId,
  mode,
  initialData,
  currentMembers = [],
  onSuccess,
  finish,
  cancel,
  bind,
}) => {
  const [error, setError] = useState<string | null>(null)
  const [parsedMembers, setParsedMembers] = useState<Member[]>([])
  const [showPreview, setShowPreview] = useState(false)
  const [activeTab, setActiveTab] = useState<string | null>('select') // 默认选择"下拉选择"选项卡

  // 获取所有用户列表
  const { data: users, isLoading: isLoadingUsers } = useQuery(trpc.user.list.queryOptions())

  // 修改批量添加成员表单，添加 selectedUsers 字段
  const batchAddForm = useForm<BatchAddForm>({
    initialValues: {
      usernames: '',
      role: 'READ',
      selectedUsers: [],
    },
    validate: {
      usernames: (value, values) => {
        if (activeTab !== 'import') return null

        if (!value.trim()) {
          return '请输入要导入的用户数据'
        }

        // 验证格式是否正确
        const { parsedUsers, invalidEntries } = parseUsernames(value)
        if (parsedUsers.length === 0) {
          return '未找到有效的用户数据，请检查格式'
        }

        if (invalidEntries.length > 0) {
          return `发现 ${invalidEntries.length} 个格式错误的条目，请检查格式`
        }

        return null
      },
      role: value => (value ? null : '请选择角色'),
      selectedUsers: (value, values) => {
        if (activeTab !== 'select') return null

        if (value.length === 0) {
          return '请至少选择一个用户'
        }

        return null
      },
    },
  })

  // 监听选项卡切换，重置错误信息
  useEffect(() => {
    setError(null)
    setShowPreview(false)
  }, [activeTab])

  // 编辑成员表单
  const editForm = useForm<EditForm>({
    initialValues: {
      username: initialData?.username || '',
      role: initialData?.role || 'READ',
    },
    validate: {
      username: value => (value ? null : '用户名不能为空'),
      role: value => (value ? null : '请选择角色'),
    },
  })

  // 删除成员表单
  const deleteForm = useForm<DeleteForm>({
    initialValues: {
      username: initialData?.username || '',
      confirmDelete: false,
    },
    validate: {
      confirmDelete: value => (value ? null : '请确认删除操作'),
    },
  })

  // 添加成员的 mutation
  const addMemberMutation = useMutation(trpc.project.addMember.mutationOptions())

  // 更新成员角色的 mutation
  const updateMemberRoleMutation = useMutation(trpc.project.updateMemberRole.mutationOptions())

  // 删除成员的 mutation
  const removeMemberMutation = useMutation(trpc.project.removeMember.mutationOptions())

  // 解析批量添加的用户名
  const parseUsernames = (
    input: string,
  ): { parsedUsers: ParsedUser[]; invalidEntries: string[] } => {
    if (!input.trim()) return { parsedUsers: [], invalidEntries: [] }

    // 企业微信格式: username(中文名)，只接受分号分隔
    const entries = input
      .split(';')
      .map(entry => entry.trim())
      .filter(entry => entry.length > 0)
    const wechatFormatRegex = /^([a-zA-Z0-9_-]+)\((.+?)\)$/

    const parsedUsers: ParsedUser[] = []
    const invalidEntries: string[] = []

    for (const entry of entries) {
      const match = entry.match(wechatFormatRegex)
      if (match) {
        const username = match[1].trim()
        const chineseName = match[2].trim()

        // 验证用户名格式
        if (username.length === 0) {
          invalidEntries.push(entry)
          continue
        }

        parsedUsers.push({
          username,
          chineseName,
        })
      } else {
        // 不符合格式的条目
        if (entry.length > 0) {
          invalidEntries.push(entry)
        }
      }
    }

    return { parsedUsers, invalidEntries }
  }

  // 预览批量添加的成员
  const handlePreview = () => {
    const { parsedUsers, invalidEntries } = parseUsernames(batchAddForm.values.usernames)
    const role = batchAddForm.values.role

    if (parsedUsers.length === 0) {
      setError('请输入至少一个有效的用户名')
      return
    }

    if (invalidEntries.length > 0) {
      setError(
        `发现 ${invalidEntries.length} 个格式错误的条目：${invalidEntries.join(', ')}。请修正后重试。`,
      )
      return
    }

    const members = parsedUsers.map(user => ({
      username: user.username,
      role,
      chineseName: user.chineseName,
    }))

    setParsedMembers(members)
    setShowPreview(true)
  }

  // 处理批量添加成员
  const handleBatchAdd = async (values: BatchAddForm) => {
    try {
      setError(null)

      if (activeTab === 'select') {
        // 下拉选择模式
        if (values.selectedUsers.length === 0) {
          setError('请至少选择一个用户')
          return
        }

        // 获取选中的用户
        const selectedUserObjects =
          users?.filter(user => values.selectedUsers.includes(user.username)) || []

        if (selectedUserObjects.length === 0) {
          setError('未找到选中的用户信息')
          return
        }

        // 转换为成员格式
        const members = selectedUserObjects.map(user => ({
          username: user.username,
          role: values.role,
          chineseName: user.chineseName,
        }))

        // 添加成员
        await addMemberMutation.mutateAsync({
          projectId,
          members,
        })

        notifications.show({
          title: '添加成功',
          message: `已成功添加 ${members.length} 个成员`,
          color: 'green',
        })

        if (onSuccess) onSuccess()
        finish(undefined)
      } else {
        // 批量导入模式
        if (!showPreview) {
          handlePreview()
          return
        }

        if (parsedMembers.length === 0) {
          setError('请输入至少一个有效的用户名')
          return
        }

        await addMemberMutation.mutateAsync({
          projectId,
          members: parsedMembers.map(m => ({
            username: m.username,
            role: m.role,
            chineseName: m.chineseName,
          })),
        })

        notifications.show({
          title: '添加成功',
          message: `已成功添加 ${parsedMembers.length} 个成员`,
          color: 'green',
        })

        if (onSuccess) onSuccess()
        finish(undefined)
      }
    } catch (error: any) {
      console.error('添加成员失败:', error)
      setError(error.message || '添加成员失败')
    }
  }

  // 处理编辑成员
  const handleEdit = async (values: EditForm) => {
    try {
      setError(null)

      await updateMemberRoleMutation.mutateAsync({
        projectId,
        username: values.username,
        role: values.role,
      })

      notifications.show({
        title: '更新成功',
        message: '成员角色已成功更新',
        color: 'green',
      })

      if (onSuccess) onSuccess()
      finish(undefined)
    } catch (error: any) {
      console.error('更新成员失败:', error)
      setError(error.message || '更新成员失败')
    }
  }

  // 处理删除成员
  const handleDelete = async (values: DeleteForm) => {
    try {
      setError(null)

      if (!values.confirmDelete) {
        setError('请确认删除操作')
        return
      }

      await removeMemberMutation.mutateAsync({
        projectId,
        username: values.username,
      })

      notifications.show({
        title: '删除成功',
        message: '成员已成功从项目中移除',
        color: 'green',
      })

      if (onSuccess) onSuccess()
      finish(undefined)
    } catch (error: any) {
      console.error('删除成员失败:', error)
      setError(error.message || '删除成员失败')
    }
  }

  // 获取模态框标题
  const getModalTitle = () => {
    switch (mode) {
      case 'add':
        return '添加项目成员'
      case 'edit':
        return '编辑成员角色'
      case 'delete':
        return '删除项目成员'
      default:
        return '成员管理'
    }
  }

  return (
    <Modal opened={bind.visible} onClose={bind.onClose} title={getModalTitle()} size="md">
      {error && (
        <Alert icon={<IconAlertCircle size="1rem" />} title="错误" color="red" mb="md">
          {error}
        </Alert>
      )}

      {mode === 'add' && (
        <form onSubmit={batchAddForm.onSubmit(handleBatchAdd)}>
          <Stack gap="md">
            <Tabs value={activeTab} onChange={setActiveTab}>
              <Tabs.List>
                <Tabs.Tab value="select" leftSection={<IconUsers size="0.8rem" />}>
                  下拉选择
                </Tabs.Tab>
                <Tabs.Tab value="import" leftSection={<IconFileImport size="0.8rem" />}>
                  批量导入
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value="select" pt="xs">
                <MultiSelect
                  label="选择用户"
                  description="从系统中已有的用户中选择"
                  placeholder="选择用户"
                  searchable
                  nothingFoundMessage="没有找到匹配的用户"
                  data={
                    users?.map(user => ({
                      value: user.username,
                      label: user.chineseName
                        ? `${user.username} (${user.chineseName})`
                        : user.username,
                    })) || []
                  }
                  rightSection={isLoadingUsers ? <Loader size="xs" /> : null}
                  disabled={isLoadingUsers}
                  required={activeTab === 'select'}
                  {...batchAddForm.getInputProps('selectedUsers')}
                />
              </Tabs.Panel>

              <Tabs.Panel value="import" pt="xs">
                <Textarea
                  label="批量导入用户"
                  description={
                    <>
                      <Text size="xs" c="dimmed">
                        格式：username(中文名)，多个用户之间必须使用分号(;)分隔
                      </Text>
                      <Text size="xs" c="dimmed" mt={5} fw={500}>
                        示例：xxxx(张三);xxxx(李四);xxxx(王五)
                      </Text>
                      <Text size="xs" c="dimmed" mt={5}>
                        提示：可从企业微信群成员列表中，点击"复制成员账号"快速获取
                      </Text>
                    </>
                  }
                  placeholder="xxxx(张三);xxxx(李四);xxxx(王五)"
                  minRows={5}
                  required={activeTab === 'import'}
                  {...batchAddForm.getInputProps('usernames')}
                />

                {showPreview && parsedMembers.length > 0 && (
                  <>
                    <Divider label="预览" labelPosition="center" mt="md" />
                    <Text size="sm" fw={500}>
                      将添加以下 {parsedMembers.length} 个成员:
                    </Text>
                    <Box>
                      {parsedMembers.map((member, index) => {
                        const isExisting = currentMembers.some(m => m.username === member.username)
                        return (
                          <Group key={index} mb={5}>
                            <Text>
                              {member.username}
                              {member.chineseName ? ` (${member.chineseName})` : ''}
                            </Text>
                            <Badge
                              color={
                                member.role === 'OWNER'
                                  ? 'blue'
                                  : member.role === 'WRITE'
                                    ? 'green'
                                    : 'gray'
                              }
                            >
                              {member.role}
                            </Badge>
                            {isExisting && (
                              <Tooltip label="此用户已是项目成员，将保留更高权限级别">
                                <Badge color="yellow">已存在</Badge>
                              </Tooltip>
                            )}
                          </Group>
                        )
                      })}
                    </Box>
                  </>
                )}
              </Tabs.Panel>
            </Tabs>

            <Select
              label="角色"
              placeholder="选择角色"
              required
              data={[
                { value: 'READ', label: '只读' },
                { value: 'WRITE', label: '可写' },
                { value: 'OWNER', label: '管理员' },
              ]}
              {...batchAddForm.getInputProps('role')}
            />

            <Text size="xs" c="dimmed">
              角色权限说明：
              <br />
              - 只读：可以查看项目内容，但不能修改
              <br />
              - 可写：可以查看项目内容，编辑接口 tag、路径等，但不能管理成员
              <br />- 管理员：拥有所有权限，包括管理成员和删除项目
            </Text>

            <Group justify="flex-end">
              {activeTab === 'import' && !showPreview && (
                <Button variant="outline" onClick={handlePreview} mr="auto">
                  预览
                </Button>
              )}
              <Button variant="default" onClick={cancel}>
                取消
              </Button>
              <Button
                type="submit"
                loading={addMemberMutation.isPending}
                disabled={
                  (activeTab === 'select' &&
                    (!batchAddForm.values.selectedUsers ||
                      batchAddForm.values.selectedUsers.length === 0)) ||
                  (activeTab === 'import' &&
                    !showPreview &&
                    batchAddForm.values.usernames.trim() === '')
                }
                onClick={e => {
                  if (activeTab === 'import' && !showPreview) {
                    e.preventDefault()
                    handlePreview()
                  }
                }}
              >
                {activeTab === 'import' && !showPreview ? '预览' : '添加'}
              </Button>
            </Group>
          </Stack>
        </form>
      )}

      {mode === 'edit' && (
        <form onSubmit={editForm.onSubmit(handleEdit)}>
          <Stack gap="md">
            <TextInput label="用户名" value={editForm.values.username} disabled />

            <Select
              label="角色"
              placeholder="选择角色"
              required
              data={[
                { value: 'READ', label: '只读' },
                { value: 'WRITE', label: '可写' },
                { value: 'OWNER', label: '管理员' },
              ]}
              {...editForm.getInputProps('role')}
            />

            <Text size="xs" c="dimmed">
              角色权限说明：
              <br />
              - 只读：可以查看项目内容，但不能修改
              <br />
              - 可写：可以查看项目内容，编辑接口 tag、路径等，但不能管理成员
              <br />- 管理员：拥有所有权限，包括管理成员和删除项目
            </Text>

            <Group justify="flex-end">
              <Button variant="default" onClick={cancel}>
                取消
              </Button>
              <Button type="submit" loading={updateMemberRoleMutation.isPending}>
                保存
              </Button>
            </Group>
          </Stack>
        </form>
      )}

      {mode === 'delete' && (
        <form onSubmit={deleteForm.onSubmit(handleDelete)}>
          <Stack gap="md">
            <Alert icon={<IconAlertCircle size="1rem" />} color="yellow">
              您确定要从项目中移除此成员吗？此操作无法撤销。
            </Alert>

            <TextInput label="用户名" value={deleteForm.values.username} disabled />

            <Checkbox
              label="我确认要移除此成员"
              {...deleteForm.getInputProps('confirmDelete', { type: 'checkbox' })}
            />

            <Group justify="flex-end">
              <Button variant="default" onClick={cancel}>
                取消
              </Button>
              <Button
                type="submit"
                color="red"
                loading={removeMemberMutation.isPending}
                disabled={!deleteForm.values.confirmDelete}
              >
                删除
              </Button>
            </Group>
          </Stack>
        </form>
      )}
    </Modal>
  )
}
