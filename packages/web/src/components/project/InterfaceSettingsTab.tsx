import { useState } from 'react'
import {
  Box,
  Tabs,
  Title,
  Text,
  Group,
  Button,
  Stack,
  ActionIcon,
  Badge,
  Card,
  Alert,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import { useModal } from '@/utils/modal'
import {
  IconPlus,
  IconEdit,
  IconTrash,
  IconAlertCircle,
  IconTag,
  IconRoute,
} from '@tabler/icons-react'
import { TagFormModal } from './TagFormModal'
import { PathRuleFormModal } from './PathRuleFormModal'

interface InterfaceSettingsTabProps {
  projectId: number
}

export function InterfaceSettingsTab({ projectId }: InterfaceSettingsTabProps) {
  const [activeTab, setActiveTab] = useState<string | null>('tags')

  return (
    <Stack gap="md">
      <Title order={3}>接口设置</Title>
      <Text c="dimmed" size="sm">
        管理接口标签和路径规则，用于增强接口信息展示和组织。
      </Text>

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="tags" leftSection={<IconTag size={16} />}>
            接口标签
          </Tabs.Tab>
          <Tabs.Tab value="pathRules" leftSection={<IconRoute size={16} />}>
            路径规则
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="tags" pt="md">
          <TagsManager projectId={projectId} />
        </Tabs.Panel>

        <Tabs.Panel value="pathRules" pt="md">
          <PathRulesManager projectId={projectId} />
        </Tabs.Panel>
      </Tabs>
    </Stack>
  )
}

// 标签管理组件
function TagsManager({ projectId }: { projectId: number }) {
  // 获取项目标签
  const { data: tags, isLoading, refetch } = useQuery(trpc.tag.getTags.queryOptions({ projectId }))

  // 删除标签
  const deleteTag = useMutation(trpc.tag.deleteTag.mutationOptions())

  // 使用 useModal 创建标签表单弹窗
  const tagFormModal = useModal(TagFormModal)

  // 处理删除标签
  const handleDeleteTag = async (id: number, name: string) => {
    if (!window.confirm(`确定要删除标签 "${name}" 吗？删除后，所有使用此标签的接口将失去此标签。`))
      return

    try {
      await deleteTag.mutateAsync({
        id,
        projectId,
      })
      notifications.show({
        title: '删除成功',
        message: `标签 "${name}" 已删除`,
        color: 'green',
      })
      refetch()
    } catch (error: any) {
      notifications.show({
        title: '删除失败',
        message: error.message || '删除标签失败',
        color: 'red',
      })
    }
  }

  // 打开创建标签弹窗
  const openCreateTagModal = () => {
    tagFormModal.openModal({
      projectId,
      mode: 'create',
      onSuccess: () => refetch(),
    })
  }

  // 打开编辑标签弹窗
  const openEditTagModal = (tag: any) => {
    tagFormModal.openModal({
      projectId,
      mode: 'edit',
      initialData: tag,
      onSuccess: () => refetch(),
    })
  }

  return (
    <Stack gap="md">
      <Group justify="space-between">
        <Text fw={500}>标签管理</Text>
        <Button leftSection={<IconPlus size={16} />} size="xs" onClick={openCreateTagModal}>
          添加标签
        </Button>
      </Group>

      {isLoading ? (
        <Text>加载中...</Text>
      ) : tags && tags.length > 0 ? (
        <Stack gap="xs">
          {tags.map(tag => (
            <Group key={tag.id} justify="space-between" p="xs" style={{ borderRadius: 4 }}>
              <Group>
                <Badge color={tag.color || 'blue'} size="lg" variant="filled">
                  {tag.name}
                </Badge>
                {tag.icon && <Text size="sm">{tag.icon}</Text>}
              </Group>
              <Group>
                <ActionIcon variant="subtle" color="gray" onClick={() => openEditTagModal(tag)}>
                  <IconEdit size={16} />
                </ActionIcon>
                <ActionIcon
                  variant="subtle"
                  color="red"
                  onClick={() => handleDeleteTag(tag.id, tag.name)}
                >
                  <IconTrash size={16} />
                </ActionIcon>
              </Group>
            </Group>
          ))}
        </Stack>
      ) : (
        <Text c="dimmed">暂无标签，点击"添加标签"创建第一个标签。</Text>
      )}
    </Stack>
  )
}

// 路径规则管理组件
function PathRulesManager({ projectId }: { projectId: number }) {
  // 获取项目路径规则
  const {
    data: pathRules,
    isLoading,
    refetch,
  } = useQuery(trpc.pathRule.getPathRules.queryOptions({ projectId }))

  // 获取项目信息（包含默认路径规则）
  const { data: project } = useQuery(trpc.project.info.queryOptions({ projectId }))

  // 删除路径规则
  const deletePathRule = useMutation(trpc.pathRule.deletePathRule.mutationOptions())

  // 设置默认路径规则
  const setDefaultPathRule = useMutation(trpc.pathRule.setDefaultPathRule.mutationOptions())

  // 使用 useModal 创建路径规则表单弹窗
  const pathRuleFormModal = useModal(PathRuleFormModal)

  // 处理删除路径规则
  const handleDeletePathRule = async (id: number, name: string) => {
    if (
      !window.confirm(
        `确定要删除规则 "${name}" 吗？删除后，所有使用此规则的接口将恢复使用项目默认规则。`,
      )
    )
      return

    try {
      await deletePathRule.mutateAsync({
        id,
        projectId,
      })
      notifications.show({
        title: '删除成功',
        message: `规则 "${name}" 已删除`,
        color: 'green',
      })
      refetch()
    } catch (error: any) {
      notifications.show({
        title: '删除失败',
        message: error.message || '删除规则失败',
        color: 'red',
      })
    }
  }

  // 设置为默认规则
  const handleSetAsDefault = async (id: number, name: string) => {
    try {
      await setDefaultPathRule.mutateAsync({
        projectId,
        pathRuleId: id,
      })
      notifications.show({
        title: '设置成功',
        message: `已将 "${name}" 设为项目默认路径规则`,
        color: 'green',
      })
      refetch()
    } catch (error: any) {
      notifications.show({
        title: '设置失败',
        message: error.message || '设置默认规则失败',
        color: 'red',
      })
    }
  }

  // 清除默认规则
  const handleClearDefault = async () => {
    try {
      await setDefaultPathRule.mutateAsync({
        projectId,
        pathRuleId: null,
      })
      notifications.show({
        title: '清除成功',
        message: '已清除项目默认路径规则',
        color: 'green',
      })
      refetch()
    } catch (error: any) {
      notifications.show({
        title: '清除失败',
        message: error.message || '清除默认规则失败',
        color: 'red',
      })
    }
  }

  // 打开创建路径规则弹窗
  const openCreatePathRuleModal = () => {
    pathRuleFormModal.openModal({
      projectId,
      mode: 'create',
      onSuccess: () => refetch(),
    })
  }

  // 打开编辑路径规则弹窗
  const openEditPathRuleModal = (rule: any) => {
    pathRuleFormModal.openModal({
      projectId,
      mode: 'edit',
      initialData: rule,
      onSuccess: () => refetch(),
    })
  }

  return (
    <Stack gap="md">
      <Group justify="space-between">
        <Text fw={500}>路径规则管理</Text>
        <Button leftSection={<IconPlus size={16} />} size="xs" onClick={openCreatePathRuleModal}>
          添加规则
        </Button>
      </Group>

      <Alert icon={<IconAlertCircle size={16} />} color="blue">
        <Text size="sm">路径规则用于生成接口的完整访问路径。您可以使用以下占位符：</Text>
        <Text size="xs" mt="xs">
          <code>${'{SERVICE_NAME}'}</code> - 服务名称
          <br />
          <code>${'{METHOD_NAME}'}</code> - 方法名称
          <br />
          <code>${'{PROTO_FILE_NAME}'}</code> - Proto 文件名
          <br />
          <code>${'{PROJECT_NAME}'}</code> - 项目名称
          <br />
          <code>${'{SERVICE_NAME_LOWERCASE}'}</code> - 小写服务名称
          <br />
          <code>${'{METHOD_NAME_LOWERCASE}'}</code> - 小写方法名称
        </Text>
      </Alert>

      {isLoading ? (
        <Text>加载中...</Text>
      ) : pathRules && pathRules.length > 0 ? (
        <Stack gap="xs">
          {pathRules.map(rule => {
            return (
              <Card key={rule.id} withBorder p="sm">
                <Group justify="space-between">
                  <Stack gap={4}>
                    <Group gap="xs">
                      <Text fw={500}>{rule.name}</Text>
                      {rule.default && (
                        <Badge color="green" size="sm">
                          默认
                        </Badge>
                      )}
                    </Group>
                    <Text size="sm" c="dimmed">
                      {rule.pattern}
                    </Text>
                    {rule.description && (
                      <Text size="xs" c="dimmed">
                        {rule.description}
                      </Text>
                    )}
                  </Stack>
                  <Group>
                    {!rule.default && (
                      <Button
                        variant="light"
                        size="xs"
                        onClick={() => handleSetAsDefault(rule.id, rule.name)}
                      >
                        设为默认
                      </Button>
                    )}
                    {rule.default && (
                      <Button variant="light" color="gray" size="xs" onClick={handleClearDefault}>
                        取消默认
                      </Button>
                    )}
                    <ActionIcon
                      variant="subtle"
                      color="gray"
                      onClick={() => openEditPathRuleModal(rule)}
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                    <ActionIcon
                      variant="subtle"
                      color="red"
                      onClick={() => handleDeletePathRule(rule.id, rule.name)}
                      disabled={rule.default}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Group>
              </Card>
            )
          })}
        </Stack>
      ) : (
        <Text c="dimmed">暂无路径规则，点击"添加规则"创建第一个规则。</Text>
      )}
    </Stack>
  )
}
