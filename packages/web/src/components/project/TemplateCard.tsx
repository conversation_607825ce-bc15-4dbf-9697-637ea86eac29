import { Card, Group, Text, Badge, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u, ActionIcon } from '@mantine/core'
import { useMutation } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import {
  IconBrandTypescript,
  IconBrandJavascript,
  IconDotsVertical,
  IconUnlink,
  IconExternalLink,
  IconLock,
  IconWorld,
  IconEdit as IconEditOpen,
  IconBrandAdobeIllustrator,
} from '@tabler/icons-react'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { Link } from '@tanstack/react-router'
import { modals } from '@mantine/modals'
import { UserAvatar } from '@/components/common/UserAvatar'

interface TemplateCardProps {
  template: any
  type: 'script' | 'ai'
  projectId: number
  onUnbind: () => void
}

// 获取权限图标和颜色
const getAuthInfo = (auth: string) => {
  switch (auth) {
    case 'PRIVATE':
      return { icon: <IconLock size={12} />, color: 'gray', label: '私有' }
    case 'PUBLIC':
      return { icon: <IconWorld size={12} />, color: 'blue', label: '公开' }
    case 'OPEN':
      return { icon: <IconEditOpen size={12} />, color: 'green', label: '开放' }
    default:
      return { icon: <IconWorld size={12} />, color: 'blue', label: '公开' }
  }
}

// 获取语言图标
const getLangIcon = (lang: 'TS' | 'JS') => {
  switch (lang) {
    case 'TS':
      return <IconBrandTypescript size={16} />
    case 'JS':
      return <IconBrandJavascript size={16} />
  }
}

export const TemplateCard = ({ template, type, projectId, onUnbind }: TemplateCardProps) => {
  // 解绑脚本模板
  const unbindScriptTemplate = useMutation(trpc.template.script.unbindProject.mutationOptions())

  // 解绑AI模板
  const unbindAiTemplate = useMutation(trpc.template.ai.unbindProject.mutationOptions())

  // 处理解绑操作
  const handleUnbind = () => {
    modals.openConfirmModal({
      title: '确认解绑',
      children: (
        <Text size="sm">
          确定要解绑模板 <strong>{template.title}</strong> 吗？解绑后该模板将不再与此项目关联。
        </Text>
      ),
      labels: { confirm: '解绑', cancel: '取消' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          if (type === 'script') {
            await unbindScriptTemplate.mutateAsync({
              projectId,
              templateIds: [template.id],
            })
          } else {
            await unbindAiTemplate.mutateAsync({
              projectId,
              templateIds: [template.id],
            })
          }

          notifications.show({
            title: '解绑成功',
            message: '模板已成功解绑',
            color: 'green',
          })

          onUnbind()
        } catch (error: any) {
          console.error('解绑模板失败:', error)
          notifications.show({
            title: '解绑失败',
            message: error.message || '解绑模板失败，请重试',
            color: 'red',
          })
        }
      },
    })
  }

  const isUnbinding = unbindScriptTemplate.isPending || unbindAiTemplate.isPending
  const authInfo = getAuthInfo(template.auth)

  return (
    <Card shadow="sm" padding="md" radius="md" withBorder>
      <Stack gap="sm">
        <Group justify="space-between" align="flex-start">
          <div style={{ flex: 1 }}>
            <Text fw={500} mb="xs">
              {template.title}
            </Text>

            <Group gap="xs" mb="xs">
              {type === 'script' ? (
                <Badge leftSection={getLangIcon(template.lang)} variant="light" size="sm">
                  {template.lang}
                </Badge>
              ) : (
                <Badge
                  leftSection={<IconBrandAdobeIllustrator size={12} />}
                  variant="light"
                  color="green"
                  size="sm"
                >
                  AI
                </Badge>
              )}
              <Badge leftSection={authInfo.icon} variant="light" color={authInfo.color} size="sm">
                {authInfo.label}
              </Badge>
            </Group>

            <Text size="sm" c="dimmed" lineClamp={2} mb="xs">
              {template.desc || '暂无描述'}
            </Text>

            <Group gap="xs" align="center">
              <UserAvatar username={template.createdBy} size="xs" />
              <Text size="xs" fw={500}>
                {template.createdBy}
              </Text>
              <Text size="xs" c="dimmed">
                {formatDistanceToNow(new Date(template.createdAt), {
                  addSuffix: true,
                  locale: zhCN,
                })}
              </Text>
            </Group>
          </div>

          <Menu shadow="md" width={150} position="bottom-end">
            <Menu.Target>
              <ActionIcon size="sm" variant="subtle">
                <IconDotsVertical size={14} />
              </ActionIcon>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item
                leftSection={<IconExternalLink size={14} />}
                component={Link}
                to={`/templates/detail/${type}/${template.id}`}
              >
                查看详情
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item
                leftSection={<IconUnlink size={14} />}
                color="red"
                onClick={handleUnbind}
                disabled={isUnbinding}
              >
                解绑模板
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </Stack>
    </Card>
  )
}
