import { Button, Group, Stack, Text, TextInput } from '@mantine/core'
import { notifications } from '@mantine/notifications'
import { useMutation } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { Modal } from '@mantine/core'
import { type MC } from '@/utils/modal'
import { useState } from 'react'
import { useNavigate } from '@tanstack/react-router'

// 定义自定义选项类型
interface DeleteProjectOptions {
  projectId: number
  projectName: string
  confirmText: string // 用于确认删除的文本
}

// 定义返回数据类型
interface DeleteResult {
  success: boolean
}

// 项目删除模态框组件
const DeleteProjectModal: MC<DeleteProjectOptions, DeleteResult> = props => {
  const { projectId, projectName, confirmText } = props
  const [confirmInput, setConfirmInput] = useState('')
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate({ from: '/$projectId' })

  // 删除项目的 mutation
  const deleteModule = useMutation(trpc.project.delete.mutationOptions())

  // 处理删除提交
  const handleDelete = async () => {
    // 验证确认文本
    if (confirmInput !== confirmText) {
      setError('请输入正确的确认文本')
      return
    }

    try {
      // 调用删除 API
      await deleteModule.mutateAsync({
        projectId,
      })

      // 调用 finish 方法关闭弹窗并返回结果
      props.finish(
        {
          success: true,
        },
        '项目已成功删除',
      )

      // 导航到项目列表页
      navigate({ to: '/projects' })
    } catch (error) {
      console.error('删除项目失败:', error)
      notifications.show({
        title: '删除失败',
        message: `无法删除项目: ${(error as any)?.message || '未知错误'}`,
        color: 'red',
      })
    }
  }

  return (
    <Modal
      opened={props.bind.visible}
      onClose={props.bind.onClose}
      title="删除项目"
      size="md"
      centered
    >
      <Stack gap="md">
        <Text>
          您确定要删除项目 <strong>{projectName}</strong>{' '}
          吗？此操作不可撤销，项目的所有数据将被永久删除。
        </Text>

        <Text fw={500}>
          请输入 <strong>{confirmText}</strong> 以确认删除：
        </Text>

        <TextInput
          placeholder={`请输入 ${confirmText}`}
          value={confirmInput}
          onChange={e => {
            setConfirmInput(e.target.value)
            setError(null)
          }}
          error={error}
          data-autofocus
        />

        <Group justify="flex-end">
          <Button variant="default" onClick={props.cancel}>
            取消
          </Button>
          <Button
            color="red"
            onClick={handleDelete}
            loading={deleteModule.isPending}
            disabled={confirmInput !== confirmText}
          >
            删除项目
          </Button>
        </Group>
      </Stack>
    </Modal>
  )
}

export { DeleteProjectModal }
