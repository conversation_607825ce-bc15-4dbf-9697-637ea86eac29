import { Mo<PERSON>, <PERSON><PERSON>, <PERSON>, Badge, Group, Avatar, Divider, <PERSON><PERSON>, But<PERSON> } from '@mantine/core'
import { IconShield, IconAlertCircle, IconUser, IconCrown, IconEdit } from '@tabler/icons-react'
import { type MC } from '@/utils/modal'
import { UserAvatar } from '@/components/common/UserAvatar'

// 定义项目成员类型
interface ProjectMember {
  username: string
  role: 'OWNER' | 'READ' | 'WRITE'
  user?: {
    username: string
    chineseName?: string | null
  }
}

// 定义弹窗选项类型
interface ProjectPermissionModalOptions {
  projectName: string
  projectTitle?: string
  repository: string
  members: ProjectMember[]
}

// 权限申请弹窗组件
const ProjectPermissionModal: MC<ProjectPermissionModalOptions> = ({
  bind,
  cancel,
  projectName,
  projectTitle,
  repository,
  members,
}) => {
  // 获取可以授权的成员（OWNER 和 WRITE 权限）
  const authorizingMembers = members.filter(
    member => member.role === 'OWNER' || member.role === 'WRITE',
  )

  // 获取权限角色对应的图标和颜色
  const getRoleInfo = (role: ProjectMember['role']) => {
    switch (role) {
      case 'OWNER':
        return { icon: IconCrown, color: 'yellow', label: '项目所有者' }
      case 'WRITE':
        return { icon: IconEdit, color: 'blue', label: '写入权限' }
      case 'READ':
        return { icon: IconUser, color: 'gray', label: '只读权限' }
    }
  }

  return (
    <Modal opened={bind.visible} onClose={bind.onClose} title="申请项目权限" size="md" centered>
      <Stack gap="md">
        <Alert icon={<IconAlertCircle size={16} />} title="权限不足" color="orange">
          您当前没有访问此项目的权限，需要联系项目管理员为您开通权限。
        </Alert>

        <Stack gap="xs">
          <Text fw={500} size="sm">
            项目信息
          </Text>
          <Group gap="xs">
            <Text size="sm" c="dimmed">
              项目名称：
            </Text>
            <Text size="sm">{projectTitle || projectName}</Text>
          </Group>
          <Group gap="xs">
            <Text size="sm" c="dimmed">
              仓库：
            </Text>
            <Text size="sm">{repository}</Text>
          </Group>
        </Stack>

        {authorizingMembers.length > 0 && (
          <>
            <Divider />
            <Alert
              icon={<IconShield size={16} />}
              title="联系以下成员申请权限"
              color="blue"
              variant="light"
            >
              <Stack gap="xs" mt="xs">
                <Text size="sm">以下成员拥有管理权限，可以为您开通项目访问权限：</Text>
                <Stack gap="xs">
                  {authorizingMembers.map(member => (
                    <Group key={member.username} gap="sm">
                      <Text size="sm" fw={500}>
                        {member.user?.chineseName || member.username}
                      </Text>
                      <Text size="xs" c="dimmed">
                        (@{member.username})
                      </Text>
                      <Badge color={getRoleInfo(member.role).color} size="xs" variant="light">
                        {getRoleInfo(member.role).label}
                      </Badge>
                    </Group>
                  ))}
                </Stack>
              </Stack>
            </Alert>
          </>
        )}

        <Group justify="flex-end" mt="md">
          <Button variant="default" onClick={cancel}>
            我知道了
          </Button>
        </Group>
      </Stack>
    </Modal>
  )
}

export { ProjectPermissionModal }
