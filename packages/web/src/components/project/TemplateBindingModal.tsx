import { useState } from 'react'
import {
  Modal,
  Stack,
  Text,
  Button,
  Group,
  Tabs,
  Grid,
  Card,
  Badge,
  Checkbox,
  TextInput,
  Loader,
  Alert,
  Title,
  ThemeIcon,
  rem,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import {
  IconCode,
  IconSearch,
  IconAlertCircle,
  IconBrandTypescript,
  IconBrandJavascript,
  IconTemplate,
  IconBrandAdobeIllustrator,
} from '@tabler/icons-react'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { MC } from '@/utils/modal'

interface TemplateBindingModalOptions {
  projectId: number
  onSuccess?: () => void
}

interface TemplateBindingData {
  success: boolean
}

// 获取语言图标
const getLangIcon = (lang: 'TS' | 'JS') => {
  switch (lang) {
    case 'TS':
      return <IconBrandTypescript size={16} />
    case 'JS':
      return <IconBrandJavascript size={16} />
  }
}

export const TemplateBindingModal: MC<TemplateBindingModalOptions, TemplateBindingData> = ({
  projectId,
  bind,
  finish,
  cancel,
}) => {
  const [activeTab, setActiveTab] = useState<string | null>('ai')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedScriptTemplates, setSelectedScriptTemplates] = useState<number[]>([])
  const [selectedAiTemplates, setSelectedAiTemplates] = useState<number[]>([])

  // 获取所有可用的脚本模板
  const { data: allScriptTemplates, isLoading: isLoadingScriptTemplates } = useQuery(
    trpc.template.script.listAll.queryOptions(),
  )

  // 获取所有可用的AI模板
  const { data: allAiTemplates, isLoading: isLoadingAiTemplates } = useQuery(
    trpc.template.ai.listAll.queryOptions(),
  )

  // 获取项目已绑定的脚本模板
  const { data: boundScriptTemplates } = useQuery(
    trpc.template.script.listByProject.queryOptions({ projectId }),
  )

  // 获取项目已绑定的AI模板
  const { data: boundAiTemplates } = useQuery(
    trpc.template.ai.listByProject.queryOptions({ projectId }),
  )

  // 绑定脚本模板
  const bindScriptTemplates = useMutation(trpc.template.script.bindProject.mutationOptions())

  // 绑定AI模板
  const bindAiTemplates = useMutation(trpc.template.ai.bindProject.mutationOptions())

  // 过滤可绑定的脚本模板（排除已绑定的）
  const availableScriptTemplates =
    allScriptTemplates
      ?.filter(template => !boundScriptTemplates?.some(bound => bound.id === template.id))
      .filter(template => {
        if (!searchQuery) return true
        const query = searchQuery.toLowerCase()
        return (
          template.title.toLowerCase().includes(query) ||
          template.createdBy.toLowerCase().includes(query) ||
          (template.desc && template.desc.toLowerCase().includes(query))
        )
      }) || []

  // 过滤可绑定的AI模板（排除已绑定的）
  const availableAiTemplates =
    allAiTemplates
      ?.filter(template => !boundAiTemplates?.some(bound => bound.id === template.id))
      .filter(template => {
        if (!searchQuery) return true
        const query = searchQuery.toLowerCase()
        return (
          template.title.toLowerCase().includes(query) ||
          template.createdBy.toLowerCase().includes(query) ||
          (template.desc && template.desc.toLowerCase().includes(query))
        )
      }) || []

  // 处理脚本模板选择
  const handleScriptTemplateSelect = (templateId: number, checked: boolean) => {
    if (checked) {
      setSelectedScriptTemplates(prev => [...prev, templateId])
    } else {
      setSelectedScriptTemplates(prev => prev.filter(id => id !== templateId))
    }
  }

  // 处理AI模板选择
  const handleAiTemplateSelect = (templateId: number, checked: boolean) => {
    if (checked) {
      setSelectedAiTemplates(prev => [...prev, templateId])
    } else {
      setSelectedAiTemplates(prev => prev.filter(id => id !== templateId))
    }
  }

  // 处理绑定操作
  const handleBind = async () => {
    try {
      const promises = []

      // 绑定选中的脚本模板
      if (selectedScriptTemplates.length > 0) {
        promises.push(
          bindScriptTemplates.mutateAsync({
            projectId,
            templateIds: selectedScriptTemplates,
          }),
        )
      }

      // 绑定选中的AI模板
      if (selectedAiTemplates.length > 0) {
        promises.push(
          bindAiTemplates.mutateAsync({
            projectId,
            templateIds: selectedAiTemplates,
          }),
        )
      }

      if (promises.length === 0) {
        notifications.show({
          title: '提示',
          message: '请至少选择一个模板进行绑定',
          color: 'yellow',
        })
        return
      }

      await Promise.all(promises)

      notifications.show({
        title: '绑定成功',
        message: `已成功绑定 ${selectedScriptTemplates.length + selectedAiTemplates.length} 个模板`,
        color: 'green',
      })

      finish({ success: true })
    } catch (error: any) {
      console.error('绑定模板失败:', error)
      notifications.show({
        title: '绑定失败',
        message: error.message || '绑定模板失败，请重试',
        color: 'red',
      })
    }
  }

  const isLoading = bindScriptTemplates.isPending || bindAiTemplates.isPending
  const hasSelection = selectedScriptTemplates.length > 0 || selectedAiTemplates.length > 0

  return (
    <Modal
      opened={bind.visible}
      onClose={bind.onClose}
      title={
        <Group>
          <ThemeIcon size="lg" radius="md" color="indigo" variant="light">
            <IconTemplate size={20} />
          </ThemeIcon>
          <Title order={4}>绑定模板到项目</Title>
        </Group>
      }
      size="xl"
      centered
    >
      <Stack gap="md">
        <TextInput
          placeholder="搜索模板..."
          value={searchQuery}
          onChange={e => setSearchQuery(e.currentTarget.value)}
          leftSection={<IconSearch size={16} />}
        />

        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value="ai" leftSection={<IconBrandAdobeIllustrator size={14} />}>
              AI 模板 ({availableAiTemplates.length})
            </Tabs.Tab>
            <Tabs.Tab value="script" leftSection={<IconCode size={14} />}>
              高级模板 ({availableScriptTemplates.length})
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="script" pt="md">
            {isLoadingScriptTemplates ? (
              <Group justify="center" p="xl">
                <Loader size="md" />
              </Group>
            ) : availableScriptTemplates.length > 0 ? (
              <Grid>
                {availableScriptTemplates.map(template => (
                  <Grid.Col key={template.id} span={{ base: 12, sm: 6 }}>
                    <Card
                      shadow="sm"
                      padding="md"
                      radius="md"
                      withBorder
                      style={{
                        cursor: 'pointer',
                      }}
                      bg={selectedScriptTemplates.includes(template.id) ? 'blue.0' : undefined}
                      bd={
                        selectedScriptTemplates.includes(template.id)
                          ? '1px solid var(--mantine-color-blue-4)'
                          : undefined
                      }
                      onClick={() =>
                        handleScriptTemplateSelect(
                          template.id,
                          !selectedScriptTemplates.includes(template.id),
                        )
                      }
                    >
                      <Stack gap="sm">
                        <Group justify="space-between" wrap="nowrap">
                          <Checkbox
                            checked={selectedScriptTemplates.includes(template.id)}
                            onChange={e =>
                              handleScriptTemplateSelect(template.id, e.currentTarget.checked)
                            }
                            onClick={e => e.stopPropagation()}
                          />
                          <Text fw={500} size="sm" lineClamp={1} style={{ flex: 1 }}>
                            {template.title}
                          </Text>
                        </Group>

                        <Badge leftSection={getLangIcon(template.lang)} variant="light" size="sm">
                          {template.lang}
                        </Badge>

                        <Text size="xs" c="dimmed" lineClamp={2}>
                          {template.desc || '暂无描述'}
                        </Text>

                        <Group gap="xs">
                          <Text size="xs" c="dimmed">
                            {template.createdBy}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {formatDistanceToNow(new Date(template.createdAt), {
                              addSuffix: true,
                              locale: zhCN,
                            })}
                          </Text>
                        </Group>
                      </Stack>
                    </Card>
                  </Grid.Col>
                ))}
              </Grid>
            ) : (
              <Alert icon={<IconAlertCircle size="1rem" />} color="gray">
                {searchQuery ? '没有找到匹配的脚本模板' : '没有可绑定的脚本模板'}
              </Alert>
            )}
          </Tabs.Panel>

          <Tabs.Panel value="ai" pt="md">
            {isLoadingAiTemplates ? (
              <Group justify="center" p="xl">
                <Loader size="md" />
              </Group>
            ) : availableAiTemplates.length > 0 ? (
              <Grid>
                {availableAiTemplates.map(template => (
                  <Grid.Col key={template.id} span={{ base: 12, sm: 6 }}>
                    <Card
                      shadow="sm"
                      padding="md"
                      radius="md"
                      withBorder
                      style={{
                        cursor: 'pointer',
                      }}
                      bg={selectedAiTemplates.includes(template.id) ? 'green.0' : undefined}
                      bd={
                        selectedAiTemplates.includes(template.id)
                          ? '1px solid var(--mantine-color-green-4)'
                          : undefined
                      }
                      onClick={() =>
                        handleAiTemplateSelect(
                          template.id,
                          !selectedAiTemplates.includes(template.id),
                        )
                      }
                    >
                      <Stack gap="sm">
                        <Group justify="space-between" wrap="nowrap">
                          <Checkbox
                            checked={selectedAiTemplates.includes(template.id)}
                            onChange={e =>
                              handleAiTemplateSelect(template.id, e.currentTarget.checked)
                            }
                            onClick={e => e.stopPropagation()}
                          />
                          <Text fw={500} size="sm" lineClamp={1} style={{ flex: 1 }}>
                            {template.title}
                          </Text>
                        </Group>

                        <Badge
                          leftSection={<IconBrandAdobeIllustrator size={16} />}
                          variant="light"
                          color="green"
                          size="sm"
                        >
                          AI
                        </Badge>

                        <Text size="xs" c="dimmed" lineClamp={2}>
                          {template.desc || '暂无描述'}
                        </Text>

                        <Group gap="xs">
                          <Text size="xs" c="dimmed">
                            {template.createdBy}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {formatDistanceToNow(new Date(template.createdAt), {
                              addSuffix: true,
                              locale: zhCN,
                            })}
                          </Text>
                        </Group>
                      </Stack>
                    </Card>
                  </Grid.Col>
                ))}
              </Grid>
            ) : (
              <Alert icon={<IconAlertCircle size="1rem" />} color="gray">
                {searchQuery ? '没有找到匹配的AI模板' : '没有可绑定的AI模板'}
              </Alert>
            )}
          </Tabs.Panel>
        </Tabs>

        <Group justify="flex-end" mt="md">
          <Button variant="light" onClick={() => cancel()}>
            取消
          </Button>
          <Button onClick={handleBind} loading={isLoading} disabled={!hasSelection}>
            绑定选中的模板 ({selectedScriptTemplates.length + selectedAiTemplates.length})
          </Button>
        </Group>
      </Stack>
    </Modal>
  )
}
