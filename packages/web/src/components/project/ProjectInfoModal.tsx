import { useState } from 'react'
import { Modal, Button, Group, Text, Stack, TextInput, Textarea, Alert } from '@mantine/core'
import { useMutation } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import { IconAlertCircle } from '@tabler/icons-react'
import { z } from 'zod'
import { useForm, zodResolver } from '@mantine/form'
import type { MC } from '@/utils/modal'

// 定义项目信息类型
interface ProjectInfo {
  title?: string
  desc?: string
}

// 定义模态框属性
interface ProjectInfoModalProps {
  projectId: number
  initialData?: ProjectInfo
  onSuccess?: () => void
}

// 项目信息编辑模态框组件
export const ProjectInfoModal: MC<ProjectInfoModalProps> = ({
  projectId,
  initialData,
  onSuccess,
  finish,
  cancel,
  bind,
}) => {
  const [error, setError] = useState<string | null>(null)

  // 定义表单 schema
  const formSchema = z.object({
    title: z.string().optional(),
    desc: z.string().optional(),
  })

  // 初始化表单
  const form = useForm({
    initialValues: {
      title: initialData?.title || '',
      desc: initialData?.desc || '',
    },
    validate: zodResolver(formSchema),
  })

  // 更新项目信息的 mutation
  const updateProjectInfo = useMutation(trpc.project.updateInfo.mutationOptions())

  // 处理表单提交
  const handleSubmit = async (values: typeof form.values) => {
    setError(null)

    try {
      await updateProjectInfo.mutateAsync({
        projectId,
        title: values.title,
        desc: values.desc,
      })

      notifications.show({
        title: '更新成功',
        message: '项目信息已成功更新',
        color: 'green',
      })

      if (onSuccess) onSuccess()
      finish(undefined)
    } catch (error: any) {
      console.error('更新项目信息失败:', error)
      setError(error.message || '更新项目信息失败')
    }
  }

  return (
    <Modal opened={bind.visible} onClose={bind.onClose} title="编辑项目信息" size="md">
      {error && (
        <Alert icon={<IconAlertCircle size="1rem" />} title="错误" color="red" mb="md">
          {error}
        </Alert>
      )}

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <TextInput
            label="项目标题"
            placeholder="输入项目标题（可选）"
            {...form.getInputProps('title')}
          />

          <Textarea
            label="项目描述"
            placeholder="输入项目描述（可选）"
            minRows={4}
            {...form.getInputProps('desc')}
          />

          <Text size="xs" c="dimmed">
            项目标题和描述是可选的。如果设置了标题，将在界面中优先显示标题而不是项目名称。
          </Text>

          <Group justify="flex-end">
            <Button variant="default" onClick={cancel}>
              取消
            </Button>
            <Button type="submit" loading={updateProjectInfo.isPending}>
              保存
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  )
}
