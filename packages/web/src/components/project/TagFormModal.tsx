import { useState } from 'react'
import { Modal, Button, Group, Stack, TextInput, ColorInput, Alert } from '@mantine/core'
import { useMutation } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import { useForm, zodResolver } from '@mantine/form'
import { z } from 'zod'
import { IconAlertCircle } from '@tabler/icons-react'
import type { MC } from '@/utils/modal'

// 标签表单验证
const tagFormSchema = z.object({
  name: z.string().min(1, '标签名称不能为空'),
  color: z.string().optional(),
  icon: z.string().optional(),
})

// 定义标签类型
interface Tag {
  id: number
  name: string
  color?: string
  icon?: string
}

// 定义模态框属性
interface TagFormModalProps {
  projectId: number
  mode: 'create' | 'edit'
  initialData?: Tag
  onSuccess?: () => void
}

// 标签表单模态框组件
export const TagFormModal: MC<TagFormModalProps> = ({
  projectId,
  mode,
  initialData,
  onSuccess,
  finish,
  cancel,
  bind,
}) => {
  const [error, setError] = useState<string | null>(null)

  // 创建标签
  const createTag = useMutation(trpc.tag.createTag.mutationOptions())

  // 更新标签
  const updateTag = useMutation(trpc.tag.updateTag.mutationOptions())

  // 标签表单
  const form = useForm({
    initialValues: {
      name: initialData?.name || '',
      color: initialData?.color || '#228be6',
      icon: initialData?.icon || '',
    },
    validate: zodResolver(tagFormSchema),
  })

  // 处理表单提交
  const handleSubmit = async (values: typeof form.values) => {
    setError(null)

    try {
      if (mode === 'create') {
        // 创建新标签
        const newTag = await createTag.mutateAsync({
          name: values.name,
          color: values.color,
          icon: values.icon,
          projectId,
        })

        finish()
      } else if (mode === 'edit' && initialData) {
        // 更新标签
        const updatedTag = await updateTag.mutateAsync({
          id: initialData.id,
          name: values.name,
          color: values.color,
          icon: values.icon,
          projectId,
        })

        finish()
      }

      onSuccess?.()
    } catch (error: any) {
      setError(error.message || (mode === 'create' ? '创建标签失败' : '更新标签失败'))
    }
  }

  return (
    <Modal
      opened={bind.visible}
      onClose={bind.onClose}
      title={mode === 'create' ? '添加标签' : '编辑标签'}
      size="md"
    >
      {error && (
        <Alert icon={<IconAlertCircle size="1rem" />} title="错误" color="red" mb="md">
          {error}
        </Alert>
      )}

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <TextInput
            label="标签名称"
            placeholder="输入标签名称"
            required
            {...form.getInputProps('name')}
          />
          <ColorInput
            label="标签颜色"
            placeholder="选择颜色"
            {...form.getInputProps('color')}
            swatches={[
              '#25262b',
              '#868e96',
              '#fa5252',
              '#e64980',
              '#be4bdb',
              '#7950f2',
              '#4c6ef5',
              '#228be6',
              '#15aabf',
              '#12b886',
              '#40c057',
              '#82c91e',
              '#fab005',
              '#fd7e14',
            ]}
          />
          <TextInput
            label="图标"
            placeholder="输入图标名称或代码（可选）"
            {...form.getInputProps('icon')}
          />

          <Group justify="flex-end">
            <Button variant="default" onClick={cancel}>
              取消
            </Button>
            <Button type="submit" loading={createTag.isPending || updateTag.isPending}>
              {mode === 'create' ? '创建' : '更新'}
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  )
}
