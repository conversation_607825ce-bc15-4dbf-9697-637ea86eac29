import { useState } from 'react'
import { Modal, Button, Group, Stack, TextInput, Textarea, Alert, Text, Code } from '@mantine/core'
import { useMutation } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import { useForm, zodResolver } from '@mantine/form'
import { z } from 'zod'
import { IconAlertCircle } from '@tabler/icons-react'
import type { MC } from '@/utils/modal'

// 路径规则表单验证
const pathRuleFormSchema = z.object({
  name: z.string().min(1, '规则名称不能为空'),
  pattern: z.string().min(1, '路径模板不能为空'),
  description: z.string().optional(),
})

// 定义路径规则类型
interface PathRule {
  id: number
  name: string
  pattern: string
  description?: string
}

// 定义模态框属性
interface PathRuleFormModalProps {
  projectId: number
  mode: 'create' | 'edit'
  initialData?: PathRule
  onSuccess?: () => void
}

// 路径规则表单模态框组件
export const PathRuleFormModal: MC<PathRuleFormModalProps> = ({
  projectId,
  mode,
  initialData,
  onSuccess,
  finish,
  cancel,
  bind,
}) => {
  const [error, setError] = useState<string | null>(null)

  // 创建路径规则
  const createPathRule = useMutation(trpc.pathRule.createPathRule.mutationOptions())

  // 更新路径规则
  const updatePathRule = useMutation(trpc.pathRule.updatePathRule.mutationOptions())

  // 路径规则表单
  const form = useForm({
    initialValues: {
      name: initialData?.name || '',
      pattern: initialData?.pattern || '/api/${SERVICE_NAME}/${METHOD_NAME}',
      description: initialData?.description || '',
    },
    validate: zodResolver(pathRuleFormSchema),
  })

  // 处理表单提交
  const handleSubmit = async (values: typeof form.values) => {
    setError(null)

    try {
      if (mode === 'create') {
        // 创建新路径规则
        const newRule = await createPathRule.mutateAsync({
          name: values.name,
          pattern: values.pattern,
          description: values.description,
          projectId,
        })

        finish()
      } else if (mode === 'edit' && initialData) {
        // 更新路径规则
        const updatedRule = await updatePathRule.mutateAsync({
          id: initialData.id,
          name: values.name,
          pattern: values.pattern,
          description: values.description,
          projectId,
        })

        finish()
      }

      onSuccess?.()
    } catch (error: any) {
      setError(error.message || (mode === 'create' ? '创建规则失败' : '更新规则失败'))
    }
  }

  return (
    <Modal
      opened={bind.visible}
      onClose={bind.onClose}
      title={mode === 'create' ? '添加路径规则' : '编辑路径规则'}
      size="md"
    >
      {error && (
        <Alert icon={<IconAlertCircle size="1rem" />} title="错误" color="red" mb="md">
          {error}
        </Alert>
      )}

      <Alert icon={<IconAlertCircle size="1rem" />} color="blue" mb="md">
        <Text size="sm">路径规则用于生成接口的完整访问路径。您可以使用以下占位符：</Text>
        <Text size="xs" mt="xs">
          <Code>${'{SERVICE_NAME}'}</Code> - 服务名称
          <br />
          <Code>${'{METHOD_NAME}'}</Code> - 方法名称
          <br />
          <Code>${'{PROTO_FILE_NAME}'}</Code> - Proto 文件名
          <br />
          <Code>${'{PROJECT_NAME}'}</Code> - 项目名称
          <br />
          <Code>${'{SERVICE_NAME_LOWERCASE}'}</Code> - 小写服务名称
          <br />
          <Code>${'{METHOD_NAME_LOWERCASE}'}</Code> - 小写方法名称
        </Text>
      </Alert>

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <TextInput
            label="规则名称"
            placeholder="输入规则名称"
            required
            {...form.getInputProps('name')}
          />
          <TextInput
            label="路径模板"
            placeholder="例如: /api/${SERVICE_NAME}/${METHOD_NAME}"
            required
            {...form.getInputProps('pattern')}
          />
          <Textarea
            label="描述"
            placeholder="输入规则描述（可选）"
            {...form.getInputProps('description')}
          />

          <Group justify="flex-end">
            <Button variant="default" onClick={cancel}>
              取消
            </Button>
            <Button type="submit" loading={createPathRule.isPending || updatePathRule.isPending}>
              {mode === 'create' ? '创建' : '更新'}
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  )
}
