import { useState, useEffect } from 'react'
import { Avatar, Loader } from '@mantine/core'
import type { AvatarProps } from '@mantine/core'

// 图片加载状态枚举
enum ImageLoadState {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
}

// UserAvatar 组件属性接口
interface UserAvatarProps extends Omit<AvatarProps, 'src' | 'children'> {
  username?: string
  photoSize?: number // 图片尺寸，默认 150
  showLoadingIndicator?: boolean // 是否显示加载指示器
}

// 简单的内存缓存
const imageCache = new Map<string, ImageLoadState>()

// 生成头像图片 URL
const generateAvatarUrl = (username: string, size: number = 150): string => {
  return `https://rhrc.woa.com/photo/${size}/${username}.png`
}

// 检查图片是否可加载
const checkImageAvailability = (url: string): Promise<boolean> => {
  return new Promise(resolve => {
    const img = new Image()
    img.onload = () => resolve(true)
    img.onerror = () => resolve(false)
    img.src = url
  })
}

// 生成用户名首字母显示内容
const getUsernameInitials = (username: string): string => {
  if (!username || username.trim() === '') {
    return 'U'
  }
  // 取用户名前两个字符并转为大写
  return username.substring(0, 2).toUpperCase()
}

export function UserAvatar({
  username,
  photoSize = 150,
  showLoadingIndicator = false,
  ...avatarProps
}: UserAvatarProps) {
  const [imageState, setImageState] = useState<ImageLoadState>(ImageLoadState.IDLE)
  const [imageUrl, setImageUrl] = useState<string>('')

  useEffect(() => {
    if (!username || username.trim() === '') {
      setImageState(ImageLoadState.ERROR)
      return
    }

    const cacheKey = `${username}-${photoSize}`
    const cachedState = imageCache.get(cacheKey)

    // 如果缓存中有结果，直接使用
    if (cachedState) {
      setImageState(cachedState)
      if (cachedState === ImageLoadState.SUCCESS) {
        setImageUrl(generateAvatarUrl(username, photoSize))
      }
      return
    }

    // 开始加载图片
    setImageState(ImageLoadState.LOADING)
    const url = generateAvatarUrl(username, photoSize)

    checkImageAvailability(url)
      .then(isAvailable => {
        const newState = isAvailable ? ImageLoadState.SUCCESS : ImageLoadState.ERROR
        setImageState(newState)
        imageCache.set(cacheKey, newState)

        if (isAvailable) {
          setImageUrl(url)
        }
      })
      .catch(() => {
        setImageState(ImageLoadState.ERROR)
        imageCache.set(cacheKey, ImageLoadState.ERROR)
      })
  }, [username, photoSize])

  // 根据状态返回不同的 Avatar 内容
  const renderAvatarContent = () => {
    switch (imageState) {
      case ImageLoadState.LOADING:
        return showLoadingIndicator ? (
          <Loader size="sm" color="white" />
        ) : (
          getUsernameInitials(username || '')
        )
      case ImageLoadState.SUCCESS:
        return null // src 属性会处理图片显示
      case ImageLoadState.ERROR:
      case ImageLoadState.IDLE:
      default:
        return getUsernameInitials(username || '')
    }
  }

  return (
    <Avatar {...avatarProps} src={imageState === ImageLoadState.SUCCESS ? imageUrl : undefined}>
      {renderAvatarContent()}
    </Avatar>
  )
}
