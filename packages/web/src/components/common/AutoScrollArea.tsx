import { useEffect, useRef } from 'react'
import type { ReactNode } from 'react'
import { ScrollArea } from '@mantine/core'
import type { ScrollAreaProps } from '@mantine/core'

interface UseAutoScrollOptions {
  /** 是否正在更新内容 */
  isUpdating?: boolean
  /** 内容变化的依赖项 */
  content?: string
  /** 底部检测的误差范围（像素） */
  threshold?: number
}

interface UseAutoScrollResult {
  /** ScrollArea的viewport引用 */
  viewportRef: React.RefObject<HTMLDivElement>
  /** 手动滚动到底部 */
  scrollToBottom: () => void
  /** 当前是否应该自动滚动 */
  shouldAutoScroll: boolean
}

/**
 * 智能自动滚动Hook
 * 当内容更新且用户在底部时自动滚动，用户手动滚动离开底部时停止自动滚动
 */
export function useAutoScroll(options: UseAutoScrollOptions = {}): UseAutoScrollResult {
  const { isUpdating = false, content = '', threshold = 5 } = options

  const viewportRef = useRef<HTMLDivElement>(null)
  const shouldAutoScrollRef = useRef(true)
  const userScrolledAwayRef = useRef(false)

  // 检测是否在底部
  const checkIfAtBottom = (): boolean => {
    if (!viewportRef.current) return false
    const { scrollTop, scrollHeight, clientHeight } = viewportRef.current
    return scrollHeight - scrollTop - clientHeight <= threshold
  }

  // 滚动到底部
  const scrollToBottom = (): void => {
    if (viewportRef.current) {
      viewportRef.current.scrollTop = viewportRef.current.scrollHeight
    }
  }

  // 处理用户滚动事件
  const handleScroll = (): void => {
    const isAtBottom = checkIfAtBottom()
    shouldAutoScrollRef.current = isAtBottom

    // 如果用户不在底部，标记为用户主动滚动离开
    if (!isAtBottom) {
      userScrolledAwayRef.current = true
    } else {
      userScrolledAwayRef.current = false
    }
  }

  // 监听内容变化，执行智能自动滚动
  useEffect(() => {
    if (isUpdating && content) {
      // 使用 requestAnimationFrame 确保 DOM 更新完成
      requestAnimationFrame(() => {
        // 再次检查当前状态，确保用户没有在这期间滚动离开
        const isCurrentlyAtBottom = checkIfAtBottom()
        const shouldScroll = shouldAutoScrollRef.current && !userScrolledAwayRef.current

        if (shouldScroll || isCurrentlyAtBottom) {
          scrollToBottom()
        }
      })
    }
  }, [content, isUpdating])

  // 添加滚动事件监听器
  useEffect(() => {
    const viewport = viewportRef.current
    if (viewport) {
      viewport.addEventListener('scroll', handleScroll, { passive: true })
      return () => {
        viewport.removeEventListener('scroll', handleScroll)
      }
    }
  }, [])

  // 重置滚动状态（当开始新的更新时）
  useEffect(() => {
    if (isUpdating) {
      shouldAutoScrollRef.current = true
      userScrolledAwayRef.current = false
    }
  }, [isUpdating])

  return {
    viewportRef,
    scrollToBottom,
    shouldAutoScroll: shouldAutoScrollRef.current,
  } as UseAutoScrollResult
}

interface AutoScrollAreaProps extends Omit<ScrollAreaProps, 'viewportRef'> {
  /** 要显示的内容 */
  children: ReactNode
  /** 是否正在更新内容 */
  isUpdating?: boolean
  /** 用于触发自动滚动的内容依赖 */
  content?: string
  /** 底部检测的误差范围（像素），默认20px */
  threshold?: number
}

/**
 * 智能自动滚动区域组件
 * 包装了Mantine的ScrollArea，添加了智能自动滚动功能
 */
export function AutoScrollArea({
  children,
  isUpdating = false,
  content = '',
  threshold = 20,
  ...scrollAreaProps
}: AutoScrollAreaProps) {
  const { viewportRef } = useAutoScroll({
    isUpdating,
    content,
    threshold,
  })

  return (
    <ScrollArea {...scrollAreaProps} viewportRef={viewportRef}>
      {children}
    </ScrollArea>
  )
}
