import { useState } from 'react'
import {
  Group,
  Text,
  ThemeIcon,
  Divider,
  Stack,
  rem,
  NavLink as MantineNavLink,
  Box,
  Title,
  Menu,
  useMantineColorScheme,
  ActionIcon,
} from '@mantine/core'
import { UserAvatar } from '@/components/common/UserAvatar'
import {
  IconHome,
  IconFileCode,
  IconFolders,
  IconCode,
  IconUser,
  IconChevronRight,
  IconLogout,
  IconSettings,
  IconMoon,
  IconSun,
  IconChevronDown,
  IconApi,
} from '@tabler/icons-react'
import { Link, useRouter } from '@tanstack/react-router'
import { VscodeIconsFileTypeProtobuf } from '../common/icons'
import { useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { useAuthStore } from '@/store/authStore'

interface CustomNavLinkProps {
  icon: React.ReactNode
  label: string
  to: string
  active?: boolean
  opened?: boolean
}

function CustomNavLink({ icon, label, to, active, opened }: CustomNavLinkProps) {
  return (
    <MantineNavLink
      component={Link}
      to={to}
      label={
        opened && (
          <Group>
            <Text size="sm" fw={500}>
              {label}
            </Text>
            {active && <IconChevronRight size={16} style={{ marginLeft: 'auto' }} />}
          </Group>
        )
      }
      leftSection={
        <ThemeIcon size={30} variant="light" color={active ? 'blue' : 'gray'}>
          {icon}
        </ThemeIcon>
      }
      active={active}
    />
  )
}

// User profile component for the bottom of the navbar
function UserProfileFooter({ opened }: { opened: boolean }) {
  const { data: currentUser } = useQuery(trpc.auth.getCurrentUser.queryOptions())
  const { colorScheme, toggleColorScheme } = useMantineColorScheme()
  const authStore = useAuthStore()

  // Handle logout
  const handleLogout = () => {
    authStore.clearAuth()
    // Redirect to home page or login page if needed
    window.location.href = '/'
  }

  if (!currentUser) return null

  return (
    <Box>
      <Divider my="sm" />
      {opened ? (
        // Expanded view with dropdown menu
        <Menu position="top" withArrow offset={12}>
          <Menu.Target>
            <Box style={{ cursor: 'pointer' }} px="sm">
              <Group gap="xs">
                <UserAvatar username={currentUser.username} color="blue" radius="xl" />
                <Box style={{ flex: 1 }}>
                  <Text size="sm" fw={500} lineClamp={1}>
                    {currentUser.username}
                  </Text>
                  <Text size="xs" c="dimmed">
                    {currentUser.chineseName}
                  </Text>
                </Box>
                <IconChevronDown size={16} />
              </Group>
            </Box>
          </Menu.Target>

          <Menu.Dropdown>
            <Menu.Item component={Link} to="/settings" leftSection={<IconSettings size={16} />}>
              个人设置
            </Menu.Item>
            <Menu.Item
              onClick={toggleColorScheme}
              leftSection={colorScheme === 'dark' ? <IconSun size={16} /> : <IconMoon size={16} />}
            >
              {colorScheme === 'dark' ? '浅色模式' : '深色模式'}
            </Menu.Item>
            <Divider />
            <Menu.Item onClick={handleLogout} leftSection={<IconLogout size={16} />} color="red">
              退出登录
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
      ) : (
        // Collapsed view with just the avatar
        <Box px="sm" py="xs">
          <Menu position="right-start" withArrow offset={12}>
            <Menu.Target>
              <UserAvatar
                username={currentUser.username}
                color="blue"
                radius="xl"
                style={{ cursor: 'pointer' }}
              />
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Label>{currentUser.username}</Menu.Label>
              <Menu.Item component={Link} to="/settings" leftSection={<IconSettings size={16} />}>
                个人设置
              </Menu.Item>
              <Menu.Item
                onClick={toggleColorScheme}
                leftSection={
                  colorScheme === 'dark' ? <IconSun size={16} /> : <IconMoon size={16} />
                }
              >
                {colorScheme === 'dark' ? '浅色模式' : '深色模式'}
              </Menu.Item>
              <Divider />
              <Menu.Item onClick={handleLogout} leftSection={<IconLogout size={16} />} color="red">
                退出登录
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Box>
      )}
    </Box>
  )
}

export function AppNavbar({ opened }: { opened: boolean }) {
  const router = useRouter()
  const currentPath = router.state.location.pathname

  const isActive = (path: string) => {
    if (path === '/') {
      return currentPath === '/'
    }
    return currentPath.startsWith(path)
  }

  return (
    <Box
      w={opened ? 240 : 74}
      p="xs"
      style={{ display: 'flex', flexDirection: 'column', height: '100%' }}
    >
      {/* App Logo and Title */}
      <Group component={Link} {...{ to: '/' }} px="sm" className="no-decoration" tabIndex={-1}>
        <VscodeIconsFileTypeProtobuf className="text-[40px]" />
        {opened && <Title order={3}>Proto API</Title>}
      </Group>
      <Divider my="sm" />

      {/* Navigation Links */}
      <Stack gap="xs" style={{ flex: 1 }}>
        <CustomNavLink
          icon={<IconHome size="1.2rem" />}
          label="首页"
          to="/"
          active={isActive('/')}
          opened={opened}
        />
        <CustomNavLink
          icon={<IconFolders size="1.2rem" />}
          label="我的项目"
          to="/projects"
          active={isActive('/projects')}
          opened={opened}
        />
        {/* <CustomNavLink
          icon={<IconFileCode size="1.2rem" />}
          label="Proto 文件"
          to="/protos"
          active={isActive('/protos')}
          opened={opened}
        /> */}
        <CustomNavLink
          icon={<IconCode size="1.2rem" />}
          label="模板中心"
          to="/templates"
          active={isActive('/templates')}
          opened={opened}
        />

        <Divider my="sm" />

        <CustomNavLink
          icon={<IconApi size="1.2rem" />}
          label="API文档"
          to="/api-docs"
          active={isActive('/api-docs')}
          opened={opened}
        />
        <CustomNavLink
          icon={<IconUser size="1.2rem" />}
          label="个人设置"
          to="/settings"
          active={isActive('/settings')}
          opened={opened}
        />
      </Stack>

      {/* User Profile at Bottom */}
      <UserProfileFooter opened={opened} />
    </Box>
  )
}
