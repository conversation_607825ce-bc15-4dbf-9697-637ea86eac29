import { useState } from 'react'
import { Button, Group, Menu, ActionIcon } from '@mantine/core'
import { IconCode, IconChevronDown, IconBrandAdobeIllustrator } from '@tabler/icons-react'
import { useQuery } from '@tanstack/react-query'
import { trpc, type ApiData } from '@/utils/trpc'
import { ScriptTemplateRunner } from './TemplateRunner'
import { AiTemplateRunner } from './AiTemplateRunner'
import type { ExtractComponentsResult } from '../api/common'
import { useModal } from '@/utils/modal'

export type ApiTemplateData = {
  serviceName: string
  methodName: string
}

interface TemplateManagerProps {
  apiData: ApiTemplateData
  projectId: number
}

export function TemplateManager({ apiData, projectId }: TemplateManagerProps) {
  const [scriptModalOpen, setScriptModalOpen] = useState(false)
  const [selectedScriptTemplate, setSelectedScriptTemplate] = useState<
    ApiData['template']['script']['listByProject'][0] | null
  >(null)

  // 使用useModal创建AiTemplateRunner
  const aiTemplateModal = useModal(AiTemplateRunner)
  const scriptModal = useModal(ScriptTemplateRunner)

  // 获取项目关联的脚本模板
  const { data: scriptTemplates } = useQuery(
    trpc.template.script.listByProject.queryOptions(
      { projectId: projectId! },
      { enabled: !!projectId, staleTime: 30_000 },
    ),
  )

  // 获取项目关联的 AI 模板
  const { data: aiTemplates } = useQuery(
    trpc.template.ai.listByProject.queryOptions(
      { projectId: projectId! },
      { enabled: !!projectId, staleTime: 30_000 },
    ),
  )

  // 检查是否有可用模板
  const hasScriptTemplates = (scriptTemplates?.length || 0) > 0
  const hasAiTemplates = (aiTemplates?.length || 0) > 0
  const hasTemplates = hasScriptTemplates || hasAiTemplates

  // 处理脚本模板选择
  const handleScriptTemplateSelect = (
    template: ApiData['template']['script']['listByProject'][0],
  ) => {
    scriptModal.openModal({
      apiData,
      selectedTemplate: template,
      projectId: projectId,
    })
  }

  // 处理AI模板选择
  const handleAiTemplateSelect = (template: ApiData['template']['ai']['listByProject'][0]) => {
    aiTemplateModal.openModal({
      apiData,
      selectedTemplate: template,
      projectId: projectId,
    })
  }

  // 如果没有模板，不显示任何内容
  if (!hasTemplates) {
    return null
  }

  return (
    <>
      {/* 模板按钮组 */}
      {hasTemplates && (
        <Group gap="xs">
          <Menu shadow="md" width={200}>
            <Menu.Target>
              <Button size="xs" rightSection={<IconChevronDown size={16} />}>
                生成代码
              </Button>
            </Menu.Target>

            <Menu.Dropdown>
              {hasAiTemplates && <Menu.Label>AI 模板</Menu.Label>}
              {aiTemplates?.map(template => (
                <Menu.Item
                  key={template.id}
                  leftSection={<IconBrandAdobeIllustrator size={16} />}
                  onClick={() => handleAiTemplateSelect(template)}
                >
                  {template.title}
                </Menu.Item>
              ))}
              {hasScriptTemplates && <Menu.Label>高级模板</Menu.Label>}
              {scriptTemplates?.map(template => (
                <Menu.Item
                  key={template.id}
                  leftSection={<IconCode size={16} />}
                  onClick={() => handleScriptTemplateSelect(template)}
                >
                  {template.title}
                </Menu.Item>
              ))}
            </Menu.Dropdown>
          </Menu>
        </Group>
      )}
    </>
  )
}
