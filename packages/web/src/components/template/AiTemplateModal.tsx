import { useState } from 'react'
import {
  Modal,
  Button,
  Group,
  Stack,
  TextInput,
  Textarea,
  Alert,
  Select,
  MultiSelect,
  Loader,
  Drawer,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import { IconAlertCircle } from '@tabler/icons-react'
import { z } from 'zod'
import { useForm, zodResolver } from '@mantine/form'
import type { MC } from '@/utils/modal'
import { Editor } from '@monaco-editor/react'

// 定义表单 schema
const formSchema = z.object({
  title: z.string().min(1, '模板名称不能为空'),
  prompt: z.string().min(1, '提示词不能为空'),
  description: z.string().optional(),
  auth: z.string().min(1, '请选择权限'),
  projectIds: z.array(z.string()).optional(),
})

// 定义自定义选项类型
interface AiTemplateModalOptions {
  mode: 'add' | 'edit'
  templateId?: number
  initialValues?: {
    title?: string
    prompt?: string
    description?: string
    auth?: string
    projectIds?: string[] | number[]
  }
}

// 定义返回数据类型
interface AiTemplateData {
  id: number
  title: string
}

// AI模板弹框组件
export const AiTemplateModal: MC<AiTemplateModalOptions, AiTemplateData> = ({
  mode,
  templateId,
  initialValues,
  bind,
  finish,
  cancel,
}) => {
  const [error, setError] = useState<string | null>(null)

  // 获取项目列表
  const { data: projects, isLoading: isLoadingProjects } = useQuery(
    trpc.project.list.queryOptions(),
  )

  // 创建模板的 mutation
  const createTemplate = useMutation(trpc.template.ai.create.mutationOptions())

  // 更新模板的 mutation
  const updateTemplate = useMutation(trpc.template.ai.update.mutationOptions())

  // 绑定项目的 mutation
  const bindProject = useMutation(trpc.template.ai.bindProject.mutationOptions())

  // 初始化表单
  const form = useForm({
    initialValues: {
      title: initialValues?.title || '',
      prompt:
        initialValues?.prompt ||
        `# 代码生成提示词

你是一个专业的代码生成助手，请根据以下API定义生成TypeScript客户端代码。

## 要求
1. 生成的代码应该是TypeScript类型安全的
2. 使用fetch API进行网络请求
3. 为每个API方法生成一个独立的函数
4. 为请求和响应类型生成接口定义
5. 添加适当的错误处理
6. 添加详细的注释说明每个函数的用途

## 输出格式
请生成完整的TypeScript代码，包括：
- 类型定义
- API调用函数
- 必要的工具函数

请确保代码可以直接复制使用，不要包含markdown格式。`,
      description: initialValues?.description || '',
      auth: initialValues?.auth || 'PUBLIC',
      projectIds: initialValues?.projectIds
        ? Array.isArray(initialValues.projectIds)
          ? initialValues.projectIds.map(id => String(id))
          : [String(initialValues.projectIds)]
        : [],
    },
    validate: zodResolver(formSchema),
  })

  // 处理表单提交
  const handleSubmit = async (values: typeof form.values) => {
    setError(null)

    try {
      if (mode === 'add') {
        // 创建新模板
        const newTemplate = await createTemplate.mutateAsync({
          title: values.title,
          prompt: values.prompt,
          desc: values.description,
          auth: values.auth as 'PUBLIC' | 'PRIVATE' | 'OPEN',
        })

        // 绑定选中的项目
        if (values.projectIds && values.projectIds.length > 0) {
          // 为每个项目单独调用绑定API
          const bindPromises = values.projectIds.map(async projectId => {
            return bindProject.mutateAsync({
              projectId: parseInt(projectId),
              templateIds: [newTemplate.id],
            })
          })
          await Promise.all(bindPromises)
        }

        // 完成并返回数据
        finish(
          {
            id: newTemplate.id,
            title: newTemplate.title,
          },
          'AI 模板已成功创建',
        )
      } else {
        // 更新现有模板
        if (!templateId) {
          setError('模板ID不存在')
          return
        }

        // 更新模板
        await updateTemplate.mutateAsync({
          id: templateId,
          title: values.title,
          prompt: values.prompt,
          desc: values.description,
          auth: values.auth as 'PUBLIC' | 'PRIVATE' | 'OPEN',
        })

        // 编辑模式下不处理项目绑定

        // 完成并返回数据
        finish(
          {
            id: templateId,
            title: values.title,
          },
          'AI 模板已成功更新',
        )
      }
    } catch (error: any) {
      console.error('操作失败:', error)
      setError(error.message || '操作失败，请重试')
    }
  }

  return (
    <Drawer
      opened={bind.visible}
      onClose={bind.onClose}
      title={mode === 'add' ? '创建 AI 代码生成模板' : '编辑 AI 代码生成模板'}
      size="900"
      position="right"
      closeOnClickOutside={false}
      classNames={{ content: '!flex flex-col', body: 'flex-1' }}
    >
      {error && (
        <Alert icon={<IconAlertCircle size="1rem" />} title="错误" color="red" mb="md">
          {error}
        </Alert>
      )}

      <form
        onSubmit={form.onSubmit(handleSubmit)}
        style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
      >
        <Stack gap="md" style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <Group grow>
            <TextInput
              label="模板名称"
              placeholder="输入模板名称"
              required
              {...form.getInputProps('title')}
            />

            <Select
              label="权限"
              placeholder="选择权限"
              required
              data={[
                { value: 'PRIVATE', label: '私有' },
                { value: 'PUBLIC', label: '公开' },
                { value: 'OPEN', label: '开放（任何人可修改）' },
              ]}
              {...form.getInputProps('auth')}
            />
          </Group>

          {mode === 'add' && (
            <MultiSelect
              label="绑定项目"
              placeholder="选择要绑定的项目（可选）"
              searchable
              nothingFoundMessage="没有找到匹配的项目"
              data={
                projects?.map(project => ({
                  value: project.id.toString(),
                  label: project.title || project.name,
                })) || []
              }
              rightSection={isLoadingProjects ? <Loader size="xs" /> : null}
              disabled={isLoadingProjects}
              {...form.getInputProps('projectIds')}
            />
          )}

          <Textarea
            label="描述"
            placeholder="输入模板描述（可选）"
            minRows={2}
            {...form.getInputProps('description')}
          />

          <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            <label
              style={{ fontSize: '14px', fontWeight: 500, marginBottom: '8px', display: 'block' }}
            >
              提示词 <span style={{ color: 'red' }}>*</span>
            </label>
            <div style={{ flex: 1, overflow: 'hidden', borderRadius: '4px' }}>
              <Editor
                height="100%"
                language="markdown"
                value={form.values.prompt}
                theme="vs-dark"
                onChange={value => form.setFieldValue('prompt', value || '')}
                options={{
                  padding: { bottom: 10, top: 10 },
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 14,
                  tabSize: 2,
                  wordWrap: 'on',
                  lineNumbers: 'on',
                }}
              />
            </div>
          </div>

          <Group justify="flex-end">
            <Button variant="default" onClick={cancel}>
              取消
            </Button>
            <Button
              type="submit"
              loading={mode === 'add' ? createTemplate.isPending : updateTemplate.isPending}
            >
              {mode === 'add' ? '创建' : '保存'}
            </Button>
          </Group>
        </Stack>
      </form>
    </Drawer>
  )
}
