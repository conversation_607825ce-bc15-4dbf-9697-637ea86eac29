import { useState } from 'react'
import {
  Modal,
  Stack,
  Group,
  Text,
  Button,
  Card,
  Grid,
  TextInput,
  Loader,
  Alert,
  Menu,
  ActionIcon,
  Checkbox,
  Title,
  Badge,
  Divider,
  Box,
  ScrollArea,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import {
  IconPlus,
  IconSearch,
  IconUnlink,
  IconDotsVertical,
  IconAlertCircle,
  IconUsers,
} from '@tabler/icons-react'
import { modals } from '@mantine/modals'
import { UserAvatar } from '@/components/common/UserAvatar'
import type { MC } from '@/utils/modal'

interface ProjectBindingModalOptions {
  templateId: number
  templateType: 'script' | 'ai'
  onUpdate?: () => void
}

interface ProjectBindingModalData {
  success: boolean
}

export const ProjectBindingModal: MC<ProjectBindingModalOptions, ProjectBindingModalData> = ({
  templateId,
  templateType,
  onUpdate,
  bind,
  finish,
  cancel,
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProjects, setSelectedProjects] = useState<number[]>([])
  const [showBindingForm, setShowBindingForm] = useState(false)

  // 获取所有项目列表
  const { data: allProjects, isLoading: isLoadingProjects } = useQuery(
    trpc.project.list.queryOptions(),
  )

  // 获取已绑定的项目
  const {
    data: boundProjects,
    isLoading: isLoadingBoundProjects,
    refetch: refetchBoundProjects,
  } = useQuery(
    templateType === 'script'
      ? trpc.template.script.getBoundProjects.queryOptions(
          { templateId },
          { enabled: !!templateId },
        )
      : trpc.template.ai.getBoundProjects.queryOptions({ templateId }, { enabled: !!templateId }),
  )

  // 绑定项目的 mutation
  const bindProjects = useMutation(
    templateType === 'script'
      ? trpc.template.script.bindProject.mutationOptions()
      : trpc.template.ai.bindProject.mutationOptions(),
  )

  // 解绑项目的 mutation
  const unbindProjects = useMutation(
    templateType === 'script'
      ? trpc.template.script.unbindProject.mutationOptions()
      : trpc.template.ai.unbindProject.mutationOptions(),
  )

  // 过滤可绑定的项目（排除已绑定的）
  const availableProjects =
    allProjects
      ?.filter(project => !boundProjects?.some(bound => bound.id === project.id))
      .filter(project => {
        if (!searchQuery) return true
        const query = searchQuery.toLowerCase()
        return (
          project.name.toLowerCase().includes(query) ||
          (project.title && project.title.toLowerCase().includes(query)) ||
          project.repository.toLowerCase().includes(query)
        )
      }) || []

  // 处理项目选择
  const handleProjectSelect = (projectId: number, checked: boolean) => {
    if (checked) {
      setSelectedProjects(prev => [...prev, projectId])
    } else {
      setSelectedProjects(prev => prev.filter(id => id !== projectId))
    }
  }

  // 处理绑定操作
  const handleBind = async () => {
    if (selectedProjects.length === 0) {
      notifications.show({
        title: '提示',
        message: '请至少选择一个项目进行绑定',
        color: 'yellow',
      })
      return
    }

    try {
      // 为每个项目单独调用绑定API
      const bindPromises = selectedProjects.map(async projectId => {
        return bindProjects.mutateAsync({
          projectId,
          templateIds: [templateId],
        })
      })
      await Promise.all(bindPromises)

      notifications.show({
        title: '绑定成功',
        message: `已成功绑定 ${selectedProjects.length} 个项目`,
        color: 'green',
      })

      setSelectedProjects([])
      setShowBindingForm(false)
      refetchBoundProjects()
      onUpdate?.()
    } catch (error: any) {
      console.error('绑定项目失败:', error)
      notifications.show({
        title: '绑定失败',
        message: error.message || '绑定项目失败，请重试',
        color: 'red',
      })
    }
  }

  // 处理解绑操作
  const handleUnbind = (project: any) => {
    modals.openConfirmModal({
      title: '确认解绑',
      children: (
        <Text size="sm">
          确定要解绑项目 <strong>{project.title || project.name}</strong>{' '}
          吗？解绑后该模板将不再与此项目关联。
        </Text>
      ),
      labels: { confirm: '解绑', cancel: '取消' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          await unbindProjects.mutateAsync({
            projectId: project.id,
            templateIds: [templateId],
          })

          notifications.show({
            title: '解绑成功',
            message: '项目已成功解绑',
            color: 'green',
          })

          refetchBoundProjects()
          onUpdate?.()
        } catch (error: any) {
          console.error('解绑项目失败:', error)
          notifications.show({
            title: '解绑失败',
            message: error.message || '解绑项目失败，请重试',
            color: 'red',
          })
        }
      },
    })
  }

  const isLoading = bindProjects.isPending || unbindProjects.isPending

  return (
    <Modal opened={bind.visible} onClose={bind.onClose} title="项目绑定管理" size="800" centered>
      <Stack gap="md">
        {/* 已绑定项目统计 */}
        <Group justify="space-between">
          <Group gap="xs">
            <IconUsers size={16} />
            <Text fw={500}>已绑定项目</Text>
            <Badge size="sm" variant="light">
              {boundProjects?.length || 0}
            </Badge>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            variant="light"
            size="sm"
            onClick={() => setShowBindingForm(!showBindingForm)}
            disabled={isLoading}
          >
            {showBindingForm ? '取消绑定' : '绑定项目'}
          </Button>
        </Group>

        <Divider />

        {/* 已绑定项目列表 */}
        {isLoadingBoundProjects ? (
          <Group justify="center" p="md">
            <Loader size="sm" />
          </Group>
        ) : boundProjects && boundProjects.length > 0 ? (
          <Box mah={400} style={{ overflowY: 'auto' }} component={ScrollArea}>
            <div className="!p-1 grid grid-cols-[repeat(auto-fill,minmax(200px,1fr))] gap-2">
              {boundProjects.map(project => (
                <Card shadow="xs" padding="sm" radius="md" withBorder key={project.id}>
                  <Group justify="space-between">
                    <div style={{ flex: 1 }}>
                      <Text fw={500} size="sm">
                        {project.title || project.name}
                      </Text>
                      <Text size="xs" c="dimmed">
                        {project.repository}
                      </Text>
                    </div>
                    <ActionIcon
                      size="sm"
                      variant="subtle"
                      color="red"
                      onClick={() => handleUnbind(project)}
                      disabled={isLoading}
                    >
                      <IconUnlink size={14} />
                    </ActionIcon>
                  </Group>
                </Card>
              ))}
            </div>
          </Box>
        ) : (
          <Alert icon={<IconAlertCircle size="1rem" />} color="gray">
            该模板暂未绑定任何项目
          </Alert>
        )}

        {/* 绑定项目表单 */}
        {showBindingForm && (
          <>
            <Divider />
            <Stack gap="md">
              <Text fw={500} size="sm">
                选择要绑定的项目
              </Text>

              <TextInput
                placeholder="搜索项目..."
                value={searchQuery}
                onChange={e => setSearchQuery(e.currentTarget.value)}
                leftSection={<IconSearch size={16} />}
              />

              {isLoadingProjects ? (
                <Group justify="center" p="md">
                  <Loader size="sm" />
                </Group>
              ) : availableProjects.length > 0 ? (
                <Box mah={400} style={{ overflowY: 'auto' }} component={ScrollArea}>
                  <div className="!p-1 grid grid-cols-[repeat(auto-fill,minmax(200px,1fr))] gap-2">
                    {availableProjects.map(project => (
                      <Card
                        key={project.id}
                        shadow="xs"
                        padding="sm"
                        radius="md"
                        withBorder
                        style={{
                          cursor: 'pointer',
                        }}
                        bg={selectedProjects.includes(project.id) ? 'blue.0' : undefined}
                        bd={
                          selectedProjects.includes(project.id)
                            ? '1px solid var(--mantine-color-blue-4)'
                            : undefined
                        }
                        onClick={() =>
                          handleProjectSelect(project.id, !selectedProjects.includes(project.id))
                        }
                      >
                        <Group gap="sm">
                          <Checkbox
                            checked={selectedProjects.includes(project.id)}
                            onChange={e => handleProjectSelect(project.id, e.currentTarget.checked)}
                            onClick={e => e.stopPropagation()}
                          />
                          <div style={{ flex: 1 }}>
                            <Text fw={500} size="sm" lineClamp={1}>
                              {project.title || project.name}
                            </Text>
                            <Text size="xs" c="dimmed">
                              {project.repository}
                            </Text>
                          </div>
                        </Group>
                      </Card>
                    ))}
                  </div>
                </Box>
              ) : (
                <Alert icon={<IconAlertCircle size="1rem" />} color="gray">
                  {searchQuery ? '没有找到匹配的项目' : '没有可绑定的项目'}
                </Alert>
              )}

              {availableProjects.length > 0 && (
                <Group justify="flex-end">
                  <Button variant="light" onClick={() => setShowBindingForm(false)}>
                    取消
                  </Button>
                  <Button
                    onClick={handleBind}
                    loading={isLoading}
                    disabled={selectedProjects.length === 0}
                  >
                    绑定选中的项目 ({selectedProjects.length})
                  </Button>
                </Group>
              )}
            </Stack>
          </>
        )}

        {/* 底部按钮 */}
        <Group justify="flex-end" mt="md">
          <Button variant="light" onClick={bind.onClose}>
            关闭
          </Button>
        </Group>
      </Stack>
    </Modal>
  )
}
