import { useState, useEffect, useRef } from 'react'
import { <PERSON>ton, Card, Group, Stack, Text, Alert, Loader, Modal } from '@mantine/core'
import { CodeHighlight } from '@mantine/code-highlight'
import { IconAlertCircle, IconCheck, IconCopy } from '@tabler/icons-react'
import { useMutation } from '@tanstack/react-query'
import { trpc, trpcSubscriptionClient, type ApiData } from '@/utils/trpc'
import type { ApiTemplateData } from './TemplateManager'
import { executeScript } from '@/utils/script-executor'
import type { MC } from '@/utils/modal'
import { fixGeneratedCode } from '@/utils/ai'
import { AutoScrollArea } from '@/components/common/AutoScrollArea'

interface ScriptTemplateRunnerProps {
  apiData: ApiTemplateData
  selectedTemplate: ApiData['template']['script']['listByProject'][0] | null
  projectId: number
}

// AI模板执行器组件
export const ScriptTemplateRunner: MC<ScriptTemplateRunnerProps> = props => {
  const { apiData, selectedTemplate, projectId } = props
  const [generatedCode, setGeneratedCode] = useState<string>('')
  const [error, setError] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const subscriptionRef = useRef<{ unsubscribe: () => void } | null>(null)

  // 监听模态框打开状态
  useEffect(() => {
    // 清理现有订阅
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe()
      subscriptionRef.current = null
    }

    if (!props.bind.visible) {
      // 如果模态框关闭，重置状态
      setGeneratedCode('')
      setError(null)
      setIsGenerating(false)
      return
    }

    if (!selectedTemplate || !apiData) {
      setError('缺少模板或API数据')
      return
    }

    // 模态框打开且有模板和数据，开始生成
    setGeneratedCode('')
    setError(null)
    setIsGenerating(true)

    console.log('selectedTemplate', selectedTemplate)
    console.log('apiData', apiData)

    // 使用trpcClient调用执行模板的API
    const subscription = trpcSubscriptionClient.template.script.execute.subscribe(
      {
        id: selectedTemplate.id,
        projectId: projectId,
        serviceName: apiData.serviceName,
        methodName: apiData.methodName,
      },
      {
        onData(data: { data: string; id: string }) {
          if (data.id === 'error') {
            setError(data.data)
            setIsGenerating(false)
            return
          }
          setGeneratedCode(prev => prev + data.data)
        },
        onError(err: Error) {
          console.error('AI 模板执行失败:', err)
          setError(`执行失败: ${err.message}`)
          setIsGenerating(false)
        },
        onComplete() {
          console.log('onComplete')
          setIsGenerating(false)
          // 延迟一点再复制，确保所有数据都已经渲染
          setTimeout(() => {
            if (generatedCode) {
              navigator.clipboard.writeText(generatedCode)
              setCopied(true)
              setTimeout(() => setCopied(false), 2000)
            }
          }, 100)
        },
      },
    )

    // 保存订阅引用
    subscriptionRef.current = subscription

    // 清理函数
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe()
        subscriptionRef.current = null
      }
    }
  }, [props.bind.visible, selectedTemplate, apiData])

  // 复制生成的代码到剪贴板
  const handleCopyCode = () => {
    if (generatedCode) {
      navigator.clipboard.writeText(generatedCode)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  // 取消生成
  const handleCancel = () => {
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe()
      subscriptionRef.current = null
    }
    setIsGenerating(false)
    props.cancel()
  }

  const pureCode = fixGeneratedCode(generatedCode)

  return (
    <Modal
      opened={props.bind.visible}
      onClose={props.bind.onClose}
      title={selectedTemplate ? `生成代码: ${selectedTemplate.title}` : 'AI 代码生成器'}
      size="xl"
      centered
      overlayProps={{ blur: 3 }}
    >
      <Stack gap="md">
        {/* {isGenerating && (
          <Button variant="light" color="red" onClick={handleCancel}>
            取消生成
          </Button>
        )} */}

        {/* 错误信息 */}
        {error && (
          <Alert icon={<IconAlertCircle size="1rem" />} title="生成失败" color="red">
            {error}
          </Alert>
        )}

        {/* 生成的代码 */}
        {(pureCode || isGenerating) && (
          <Stack gap="md" className="relative">
            <Button
              className="!absolute top-2 right-2 z-10 w-auto"
              size="xs"
              variant="light"
              leftSection={copied ? <IconCheck size={14} /> : <IconCopy size={14} />}
              onClick={handleCopyCode}
              color={copied ? 'green' : 'blue'}
            >
              {copied ? '已复制' : '复制代码'}
            </Button>
            <AutoScrollArea
              h={'calc(100vh - 300px)'}
              scrollbarSize={6}
              isUpdating={isGenerating}
              content={pureCode}
            >
              <CodeHighlight
                code={pureCode || '// AI 正在生成代码...'}
                language="typescript"
                withCopyButton={false}
              />
            </AutoScrollArea>
          </Stack>
        )}
      </Stack>
    </Modal>
  )
}
