import { <PERSON><PERSON>, Group, Text, Badge, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Title } from '@mantine/core'
import { useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { IconSettings, IconAlertCircle, IconUsers } from '@tabler/icons-react'
import { useModal } from '@/utils/modal'
import { ProjectBindingModal } from './ProjectBindingModal'

interface ProjectBindingManagerProps {
  templateId: number
  templateType: 'script' | 'ai'
  onUpdate?: () => void
}

export const ProjectBindingManager = ({
  templateId,
  templateType,
  onUpdate,
}: ProjectBindingManagerProps) => {
  // 获取已绑定的项目
  const {
    data: boundProjects,
    isLoading: isLoadingBoundProjects,
    refetch: refetchBoundProjects,
  } = useQuery(
    templateType === 'script'
      ? trpc.template.script.getBoundProjects.queryOptions(
          { templateId },
          { enabled: !!templateId },
        )
      : trpc.template.ai.getBoundProjects.queryOptions({ templateId }, { enabled: !!templateId }),
  )

  // 使用 useModal 创建项目绑定管理弹框
  const projectBindingModal = useModal(ProjectBindingModal)

  // 处理打开项目绑定管理弹框
  const handleOpenBindingModal = () => {
    projectBindingModal.openModal({
      templateId,
      templateType,
      onUpdate: () => {
        refetchBoundProjects()
        onUpdate?.()
      },
    })
  }

  return (
    <Stack gap="md">
      <Group justify="space-between">
        <Title order={4}>绑定项目</Title>
        <Button
          leftSection={<IconSettings size={16} />}
          variant="light"
          onClick={handleOpenBindingModal}
        >
          管理绑定项目
        </Button>
      </Group>

      {/* 绑定项目统计和简要信息 */}
      {isLoadingBoundProjects ? (
        <Group justify="center" p="md">
          <Loader size="sm" />
        </Group>
      ) : (
        <Card shadow="sm" padding="md" radius="md" withBorder>
          <Group justify="space-between" align="center">
            <Group gap="sm">
              <IconUsers size={20} />
              <div>
                <Text fw={500}>已绑定项目</Text>
                <Text size="sm" c="dimmed">
                  {boundProjects && boundProjects.length > 0
                    ? `共 ${boundProjects.length} 个项目`
                    : '暂未绑定任何项目'}
                </Text>
              </div>
            </Group>
            <Badge size="lg" variant="light" color="blue">
              {boundProjects?.length || 0}
            </Badge>
          </Group>

          {/* 显示前几个绑定的项目名称 */}
          {boundProjects && boundProjects.length > 0 && (
            <Group gap="xs" mt="sm">
              {boundProjects.slice(0, 3).map(project => (
                <Badge key={project.id} size="sm" variant="outline">
                  {project.title || project.name}
                </Badge>
              ))}
              {boundProjects.length > 3 && (
                <Badge size="sm" variant="outline" color="gray">
                  +{boundProjects.length - 3} 个
                </Badge>
              )}
            </Group>
          )}
        </Card>
      )}
    </Stack>
  )
}
