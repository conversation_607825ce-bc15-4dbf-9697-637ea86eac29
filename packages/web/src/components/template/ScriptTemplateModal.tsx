import { useState } from 'react'
import {
  Modal,
  Button,
  Group,
  Stack,
  TextInput,
  Textarea,
  Alert,
  Select,
  MultiSelect,
  Loader,
  Tabs,
  Drawer,
} from '@mantine/core'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import { IconAlertCircle, IconBrandJavascript, IconBrandTypescript } from '@tabler/icons-react'
import { z } from 'zod'
import { useForm, zodResolver } from '@mantine/form'
import type { MC } from '@/utils/modal'
import { CodeHighlightTabs } from '@mantine/code-highlight'
import { Editor } from '@monaco-editor/react'

// 定义表单 schema
const formSchema = z.object({
  title: z.string().min(1, '模板名称不能为空'),
  script: z.string().min(1, '脚本内容不能为空'),
  description: z.string().optional(),
  auth: z.string().min(1, '请选择权限'),
  lang: z.string().min(1, '请选择语言'),
  projectIds: z.array(z.string()).optional(),
  optimizePrompt: z.string().optional(),
})

// 定义自定义选项类型
interface ScriptTemplateModalOptions {
  mode: 'add' | 'edit'
  templateId?: number
  initialValues?: {
    title?: string
    script?: string
    description?: string
    auth?: string
    lang?: string
    projectIds?: string[] | number[]
    optimizePrompt?: string
  }
}

// 定义返回数据类型
interface ScriptTemplateData {
  id: number
  title: string
}

// 脚本模板弹框组件
export const ScriptTemplateModal: MC<ScriptTemplateModalOptions, ScriptTemplateData> = ({
  mode,
  templateId,
  initialValues,
  bind,
  finish,
  cancel,
}) => {
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<string | null>('script')

  // 获取项目列表
  const { data: projects, isLoading: isLoadingProjects } = useQuery(
    trpc.project.list.queryOptions(),
  )

  // 创建模板的 mutation
  const createTemplate = useMutation(trpc.template.script.create.mutationOptions())

  // 更新模板的 mutation
  const updateTemplate = useMutation(trpc.template.script.update.mutationOptions())

  // 绑定项目的 mutation
  const bindProject = useMutation(trpc.template.script.bindProject.mutationOptions())

  // 初始化表单
  const form = useForm({
    initialValues: {
      title: initialValues?.title || '',
      script:
        initialValues?.script ||
        `/**
 * 代码生成模板
 *
 * 此函数接收 API 数据作为参数，并返回生成的代码
 * @param {ApiDataInfo} apiData - API 数据，包含服务、消息和枚举定义
 * @returns {string} 生成的代码
 */
function generate(apiData: ApiDataInfo): string {
  // 提取服务、消息和枚举
  const {
    messages,
    methodComment,
    methodName,
    enums,
    packageName,
    requestType,
    responseType,
    serviceComment,
    serviceName,
  } = apiData;

  // 生成代码的逻辑
  let code = \`export const METHOD_NAME_CAMEL_CASE = (params: \${requestType}): Promise<\${responseType}> => request({
  method: "post",
  url: \\\`/\${serviceName}/\${methodName}\\\`,
});\`;

  return code;
}`,
      description: initialValues?.description || '',
      auth: initialValues?.auth || 'PUBLIC',
      lang: initialValues?.lang || 'TS',
      projectIds: initialValues?.projectIds
        ? Array.isArray(initialValues.projectIds)
          ? initialValues.projectIds.map(id => String(id))
          : [String(initialValues.projectIds)]
        : [],
      optimizePrompt:
        initialValues?.optimizePrompt ||
        `- 使用合理的变量名，小驼峰
- 补充请求体和返回体 ts interface 类型，并导出
`,
    },
    validate: zodResolver(formSchema),
  })

  // 处理表单提交
  const handleSubmit = async (values: typeof form.values) => {
    setError(null)

    try {
      if (mode === 'add') {
        // 创建新模板
        const newTemplate = await createTemplate.mutateAsync({
          title: values.title,
          script: values.script,
          desc: values.description,
          auth: values.auth as 'PUBLIC' | 'PRIVATE' | 'OPEN',
          lang: values.lang as 'JS' | 'TS',
          optimizePrompt: values.optimizePrompt || undefined,
        })

        // 绑定选中的项目
        if (values.projectIds && values.projectIds.length > 0) {
          // 为每个项目单独调用绑定API
          const bindPromises = values.projectIds.map(async projectId => {
            return bindProject.mutateAsync({
              projectId: parseInt(projectId),
              templateIds: [newTemplate.id],
            })
          })
          await Promise.all(bindPromises)
        }

        // 完成并返回数据
        finish(
          {
            id: newTemplate.id,
            title: newTemplate.title,
          },
          '脚本模板已成功创建',
        )
      } else {
        // 更新现有模板
        if (!templateId) {
          setError('模板ID不存在')
          return
        }

        // 更新模板
        await updateTemplate.mutateAsync({
          id: templateId,
          title: values.title,
          script: values.script,
          desc: values.description,
          auth: values.auth as 'PUBLIC' | 'PRIVATE' | 'OPEN',
          lang: values.lang as 'JS' | 'TS',
          optimizePrompt: values.optimizePrompt || undefined,
        })

        // 编辑模式下不处理项目绑定

        // 完成并返回数据
        finish(
          {
            id: templateId,
            title: values.title,
          },
          '脚本模板已成功更新',
        )
      }
    } catch (error: any) {
      console.error('操作失败:', error)
      setError(error.message || '操作失败，请重试')
    }
  }

  return (
    <Drawer
      opened={bind.visible}
      onClose={bind.onClose}
      title={mode === 'add' ? '创建脚本模板' : '编辑脚本模板'}
      size="900"
      position="right"
      closeOnClickOutside={false}
      classNames={{ content: '!flex flex-col', body: 'flex-1' }}
    >
      {error && (
        <Alert icon={<IconAlertCircle size="1rem" />} title="错误" color="red" mb="md">
          {error}
        </Alert>
      )}

      <form
        onSubmit={form.onSubmit(handleSubmit)}
        style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
      >
        <Stack gap="md" style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <Group grow>
            <TextInput
              label="模板名称"
              placeholder="输入模板名称"
              required
              {...form.getInputProps('title')}
            />

            <Group grow>
              <Select
                label="权限"
                placeholder="选择权限"
                required
                data={[
                  { value: 'PRIVATE', label: '私有' },
                  { value: 'PUBLIC', label: '公开' },
                  { value: 'OPEN', label: '开放（任何人可修改）' },
                ]}
                {...form.getInputProps('auth')}
              />

              <Select
                label="语言"
                placeholder="选择语言"
                required
                data={[
                  { value: 'JS', label: 'JavaScript' },
                  { value: 'TS', label: 'TypeScript' },
                ]}
                leftSection={
                  form.values.lang === 'TS' ? (
                    <IconBrandTypescript size={16} />
                  ) : (
                    <IconBrandJavascript size={16} />
                  )
                }
                {...form.getInputProps('lang')}
              />
            </Group>
          </Group>

          {mode === 'add' && (
            <MultiSelect
              label="绑定项目"
              placeholder="选择要绑定的项目（可选）"
              searchable
              nothingFoundMessage="没有找到匹配的项目"
              data={
                projects?.map(project => ({
                  value: project.id.toString(),
                  label: project.title || project.name,
                })) || []
              }
              rightSection={isLoadingProjects ? <Loader size="xs" /> : null}
              disabled={isLoadingProjects}
              {...form.getInputProps('projectIds')}
            />
          )}

          <Textarea
            label="描述"
            placeholder="输入模板描述（可选）"
            minRows={2}
            {...form.getInputProps('description')}
          />

          <Tabs
            value={activeTab}
            onChange={setActiveTab}
            style={{ flex: 1, display: 'flex', flexDirection: 'column' }}
          >
            <Tabs.List>
              <Tabs.Tab value="script">脚本代码</Tabs.Tab>
              <Tabs.Tab value="optimize">AI优化（可选）</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel
              value="script"
              pt="md"
              style={{ flex: 1, display: 'flex', flexDirection: 'column' }}
            >
              <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                <label
                  style={{
                    fontSize: '14px',
                    fontWeight: 500,
                    marginBottom: '8px',
                    display: 'block',
                  }}
                >
                  脚本内容 <span style={{ color: 'red' }}>*</span>
                </label>
                <div style={{ flex: 1, overflow: 'hidden', borderRadius: '4px' }}>
                  <Editor
                    height="100%"
                    language={form.values.lang === 'TS' ? 'typescript' : 'javascript'}
                    value={form.values.script}
                    theme="vs-dark"
                    onChange={value => form.setFieldValue('script', value || '')}
                    options={{
                      padding: { bottom: 10, top: 10 },
                      minimap: { enabled: false },
                      scrollBeyondLastLine: false,
                      fontSize: 14,
                      tabSize: 2,
                      tabCompletion: 'on',
                      quickSuggestions: true,
                      suggestOnTriggerCharacters: true,
                      acceptSuggestionOnEnter: 'on',
                    }}
                    beforeMount={monaco => {
                      monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
                        noSemanticValidation: false,
                        noSyntaxValidation: false,
                      })
                      monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
                        noSemanticValidation: false,
                        noSyntaxValidation: false,
                      })
                    }}
                  />
                </div>
              </div>
            </Tabs.Panel>

            <Tabs.Panel
              value="optimize"
              pt="md"
              style={{ flex: 1, display: 'flex', flexDirection: 'column' }}
            >
              <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                <label
                  style={{
                    fontSize: '14px',
                    fontWeight: 500,
                    marginBottom: '8px',
                    display: 'block',
                  }}
                >
                  AI 优化提示词
                </label>
                <div style={{ flex: 1, overflow: 'hidden', borderRadius: '4px' }}>
                  <Editor
                    height="100%"
                    language="markdown"
                    value={form.values.optimizePrompt}
                    theme="vs-dark"
                    onChange={value => form.setFieldValue('optimizePrompt', value || '')}
                    options={{
                      padding: { bottom: 10, top: 10 },
                      minimap: { enabled: false },
                      scrollBeyondLastLine: false,
                      fontSize: 14,
                      tabSize: 2,
                      wordWrap: 'on',
                      lineNumbers: 'on',
                    }}
                  />
                </div>
              </div>
            </Tabs.Panel>
          </Tabs>

          <Group justify="flex-end">
            <Button variant="default" onClick={cancel}>
              取消
            </Button>
            <Button
              type="submit"
              loading={mode === 'add' ? createTemplate.isPending : updateTemplate.isPending}
            >
              {mode === 'add' ? '创建' : '保存'}
            </Button>
          </Group>
        </Stack>
      </form>
    </Drawer>
  )
}
