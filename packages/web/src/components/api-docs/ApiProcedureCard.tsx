import {
  Paper,
  Text,
  Badge,
  Group,
  Box,
  Code,
  CopyButton,
  ActionIcon,
  Tooltip,
  Table,
  Stack,
} from '@mantine/core'
import { IconCopy, IconCheck } from '@tabler/icons-react'

interface ApiProcedure {
  inputSchema: {
    type: string
    properties: Record<string, any>
    required?: string[]
    additionalProperties: boolean
    $schema: string
  }
  nodeType: 'procedure'
  procedureType: 'query' | 'mutation'
  pathFromRootRouter: string[]
  extraData: {
    parameterDescriptions: Record<string, string>
  }
}

interface ApiProcedureCardProps {
  name: string
  procedure: ApiProcedure
  useRealToken: boolean
  userToken?: string
}

export function ApiProcedureCard({
  name,
  procedure,
  useRealToken,
  userToken,
}: ApiProcedureCardProps) {
  const path = procedure.pathFromRootRouter.join('.')
  const isQuery = procedure.procedureType === 'query'
  const method = isQuery ? 'GET' : 'POST'

  const generateCurlCommand = () => {
    const hasParams =
      procedure.inputSchema.properties && Object.keys(procedure.inputSchema.properties).length > 0
    const token = useRealToken && userToken ? userToken : 'YOUR_TOKEN'

    if (isQuery) {
      // GET请求，参数放在URL中
      let queryParams = ''
      if (hasParams) {
        const exampleParams: Record<string, any> = {}
        Object.entries(procedure.inputSchema.properties).forEach(([key, value]) => {
          switch (value.type) {
            case 'string':
              exampleParams[key] = 'example_string'
              break
            case 'number':
              exampleParams[key] = 123
              break
            case 'boolean':
              exampleParams[key] = true
              break
            default:
              exampleParams[key] = 'example_value'
          }
        })
        const input = JSON.stringify({ '0': { json: exampleParams } })
        queryParams = `?input=${encodeURIComponent(input)}`
      } else {
        queryParams = `?input=${encodeURIComponent('{"0":{"json":{}}}')}`
      }

      return `curl -X GET "${window.location.origin}/api/trpc/${path}${queryParams}" \\
  -H "Authorization: Bearer ${token}"`
    } else {
      // POST请求，参数放在body中
      let body = '{"0":{"json":{}}}'
      if (hasParams) {
        const exampleParams: Record<string, any> = {}
        Object.entries(procedure.inputSchema.properties).forEach(([key, value]) => {
          switch (value.type) {
            case 'string':
              exampleParams[key] = 'example_string'
              break
            case 'number':
              exampleParams[key] = 123
              break
            case 'boolean':
              exampleParams[key] = true
              break
            default:
              exampleParams[key] = 'example_value'
          }
        })
        body = JSON.stringify({ '0': { json: exampleParams } })
      }

      return `curl -X POST "${window.location.origin}/api/trpc/${path}" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${token}" \\
  -d '${body}'`
    }
  }

  const renderParametersTable = () => {
    if (
      !procedure.inputSchema.properties ||
      Object.keys(procedure.inputSchema.properties).length === 0
    ) {
      return (
        <Text size="sm" c="dimmed">
          无参数
        </Text>
      )
    }

    const rows = Object.entries(procedure.inputSchema.properties).map(([key, value]) => (
      <Table.Tr key={key}>
        <Table.Td>
          <Code>{key}</Code>
        </Table.Td>
        <Table.Td>
          <Badge size="xs" variant="light" color={value.type === 'string' ? 'blue' : 'green'}>
            {value.type}
          </Badge>
        </Table.Td>
        <Table.Td>
          {procedure.inputSchema.required?.includes(key) ? (
            <Badge size="xs" color="red" variant="light">
              必填
            </Badge>
          ) : (
            <Badge size="xs" color="gray" variant="light">
              可选
            </Badge>
          )}
        </Table.Td>
        <Table.Td>
          <Text size="sm" c="dimmed">
            {value.description || '-'}
          </Text>
        </Table.Td>
      </Table.Tr>
    ))

    return (
      <Table>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>参数名</Table.Th>
            <Table.Th>类型</Table.Th>
            <Table.Th>必填</Table.Th>
            <Table.Th>描述</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>
    )
  }

  return (
    <Paper p="md" withBorder radius="md" mb="md">
      <Stack gap="md">
        {/* 接口标题和操作 */}
        <Group justify="space-between" align="flex-start">
          <Box>
            <Group gap="xs" mb="xs">
              <Badge size="sm" color={isQuery ? 'blue' : 'orange'}>
                {method}
              </Badge>
              <Text fw={500} size="lg">
                {name}
              </Text>
            </Group>
            <Code>
              <Text component="span" c="dimmed" size="xs">
                {location.origin}/api/trpc/
              </Text>
              <Text component="span" fw={700} size="xs">
                {path}
              </Text>
            </Code>
          </Box>
          <CopyButton value={generateCurlCommand()}>
            {({ copied, copy }) => (
              <Tooltip label={copied ? '已复制curl命令' : '复制curl命令'}>
                <ActionIcon color={copied ? 'teal' : 'blue'} variant="light" onClick={copy}>
                  {copied ? <IconCheck size={14} /> : <IconCopy size={14} />}
                </ActionIcon>
              </Tooltip>
            )}
          </CopyButton>
        </Group>

        {/* 请求方法说明 */}
        {/* <Group gap="xs">
          <Text size="sm" c="dimmed">
            请求方法：
          </Text>
          <Badge size="sm" color={isQuery ? 'blue' : 'orange'}>
            {method}
          </Badge>
          <Text size="xs" c="dimmed">
            {isQuery ? '(Query接口固定使用GET方法)' : '(Mutation接口固定使用POST方法)'}
          </Text>
        </Group> */}

        {/* 参数表格 */}
        <Box>
          <Text size="sm" fw={500} mb="xs">
            请求参数：
          </Text>
          {renderParametersTable()}
        </Box>
      </Stack>
    </Paper>
  )
}
