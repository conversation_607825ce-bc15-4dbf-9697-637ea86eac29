import { Group, Switch, Tooltip } from '@mantine/core'
import { IconKey } from '@tabler/icons-react'
import { useEffect, useState } from 'react'

interface TokenSettingsProps {
  hasToken: boolean
  onToggle: (useRealToken: boolean) => void
}

export function TokenSettings({ hasToken, onToggle }: TokenSettingsProps) {
  const [useRealToken, setUseRealToken] = useState(false)

  // 从sessionStorage读取设置
  useEffect(() => {
    const saved = sessionStorage.getItem('api-docs-use-real-token')
    if (saved && hasToken) {
      setUseRealToken(JSON.parse(saved))
    }
  }, [hasToken])

  // 保存设置到sessionStorage
  const handleToggle = (checked: boolean) => {
    setUseRealToken(checked)
    sessionStorage.setItem('api-docs-use-real-token', JSON.stringify(checked))
    onToggle(checked)
  }

  // 如果用户没有token，不显示开关
  if (!hasToken) {
    return null
  }

  return (
    <Group gap="xs">
      <Tooltip label="开启后，curl命令中将使用填入的真实Token，否则使用占位符">
        <div>
          <Switch
            size="sm"
            label="使用真实Token"
            checked={useRealToken}
            onChange={event => handleToggle(event.currentTarget.checked)}
          />
        </div>
      </Tooltip>
    </Group>
  )
}
