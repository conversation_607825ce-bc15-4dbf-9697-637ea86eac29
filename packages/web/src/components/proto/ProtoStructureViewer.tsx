import { useState } from 'react'
import {
  Accordion,
  Badge,
  Box,
  Button,
  Group,
  Paper,
  Stack,
  Text,
  Title,
  Tabs,
  Tooltip,
  ActionIcon,
} from '@mantine/core'
import { IconCode, IconMessage, IconList, IconInfoCircle } from '@tabler/icons-react'
import MessageViewer from './MessageViewer'
import EnumViewer from './EnumViewer'

interface ProtoStructureViewerProps {
  parsedData: any
}

const ProtoStructureViewer = ({ parsedData }: ProtoStructureViewerProps) => {
  const [expandedService, setExpandedService] = useState<string | null>(null)

  // Extract root package name
  const rootPackage = parsedData?.package || ''

  // 递归提取所有消息、枚举和服务
  const extractComponents = (nested: Record<string, any>, prefix = '') => {
    let messages: Record<string, any> = {}
    let enums: Record<string, any> = {}
    let services: Record<string, any> = {}
    let serviceNames: string[] = []

    for (const key in nested) {
      const fullName = prefix ? `${prefix}.${key}` : key
      const item = nested[key]

      if (item.fields) {
        // 这是一个消息
        messages[fullName] = item
      } else if (item.values) {
        // 这是一个枚举
        enums[fullName] = item
      } else if (item.methods) {
        // 这是一个服务
        services[fullName] = item
        serviceNames.push(fullName)
      }

      // 递归处理嵌套项
      if (item.nested) {
        const {
          messages: nestedMessages,
          enums: nestedEnums,
          services: nestedServices,
          serviceNames: nestedServiceNames,
        } = extractComponents(item.nested, fullName)

        messages = { ...messages, ...nestedMessages }
        enums = { ...enums, ...nestedEnums }
        services = { ...services, ...nestedServices }
        serviceNames = [...serviceNames, ...nestedServiceNames]
      }
    }

    return { messages, enums, services, serviceNames }
  }

  // Extract services, messages, and enums
  const { messages, enums, services, serviceNames } = extractComponents(
    parsedData?.root?.nested || {},
  )

  const handleGenerateCode = (serviceName: string, methodName: string) => {
    // TODO: Implement code generation functionality
    console.log(`Generate code for ${serviceName}.${methodName}`)
  }

  if (!parsedData || !parsedData.root || !parsedData.root.nested) {
    return <Text>No data available or invalid Proto structure</Text>
  }

  return (
    <Stack gap="md">
      {/* <Paper p="md" withBorder>
        <Stack gap="xs">
          <Title order={4}>Package: {rootPackage}</Title>
          <div size="sm">
            Services: <Badge>{serviceNames.length}</Badge> | Messages:{' '}
            <Badge>{Object.keys(messages).length}</Badge> | Enums:{' '}
            <Badge>{Object.keys(enums).length}</Badge>
          </div>
        </Stack>
      </Paper> */}

      <Tabs defaultValue="services" keepMounted={false}>
        <Tabs.List>
          <Tabs.Tab value="services" leftSection={<IconCode size={14} />}>
            Services ({serviceNames.length})
          </Tabs.Tab>
          <Tabs.Tab value="messages" leftSection={<IconMessage size={14} />}>
            Messages ({Object.keys(messages).length})
          </Tabs.Tab>
          <Tabs.Tab value="enums" leftSection={<IconList size={14} />}>
            Enums ({Object.keys(enums).length})
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="services" pt="md">
          {serviceNames.length > 0 ? (
            <Accordion value={expandedService} onChange={setExpandedService}>
              {serviceNames.map(serviceName => {
                const service = services[serviceName]
                const methods = service.methods || {}
                const methodNames = Object.keys(methods)

                return (
                  <Accordion.Item key={serviceName} value={serviceName}>
                    <Accordion.Control>
                      <Group gap="xs">
                        <Text fw={500}>{serviceName}</Text>
                        <Badge size="sm" color="green">
                          {methodNames.length} methods
                        </Badge>
                      </Group>
                    </Accordion.Control>
                    <Accordion.Panel>
                      <Stack gap="md">
                        {methodNames.map(methodName => {
                          const method = methods[methodName]
                          const requestType = method.requestType
                          const responseType = method.responseType
                          const parsedOptions = method.parsedOptions || []

                          return (
                            <Paper key={methodName} p="md" withBorder>
                              <Stack gap="md">
                                <Group justify="space-between">
                                  <Group>
                                    <Title order={5}>{methodName}</Title>
                                    {parsedOptions.length > 0 && (
                                      <Tooltip
                                        label={
                                          <Box>
                                            {parsedOptions.map(
                                              (option: Record<string, any>, index: number) => (
                                                <Box key={index}>
                                                  {Object.entries(option).map(([key, value]) => (
                                                    <Text key={key} size="xs">
                                                      {key}: {JSON.stringify(value)}
                                                    </Text>
                                                  ))}
                                                </Box>
                                              ),
                                            )}
                                          </Box>
                                        }
                                        position="right"
                                        multiline
                                        w={300}
                                      >
                                        <ActionIcon variant="subtle" color="gray">
                                          <IconInfoCircle size={16} />
                                        </ActionIcon>
                                      </Tooltip>
                                    )}
                                  </Group>
                                  <Button
                                    size="xs"
                                    variant="light"
                                    leftSection={<IconCode size={14} />}
                                    onClick={() => handleGenerateCode(serviceName, methodName)}
                                  >
                                    Generate Code
                                  </Button>
                                </Group>

                                <Box>
                                  <Text fw={500} size="sm">
                                    Request:
                                  </Text>
                                  <Paper p="xs" withBorder mt={5}>
                                    <MessageViewer
                                      messageName={requestType}
                                      messages={messages}
                                      enums={enums}
                                      parsedData={parsedData}
                                    />
                                  </Paper>
                                </Box>

                                <Box>
                                  <Text fw={500} size="sm">
                                    Response:
                                  </Text>
                                  <Paper p="xs" withBorder mt={5}>
                                    <MessageViewer
                                      messageName={responseType}
                                      messages={messages}
                                      enums={enums}
                                      parsedData={parsedData}
                                    />
                                  </Paper>
                                </Box>
                              </Stack>
                            </Paper>
                          )
                        })}
                      </Stack>
                    </Accordion.Panel>
                  </Accordion.Item>
                )
              })}
            </Accordion>
          ) : (
            <Paper p="md" withBorder>
              <Text>No services found in this Proto file</Text>
            </Paper>
          )}
        </Tabs.Panel>

        <Tabs.Panel value="messages" pt="md">
          {Object.keys(messages).length > 0 ? (
            <Accordion>
              {Object.keys(messages).map(messageName => (
                <Accordion.Item key={messageName} value={messageName}>
                  <Accordion.Control>
                    <Group gap="xs">
                      <Text fw={500}>{messageName}</Text>
                      {messages[messageName]?.fields && (
                        <Badge size="sm">
                          {Object.keys(messages[messageName].fields).length} fields
                        </Badge>
                      )}
                    </Group>
                  </Accordion.Control>
                  <Accordion.Panel>
                    <MessageViewer
                      messageName={messageName}
                      messages={messages}
                      enums={enums}
                      parsedData={parsedData}
                      showTitle={false}
                    />
                  </Accordion.Panel>
                </Accordion.Item>
              ))}
            </Accordion>
          ) : (
            <Paper p="md" withBorder>
              <Text>No messages found in this Proto file</Text>
            </Paper>
          )}
        </Tabs.Panel>

        <Tabs.Panel value="enums" pt="md">
          {Object.keys(enums).length > 0 ? (
            <Accordion>
              {Object.keys(enums).map(enumName => (
                <Accordion.Item key={enumName} value={enumName}>
                  <Accordion.Control>
                    <Group gap="xs">
                      <Text fw={500}>{enumName}</Text>
                      {enums[enumName]?.values && (
                        <Badge size="sm" color="blue">
                          {Object.keys(enums[enumName].values).length} values
                        </Badge>
                      )}
                    </Group>
                  </Accordion.Control>
                  <Accordion.Panel>
                    <EnumViewer enumName={enumName} enumData={enums[enumName]} showTitle={false} />
                  </Accordion.Panel>
                </Accordion.Item>
              ))}
            </Accordion>
          ) : (
            <Paper p="md" withBorder>
              <Text>No enums found in this Proto file</Text>
            </Paper>
          )}
        </Tabs.Panel>
      </Tabs>
    </Stack>
  )
}

export default ProtoStructureViewer
