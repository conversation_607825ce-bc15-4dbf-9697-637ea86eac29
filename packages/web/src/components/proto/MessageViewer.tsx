import { Badge, Group, Paper, Stack, Table, Text, Title } from '@mantine/core'
import EnumViewer from './EnumViewer'
import FieldViewer from './FieldViewer'

interface MessageViewerProps {
  messageName: string
  messages: Record<string, any>
  enums: Record<string, any>
  parsedData: any
  showTitle?: boolean
  level?: number
}

const MessageViewer = ({
  messageName,
  messages,
  enums,
  parsedData,
  showTitle = true,
  level = 0,
}: MessageViewerProps) => {
  // Find the message in the messages object
  const message = messages[messageName]

  // If message not found directly, try to resolve it from the full path
  if (!message) {
    // Check if it's a fully qualified name (with package)
    const parts = messageName.split('.')
    const simpleName = parts[parts.length - 1]

    // Try to find the message with the simple name
    const matchingMessages = Object.keys(messages).filter(
      key => key === simpleName || key.endsWith(`.${simpleName}`),
    )

    if (matchingMessages.length > 0) {
      // Use the first matching message
      messageName = matchingMessages[0]
    }
  }

  // Get the message fields
  const messageData = messages[messageName]
  const fields = messageData?.fields || {}
  const fieldNames = Object.keys(fields)

  if (!messageData) {
    return (
      <Text size="sm" c="dimmed">
        {messageName} (External or not found)
      </Text>
    )
  }

  // Function to determine if a type is a scalar type
  const isScalarType = (type: string) => {
    const scalarTypes = [
      'double',
      'float',
      'int32',
      'int64',
      'uint32',
      'uint64',
      'sint32',
      'sint64',
      'fixed32',
      'fixed64',
      'sfixed32',
      'sfixed64',
      'bool',
      'string',
      'bytes',
    ]
    return scalarTypes.includes(type)
  }

  // Function to render nested message or enum
  const renderNestedType = (field: any) => {
    const { type } = field

    // Check if it's an enum
    if (enums[type]) {
      return (
        <Paper p="xs" withBorder mt={5} style={{ marginLeft: 20 }}>
          <EnumViewer enumName={type} enumData={enums[type]} />
        </Paper>
      )
    }

    // Check if it's a message
    if (messages[type] && level < 2) {
      // Limit nesting level to prevent infinite recursion
      return (
        <Paper p="xs" withBorder mt={5} style={{ marginLeft: 20 }}>
          <MessageViewer
            messageName={type}
            messages={messages}
            enums={enums}
            parsedData={parsedData}
            level={level + 1}
          />
        </Paper>
      )
    }

    return null
  }

  return (
    <Stack gap="xs">
      {showTitle && <Title order={5}>{messageName}</Title>}

      {fieldNames.length > 0 ? (
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Field</Table.Th>
              <Table.Th>Type</Table.Th>
              <Table.Th>Number</Table.Th>
              <Table.Th>Description</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {fieldNames.map(fieldName => {
              const field = fields[fieldName]
              const isEnum = enums[field.type] !== undefined
              const isMessage = messages[field.type] !== undefined

              return (
                <FieldViewer
                  key={fieldName}
                  fieldName={fieldName}
                  field={field}
                  isScalar={isScalarType(field.type)}
                  isEnum={isEnum}
                  isMessage={isMessage}
                />
              )
            })}
          </Table.Tbody>
        </Table>
      ) : (
        <Text size="sm" c="dimmed">
          No fields defined
        </Text>
      )}

      {/* Render nested types if needed */}
      {level < 1 &&
        fieldNames.map(fieldName => {
          const field = fields[fieldName]
          if (!isScalarType(field.type) && (messages[field.type] || enums[field.type])) {
            return (
              <div key={`nested-${fieldName}`}>
                <Text size="sm" fw={500} mt={10}>
                  Nested type for {fieldName}:
                </Text>
                {renderNestedType(field)}
              </div>
            )
          }
          return null
        })}
    </Stack>
  )
}

export default MessageViewer
