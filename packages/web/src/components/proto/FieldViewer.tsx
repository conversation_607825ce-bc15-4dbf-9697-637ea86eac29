import { Badge, Group, Table, Text } from '@mantine/core'

interface FieldViewerProps {
  fieldName: string
  field: any
  isScalar: boolean
  isEnum: boolean
  isMessage: boolean
}

const FieldViewer = ({ fieldName, field, isScalar, isEnum, isMessage }: FieldViewerProps) => {
  const { type, rule, id, comment } = field

  // Check if it's a repeated field
  const isRepeated = rule === 'repeated'

  // Render the type with appropriate styling
  const renderType = () => {
    if (isScalar) {
      return (
        <Group gap={5}>
          {isRepeated && <Badge size="xs">repeated</Badge>}
          <Text size="sm">{type}</Text>
        </Group>
      )
    } else if (isEnum) {
      return (
        <Group gap={5}>
          {isRepeated && <Badge size="xs">repeated</Badge>}
          <Text size="sm" fw={500} c="blue">
            {type}
          </Text>
        </Group>
      )
    } else if (isMessage) {
      return (
        <Group gap={5}>
          {isRepeated && <Badge size="xs">repeated</Badge>}
          <Text size="sm" fw={500} c="teal">
            {type}
          </Text>
        </Group>
      )
    } else {
      return (
        <Group gap={5}>
          {isRepeated && <Badge size="xs">repeated</Badge>}
          <Text size="sm" c="dimmed">
            {type}
          </Text>
        </Group>
      )
    }
  }

  return (
    <Table.Tr>
      <Table.Td>{fieldName}</Table.Td>
      <Table.Td>{renderType()}</Table.Td>
      <Table.Td>{id}</Table.Td>
      <Table.Td>
        {comment ? (
          <Text size="sm">{comment}</Text>
        ) : (
          <Text size="sm" c="dimmed">
            -
          </Text>
        )}
      </Table.Td>
    </Table.Tr>
  )
}

export default FieldViewer
