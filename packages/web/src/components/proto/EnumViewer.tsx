import { Stack, Table, Text, Title, Badge } from '@mantine/core'

interface EnumViewerProps {
  enumName: string
  enumData: any
  showTitle?: boolean
}

const EnumViewer = ({ enumName, enumData, showTitle = true }: EnumViewerProps) => {
  if (!enumData || !enumData.values) {
    return <Text>Enum data not available</Text>
  }

  const values = enumData.values
  const valueNames = Object.keys(values)

  return (
    <Stack gap="xs">
      {showTitle && (
        <Title order={5}>
          <Badge size="sm" color="blue" mr={5}>
            enum
          </Badge>
          {enumName}
        </Title>
      )}

      {valueNames.length > 0 ? (
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Name</Table.Th>
              <Table.Th>Value</Table.Th>
              <Table.Th>Description</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {valueNames.map(valueName => (
              <Table.Tr key={valueName}>
                <Table.Td>{valueName}</Table.Td>
                <Table.Td>{values[valueName]}</Table.Td>
                <Table.Td>
                  {enumData.comments && enumData.comments[valueName] ? (
                    <Text size="sm">{enumData.comments[valueName]}</Text>
                  ) : (
                    <Text size="sm" c="dimmed">
                      -
                    </Text>
                  )}
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      ) : (
        <Text size="sm" c="dimmed">
          No values defined
        </Text>
      )}
    </Stack>
  )
}

export default EnumViewer
