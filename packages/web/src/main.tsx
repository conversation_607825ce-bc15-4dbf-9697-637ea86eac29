import { StrictMode } from 'react'
import ReactDOM from 'react-dom/client'
import { RouterProvider, createRouter } from '@tanstack/react-router'
import { createTheme, MantineProvider } from '@mantine/core'
import { Notifications } from '@mantine/notifications'
// Import the generated route tree
import { routeTree } from './routeTree.gen'

import '@mantine/core/styles.css'
import '@mantine/code-highlight/styles.css'
import '@mantine/notifications/styles.css'
import '@mantine/spotlight/styles.css'

import './styles.css'
import './styles/diff.css'
import { QueryClientProvider } from '@tanstack/react-query'
import { queryClient } from './utils/trpc'
import { ModalsProvider } from '@mantine/modals'
import { ModalProvider } from './utils/modal'
import { initMonaco } from './utils/monaco-editor'
// import reportWebVitals from "./reportWebVitals.ts";

// 确保Monaco编辑器正确初始化
initMonaco().catch(error => console.error('Monaco初始化失败:', error))

// Create a new router instance
const router = createRouter({
  routeTree,
  context: {},
  defaultPreload: 'intent',
  scrollRestoration: true,
  defaultStructuralSharing: true,
  defaultPreloadStaleTime: 0,
})

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

const theme = createTheme({
  components: {
    Tooltip: {
      defaultProps: {
        withArrow: true,
      },
    },
    Input: { defaultProps: { size: 'xs' } },
    Select: { defaultProps: { size: 'xs' } },
    Button: { defaultProps: { size: 'xs' } },
    Textarea: { defaultProps: { size: 'xs' } },
    TextInput: { defaultProps: { size: 'xs' } },
    Checkbox: { defaultProps: { size: 'xs' } },
    Text: { defaultProps: { size: 'sm' } },
    Group: { defaultProps: { gap: 'xs' } },
    Stack: { defaultProps: { gap: 'xs' } },
    Drawer: { defaultProps: { size: 'xs' } },
    Tabs: { defaultProps: { size: 'xs' } },
  },
  // fontFamily: "Inter, sans-serif",
  // defaultRadius: "md",
})

// Render the app
const rootElement = document.getElementById('app')
if (rootElement && !rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(
    <StrictMode>
      <QueryClientProvider client={queryClient}>
        <MantineProvider theme={theme}>
          <ModalsProvider>
            <ModalProvider>
              <RouterProvider router={router} />
            </ModalProvider>
          </ModalsProvider>
          <Notifications />
        </MantineProvider>
      </QueryClientProvider>
    </StrictMode>,
  )
}

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
// reportWebVitals()
