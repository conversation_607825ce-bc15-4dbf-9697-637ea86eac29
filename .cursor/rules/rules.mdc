---
description:
globs:
alwaysApply: true
---

# Cursor Rules for Proto-based API Management System (Project: ProtoView / ProtoLens / ProtoSpec - choose one)

## Project Goal

This project aims to build a full-stack API management system within a **pnpm monorepo**.
The core functionality revolves around parsing `.proto` files to automatically generate documentation and visual representations of gRPC/Protobuf-based APIs.
The system should display services, methods (RPCs), request message structures, and response message structures defined within the proto files.

## Core Logic Flow

1.  **Input:** User provides `.proto` file content or points to its location.
2.  **Backend Processing (Node.js/tRPC):**
    - Uses the `protobuf.js` library to parse the `.proto` source file directly.
    - Extracts information: package, services, RPCs, messages, enums, comments.
    - Handles potential `import` statements.
    - Structures the extracted definitions.
    - Returns the structured data via a tRPC procedure.
3.  **Frontend Display (React):**
    - Uses the tRPC client hook (integrated with TanStack Query) to fetch data.
    - Renders the information using Mantine UI and Tailwind CSS.

## Technology Stack

### Backend (`packages/server`)

- **Runtime:** Node.js (LTS version)
- **Framework/API Layer:** tRPC (`@trpc/server`, `@trpc/client`) using Express (`express`) adapter (`@trpc/server/adapters/express`).
- **Database ORM:** Prisma (`@prisma/client`, `prisma`)
- **Database:** MySQL
- **Proto Parser:** protobuf.js (`protobufjs`) - **Use its capability to load and parse `.proto` source files.**
- **Language:** TypeScript (strict mode)

### Frontend (`packages/web`)

- **Framework:** React (`react`, `react-dom`)
- **Language:** TypeScript (TSX syntax)
- **Routing:** TanStack Router (`@tanstack/react-router`)
- **Data Fetching & State:**
  - TanStack Query (`@tanstack/react-query`)
  - tRPC Client (`@trpc/client`, `@trpc/react-query`)
  - Zustand (`zustand`)
- **UI Components:** Mantine UI (`@mantine/core`, `@mantine/hooks`, etc.)
- **Styling:** Tailwind CSS (`tailwindcss`)
- **Icons:** Lucide Icons (`lucide-react`)
- **Charting (Optional):** Recharts (`recharts`)
- **Date/Time:** DayJS (`dayjs`)

### General & Monorepo Setup

- **Package Manager:** **pnpm (Mandatory)**. All package management operations _must_ use `pnpm`.
- **Monorepo Structure:** pnpm workspaces. Expect standard directories like `packages/` (for libraries, shared code) and `apps/` (for deployable applications like `server`, `client`). A `packages/shared` directory might exist for shared types/utils, although tRPC minimizes explicit API type sharing.
- **Dependency Installation:**
  - **All new dependencies MUST be added from the monorepo root directory.** Do NOT `cd` into sub-project directories to add dependencies.
  - **Install dependencies to the workspace root** using the `-w` flag (short for `--workspace-root`). This makes the dependency available to all packages within the monorepo via hoisting.
    - For runtime dependencies: `pnpm add -w <package-name>`
    - For development dependencies: `pnpm add -wD <package-name>`
  - Running `pnpm install` (without arguments) in the root directory will install dependencies for all workspace packages based on their respective `package.json` files, as well as the root dependencies.
- **Linting/Formatting:** ESLint and Prettier configured for TypeScript/React across the monorepo.

## Coding Conventions & Style

- **Type Safety:** Prioritize end-to-end type safety (TypeScript, tRPC, Prisma).
- **React:** Functional components, Hooks.
- **tRPC:** Backend routers (`*.router.ts`), input validation (e.g., Zod).
- **Prisma:** `schema.prisma`, Prisma Client, `prisma migrate`.
- **protobuf.js:** Encapsulate parsing logic in dedicated backend services/utils using `protobufjs.load()`.
- **State Management:** TanStack Query (server state), Zustand (global client state), local state preferred.
- **Styling:** Tailwind utilities first, Mantine components for structure.
- **File Naming:** Consistent naming conventions (e.g., `[Feature].router.ts`, `[ComponentName].tsx`, `use[HookName].ts`).
- **Error Handling:** Proper error handling in tRPC, user-friendly frontend errors.

## Instructions for Cursor

- **Package Management:** **Crucially, when suggesting package installations, always provide the `pnpm` command to be run from the project root directory using the `-w` flag.**
  - Example for adding React: `pnpm add -w react react-dom`
  - Example for adding a dev dependency like TypeScript: `pnpm add -wD typescript @types/node @types/react @types/react-dom`
  - **Never suggest `npm install` or `yarn add`. Do not suggest `cd packages/client && pnpm add ...`.**
- Assume a monorepo structure (`packages/server`, `packages/web`).
- When generating backend code, use tRPC router structure, Prisma Client, and `protobuf.js` for parsing `.proto` _source files_.
- When generating frontend code, use Mantine UI components, Tailwind CSS utilities, TanStack Query hooks integrated with the tRPC client (`useQuery(trpc.xxx.xxx.queryOptions({ ... }))`,`useMutation(trpc.xxx.xxx.mutationOptions())`).
- Adhere to strict TypeScript and React functional component best practices.
- Focus on the core logic: parsing protos and displaying the extracted structure.
