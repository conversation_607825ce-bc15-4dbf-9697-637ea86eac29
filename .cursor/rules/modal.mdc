---
description: use this rule when define a complex modal in react. but if you just need a simple modal, like a simple confirm, just use modals.openConfirmModal by @mantine/modals
globs: 
---

# React Modal 最佳实践

## useModal 模式

在本项目中，我们采用 `useModal` 和 `MC`（ModalComponent）模式来实现所有的弹框组件，而不是使用传统的弹框组件定义和使用方式。这种模式提供了更好的类型安全、状态管理和代码组织结构。

### 核心概念

1. **MC (ModalComponent)** - 一个特殊类型的组件，专门用于定义弹框内容和行为
2. **useModal** - 一个自定义 Hook，用于创建和管理弹框实例
3. **ModalProvider** - 一个上下文提供者，负责管理所有弹框实例

### 使用方式

#### 1. 定义弹框组件 (MC)

```tsx
import { type MC } from '@/utils/modal'
import { Modal } from '@mantine/core'

// 定义自定义选项类型
interface MyModalOptions {
  initialData?: string
}

// 定义返回数据类型
interface ResultData {
  value: string
}

// 创建弹框组件
const MyModal: MC<MyModalOptions, ResultData> = ({ bind, cancel, finish, initialData }) => {
  // props 中包含：
  // - finish: 完成并关闭弹框的方法，可以返回数据
  // - cancel: 取消并关闭弹框的方法
  // - bind: 用于绑定到 Modal 组件的属性(visible, onClose)
  // - 以及自定义的选项 (MyModalOptions)

  return (
    <Modal opened={bind.visible} onClose={bind.onClose} title="我的弹框">
      {/* 弹框内容 */}
      <button onClick={() => finish({ value: 'success' }, '操作成功')}>完成</button>
      <button onClick={cancel}>取消</button>
    </Modal>
  )
}

export default MyModal
```

#### 2. 使用弹框

```tsx
import { useModal } from '@/utils/modal'
import MyModal from './MyModal'

function MyComponent() {
  // 创建弹框实例
  const myModal = useModal(MyModal)

  // 打开弹框
  const handleOpenModal = () => {
    myModal.openModal({
      initialData: 'some data',
      onFinish: result => {
        console.log('Modal returned:', result.value)
      },
    })
  }

  return (
    <div>
      <button onClick={handleOpenModal}>打开弹框</button>
    </div>
  )
}
```

### 优势

1. **类型安全** - 通过泛型提供完整的类型检查和自动补全
2. **状态管理** - 弹框状态由 useModal 自动管理
3. **代码组织** - 弹框逻辑与使用它的组件分离
4. **可重用性** - 弹框可以在多个地方重用
5. **一致性** - 所有弹框遵循相同的模式
6. **生命周期管理** - 组件卸载时自动清理弹框

### 最佳实践

1. **始终使用 MC 和 useModal** - 对于所有弹框，无论简单还是复杂
2. **将弹框组件放在单独的文件中** - 通常放在 `components/[feature]` 目录下
3. **使用明确的类型定义** - 为选项和返回数据定义清晰的接口
4. **利用 finish 方法返回数据** - 而不是使用外部状态
5. **在根组件中确保 ModalProvider 存在** - 通常在 main.tsx 中

### 示例

以下是一个完整的示例，展示了如何创建和使用一个项目创建弹框：

```tsx
// CreateProjectModal.tsx
import { Button, Group, Stack, TextInput, Textarea } from '@mantine/core'
import { notifications } from '@mantine/notifications'
import { useMutation } from '@tanstack/react-query'
import { useForm, zodResolver } from '@mantine/form'
import { z } from 'zod'
import { trpc } from '@/utils/trpc'
import { Modal } from '@mantine/core'
import { type MC } from '@/utils/modal'

// 定义表单 schema
const formSchema = z.object({
  title: z.string().min(1, '项目名称不能为空'),
  desc: z.string().optional(),
})

// 定义自定义选项类型
interface CreateProjectOptions {
  defaultValues?: {
    title?: string
    desc?: string
  }
}

// 定义返回数据类型
interface ProjectData {
  id: number
  title: string
  desc?: string
}

// 创建项目的模态框组件
export const CreateProjectModal: MC<CreateProjectOptions, ProjectData> = ({ defaultValues, bind, finish ,cancel }) => {
  // 创建项目的 mutation
  const createProject = useMutation(trpc.project.create.mutationOptions())

  // 初始化表单
  const form = useForm({
    initialValues: {
      title: defaultValues?.title || '',
      desc: defaultValues?.desc || '',
    },
    validate: zodResolver(formSchema),
  })

  // 处理创建项目
  const handleCreateProject = async (values: typeof form.values) => {
    try {
      const newProject = await createProject.mutateAsync({
        title: values.title,
        desc: values.desc,
      })

      // 调用 finish 方法关闭弹窗并返回数据
      finish(
        {
          id: newProject.id,
          title: newProject.title,
          desc: newProject.desc,
        },
        '项目已成功创建',
      )
    } catch (error) {
      console.error('创建项目失败:', error)
      notifications.show({
        title: '创建失败',
        message: '无法创建项目',
        color: 'red',
      })
    }
  }

  return (
    <Modal opened={bind.visible} onClose={bind.onClose} title="创建新项目" size="md">
      {/* 弹框内容 */}
    </Modal>
  )
}
```

通过遵循这种模式，我们可以确保所有弹框在整个应用程序中保持一致的行为和外观。
