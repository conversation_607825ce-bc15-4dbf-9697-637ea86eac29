# API Token 功能实现说明

## 概述

实现了用户API Token功能，允许用户在个人中心生成token，通过token直接调用后端接口，绕过正式环境域名的智能网关登录。

## 功能特点

- 每个用户只能生成一个token
- 支持token认证，路径为 `/api/trpc`
- 与原有的太湖认证系统兼容
- 在个人设置页面管理token

## 实现步骤

### 1. 数据库迁移

首先需要运行数据库迁移来添加新字段：

```bash
cd packages/server
npx prisma db push
```

这会在User表中添加以下字段：

- `apiToken`: 用户的API token (唯一)
- `apiTokenCreatedAt`: token创建时间

### 2. 后端实现

#### 数据库Schema更新

- 在 `packages/server/prisma/schema.prisma` 中添加了API token相关字段

#### 认证中间件更新

- 修改了 `packages/server/src/trpc.ts` 中的 `createContext` 函数
- 支持Bearer token认证，优先级高于太湖认证
- 添加了 `/api/trpc` 路由支持

#### API接口

在 `packages/server/src/routes/user.ts` 中添加了以下接口：

- `generateApiToken`: 生成新的API token
- `getApiToken`: 获取当前用户的token信息
- `deleteApiToken`: 删除用户的API token

### 3. 前端实现

#### 个人设置页面

在 `packages/web/src/routes/settings.tsx` 中添加了API Token管理功能：

- 显示token状态（已生成/未生成）
- 生成token按钮
- 删除token按钮
- 复制token功能
- 使用说明

## 使用方法

### 1. 生成Token

1. 登录系统后，进入个人设置页面
2. 在"API Token"区域点击"生成Token"按钮
3. 生成后点击"查看"按钮显示token
4. 点击复制按钮复制token并妥善保存

### 2. 查看已生成的Token

1. 在个人设置页面的"API Token"区域
2. 如果已有token，会显示"已生成"状态和创建时间
3. 点击"查看"按钮可以显示/隐藏token内容
4. 在显示状态下可以点击复制按钮复制token

### 3. 使用Token调用API

使用生成的token调用API：

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"0":{"json":{}}}' \
     http://your-domain/api/trpc/user.getCurrentUser
```

或者在代码中使用：

```javascript
const response = await fetch('/api/trpc/user.getCurrentUser', {
  method: 'POST',
  headers: {
    Authorization: 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({ 0: { json: {} } }),
})
```

### 4. 路径说明

- 原有路径：`/trpc` (用于网关认证)
- 新增路径：`/api/trpc` (用于token认证)

## 安全注意事项

1. Token具有完整的用户权限，请妥善保管
2. 不要在客户端代码中硬编码token
3. 定期更换token以提高安全性
4. 如果token泄露，请立即删除并重新生成

## 技术细节

### 认证流程

1. 检查请求头中的 `Authorization: Bearer TOKEN`
2. 如果存在token，查询数据库验证token有效性
3. 如果token有效，创建对应的用户上下文
4. 如果token认证失败，回退到原有的太湖认证

### 兼容性

- 完全兼容现有的太湖认证系统
- 不影响现有的网关登录流程
- 支持两种认证方式并存

## 故障排除

### 常见问题

1. **数据库错误**: 确保已运行 `prisma db push`
2. **Token无效**: 检查token是否正确复制，是否包含完整字符
3. **权限错误**: 确认用户有相应的操作权限

### 调试

可以通过以下方式调试token认证：

1. 检查数据库中的 `apiToken` 字段
2. 查看服务器日志中的认证信息
3. 使用浏览器开发者工具检查请求头

## 后续优化

1. 添加token过期时间
2. 支持多个token（不同用途）
3. 添加token使用日志
4. 实现token权限范围限制
