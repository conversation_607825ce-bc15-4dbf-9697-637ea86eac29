# 🚀 部署工具

简洁高效的 Web/Server 项目部署工具，支持自动配置选择和智能部署流程。

## 快速开始

### 1. 配置部署环境

复制配置模板并修改：

```bash
cp scripts/deploy/config.example.ts scripts/deploy/config.ts
```

编辑 `config.ts`，配置你的服务器信息：

```typescript
export const deployConfig = {
  web: {
    production: {
      environment: 'production',
      type: 'web',
      ssh: {
        host: 'your-server.com',
        port: 22,
        username: 'root',
        privateKeyPath: '~/.ssh/id_rsa', // 或使用 password: 'your-password'
      },
      paths: {
        local: './packages/web/dist',
        remote: '/var/www/your-app/',
      },
      // ... 其他配置
    },
  },
  server: {
    production: {
      environment: 'production',
      type: 'server',
      ssh: {
        /* 同上 */
      },
      paths: {
        local: './packages/server',
        remote: '/opt/your-app/',
      },
      // ... 其他配置
    },
  },
}
```

### 2. 部署项目

```bash
# 部署 Web 项目
pnpm deploy:web

# 部署 Server 项目
pnpm deploy:server
```

## 特性

✅ **智能配置选择** - 单配置直接部署，多配置交互选择  
✅ **统一工具链** - Web/Server 共享核心逻辑，减少冗余  
✅ **自动化流程** - 构建 → 上传 → 配置 → 重启一键完成  
✅ **安全认证** - 支持 SSH 密钥和密码两种认证方式  
✅ **环境管理** - 支持多环境配置和动态选择

## 部署流程

### Web 项目

1. 🔨 构建项目 (Vite)
2. 📤 上传文件 (rsync)

### Server 项目

1. 🔨 构建项目 (tsup)
2. 📤 上传文件 (SSH)
3. 📦 安装依赖 (pnpm)
4. 🗄️ 数据库操作 (Prisma)
5. ⚙️ 环境配置 (.env)
6. 🔄 重启服务 (PM2)

## 配置说明

### SSH 认证配置

**推荐：SSH 密钥认证**

```typescript
ssh: {
  host: 'your-server.com',
  port: 22,
  username: 'root',
  privateKeyPath: '~/.ssh/id_rsa', // 自动展开 ~ 路径
}
```

**备选：密码认证**

```typescript
ssh: {
  host: 'your-server.com',
  port: 22,
  username: 'root',
  password: 'your-password',
}
```

### 多环境配置

```typescript
export const deployConfig = {
  web: {
    staging: {
      /* staging 配置 */
    },
    production: {
      /* production 配置 */
    },
  },
  server: {
    staging: {
      /* staging 配置 */
    },
    production: {
      /* production 配置 */
    },
  },
}
```

运行时会自动检测配置数量：

- 1个配置：直接部署
- 多个配置：交互选择

## 环境要求

### 本地环境

- Node.js >= 16
- pnpm >= 8
- SSH 客户端

### 服务器环境 (Server 部署)

- Node.js (推荐通过 nvm 安装)
- pnpm
- PM2
- MySQL (如使用数据库)

## 故障排除

### SSH 连接失败

1. 检查服务器地址和端口
2. 确认 SSH 密钥权限：`chmod 600 ~/.ssh/id_rsa`
3. 测试手动连接：`ssh -i ~/.ssh/id_rsa user@host -p port`

### 构建失败

1. 检查本地构建：`pnpm build`
2. 确认构建输出目录存在

### Server 部署失败

1. 确认服务器已安装 Node.js/pnpm/PM2
2. 检查数据库连接配置
3. 验证 .env 文件配置

## 高级用法

### 调试模式

```bash
DEBUG=1 pnpm deploy:web
DEBUG=1 pnpm deploy:server
```

### 查看帮助

```bash
pnpm deploy:web --help
pnpm deploy:server --help
```

---

💡 **提示**: 首次部署建议在测试环境验证配置，确保所有依赖和权限配置正确。
