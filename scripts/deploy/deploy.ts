#!/usr/bin/env tsx

import { existsSync } from 'fs'
import { join } from 'path'
import { createInterface } from 'readline'
import { Logger } from './utils/logger'
import { Builder } from './utils/builder'
import { Uploader } from './utils/uploader'
import { getConfigsByType } from './utils/config-helper'
import type { DeployConfig } from './config'

class UnifiedDeployer {
  private projectType: 'web' | 'server'
  private config: DeployConfig
  private rl: any

  constructor(projectType: 'web' | 'server') {
    this.projectType = projectType
    this.rl = createInterface({
      input: process.stdin,
      output: process.stdout,
    })
  }

  private async selectConfig(): Promise<DeployConfig> {
    const configPath = join(process.cwd(), 'scripts/deploy/config.ts')

    if (!existsSync(configPath)) {
      Logger.error('配置文件不存在！请创建 scripts/deploy/config.ts 文件')
      Logger.info('可以参考 scripts/deploy/config.example.ts 文件')
      process.exit(1)
    }

    try {
      // 获取指定类型的所有配置
      const configs = getConfigsByType(this.projectType)
      const configKeys = Object.keys(configs)

      if (configKeys.length === 0) {
        Logger.error(`未找到 ${this.projectType} 项目的部署配置`)
        Logger.info(`请在 config.ts 中添加 ${this.projectType} 配置`)
        process.exit(1)
      }

      // 如果只有一个配置，直接使用
      if (configKeys.length === 1) {
        const selectedKey = configKeys[0]
        Logger.info(`自动选择 ${this.projectType} 环境: ${selectedKey}`)
        return configs[selectedKey]
      }

      // 多个配置时，让用户选择
      Logger.info(`发现 ${configKeys.length} 个 ${this.projectType} 环境配置:`)
      configKeys.forEach((key, index) => {
        Logger.info(`${index + 1}. ${key}`)
      })

      const selectedIndex = await this.askUserChoice('请选择要部署的环境', configKeys.length)
      const selectedKey = configKeys[selectedIndex - 1]
      Logger.info(`已选择环境: ${selectedKey}`)

      return configs[selectedKey]
    } catch (error) {
      Logger.error('配置文件加载失败')
      if (error instanceof Error) {
        Logger.error(error.message)
      }
      process.exit(1)
    }
  }

  private async askUser(question: string): Promise<boolean> {
    return new Promise(resolve => {
      this.rl.question(`${question} (y/N): `, (answer: string) => {
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes')
      })
    })
  }

  private async askUserInput(question: string): Promise<string> {
    return new Promise(resolve => {
      this.rl.question(`${question}: `, (answer: string) => {
        resolve(answer.trim())
      })
    })
  }

  private async askUserChoice(question: string, maxChoice: number): Promise<number> {
    return new Promise(resolve => {
      const ask = () => {
        this.rl.question(`${question} (1-${maxChoice}): `, (answer: string) => {
          const choice = parseInt(answer.trim())
          if (choice >= 1 && choice <= maxChoice) {
            resolve(choice)
          } else {
            Logger.warning(`请输入 1-${maxChoice} 之间的数字`)
            ask()
          }
        })
      }
      ask()
    })
  }

  async deploy(): Promise<void> {
    Logger.title(`🚀 ${this.projectType.toUpperCase()} 项目部署工具`)

    try {
      // 选择配置
      this.config = await this.selectConfig()

      Logger.info(
        `目标服务器: ${this.config.ssh.username}@${this.config.ssh.host}:${this.config.ssh.port}`,
      )
      Logger.info(`远程路径: ${this.config.paths.remote}`)
      Logger.separator()

      if (this.projectType === 'web') {
        await this.deployWeb()
      } else {
        await this.deployServer()
      }

      Logger.separator()
      Logger.success(`🎉 ${this.projectType.toUpperCase()} 部署完成！`)
    } catch (error) {
      Logger.separator()
      Logger.error('💥 部署失败！')

      if (error instanceof Error) {
        Logger.error(error.message)
      }

      process.exit(1)
    } finally {
      this.rl.close()
    }
  }

  private async deployWeb(): Promise<void> {
    // Web 部署流程：构建 + 上传
    Logger.step('🔨 构建 Web 项目')
    const builder = new Builder(this.config)
    await builder.build()

    Logger.separator()

    Logger.step('📤 上传文件到服务器')
    const uploader = new Uploader(this.config)
    await uploader.deploy()
  }

  private async deployServer(): Promise<void> {
    // Server 部署流程：构建 + 上传 + 服务器操作
    Logger.step('🔨 构建 Server 项目')
    const builder = new Builder(this.config)
    await builder.build()

    Logger.separator()

    Logger.step('📤 上传文件到服务器')
    const uploader = new Uploader(this.config)
    await uploader.deploy()

    Logger.separator()

    // Server 特有的操作
    await this.performServerOperations(uploader)

    await uploader.disconnect()
  }

  private async performServerOperations(uploader: Uploader): Promise<void> {
    // 依赖安装
    Logger.step('📦 安装服务器依赖')
    const installDeps = await this.askUser(
      '需要重新安装依赖吗？(推荐在首次部署或package.json有变化时执行)',
    )
    if (installDeps) {
      await uploader.runRemoteCommand('cd ' + this.config.paths.remote + ' && pnpm install')
    }

    // 数据库操作
    Logger.step('🗄️ 数据库操作')
    const hasSchemaChanges = await this.askUser('是否有数据库结构变更需要推送？')

    if (hasSchemaChanges) {
      Logger.warning('⚠️ 注意：数据库结构变更可能会影响现有数据！')
      const confirmPush = await this.askUser('确认执行 prisma db push 吗？')

      if (confirmPush) {
        await uploader.runRemoteCommand('cd ' + this.config.paths.remote + ' && pnpm prisma:push')
        Logger.success('数据库推送完成 (包含自动 generate)')
      } else {
        const needGenerate = await this.askUser('需要重新生成 Prisma Client 吗？')
        if (needGenerate) {
          await uploader.runRemoteCommand(
            'cd ' + this.config.paths.remote + ' && pnpm prisma:generate',
          )
        }
      }
    } else {
      const needGenerate = await this.askUser('需要重新生成 Prisma Client 吗？')
      if (needGenerate) {
        await uploader.runRemoteCommand(
          'cd ' + this.config.paths.remote + ' && pnpm prisma:generate',
        )
      }
    }

    // 环境配置
    Logger.step('⚙️ 环境配置')
    const checkEnv = await this.askUser('需要检查/修改服务器上的 .env 配置吗？')
    if (checkEnv) {
      const envInput = await this.askUserInput(
        '如需要添加环境变量，请输入 KEY=VALUE 格式 (多个用分号分隔，留空跳过)',
      )
      if (envInput.trim()) {
        const envPairs = envInput
          .split(';')
          .map(pair => pair.trim())
          .filter(Boolean)
        for (const pair of envPairs) {
          if (pair.includes('=')) {
            const [key, value] = pair.split('=', 2)
            await uploader.runRemoteCommand(
              `cd ${this.config.paths.remote} && echo "${key}=${value}" >> .env`,
            )
            Logger.info(`已添加环境变量: ${key}=${value}`)
          }
        }
      }
    }

    // PM2 服务管理
    Logger.step('🔄 重启 PM2 服务')
    const pm2Status = await uploader.runRemoteCommand(
      'cd ' +
        this.config.paths.remote +
        ' && pm2 list | grep proto-api-server || echo "not-running"',
    )

    if (pm2Status.includes('proto-api-server')) {
      Logger.info('发现运行中的 PM2 服务，将重启服务')
      await uploader.runRemoteCommand('cd ' + this.config.paths.remote + ' && pnpm pm2:restart')
      Logger.success('PM2 服务重启完成')
    } else {
      Logger.info('未发现运行中的 PM2 服务，将启动新服务')
      await uploader.runRemoteCommand('cd ' + this.config.paths.remote + ' && pnpm pm2')
      Logger.success('PM2 服务启动完成')
    }

    // 部署验证
    Logger.step('✅ 部署验证')
    await uploader.runRemoteCommand('cd ' + this.config.paths.remote + ' && pm2 list')

    const showLogs = await this.askUser('查看服务日志吗？')
    if (showLogs) {
      Logger.info('最近的服务日志：')
      await uploader.runRemoteCommand(
        'cd ' + this.config.paths.remote + ' && pm2 logs proto-api-server --lines 20 --nostream',
      )
    }
  }
}

// 显示使用说明
function showHelp(): void {
  console.log(`
🚀 统一部署工具

用法:
  pnpm deploy:web                 # 部署 Web 项目
  pnpm deploy:server              # 部署 Server 项目

特性:
  ✅ 自动识别和选择配置
  ✅ 单配置直接部署，多配置交互选择
  ✅ Web/Server 不同的部署流程
  ✅ 智能的服务器操作管理

环境变量:
  DEBUG=1                         # 启用调试模式

注意事项:
1. 请确保已配置好 SSH 认证
2. Server 部署需要服务器已安装 pnpm 和 pm2
3. 首次使用请复制 config.example.ts 为 config.ts 并修改配置
  `)
}

// 主函数
async function main(): Promise<void> {
  // 检查帮助参数
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp()
    return
  }

  // 从命令行参数或环境变量获取项目类型
  const projectType = process.argv[2] as 'web' | 'server'

  if (!projectType || !['web', 'server'].includes(projectType)) {
    Logger.error('请指定项目类型: web 或 server')
    showHelp()
    process.exit(1)
  }

  // 创建部署器并执行部署
  const deployer = new UnifiedDeployer(projectType)
  await deployer.deploy()
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  Logger.error('未处理的 Promise 拒绝')
  Logger.error(String(reason))
  process.exit(1)
})

process.on('uncaughtException', error => {
  Logger.error('未捕获的异常')
  Logger.error(error.message)
  process.exit(1)
})

// 启动应用
main().catch(error => {
  Logger.error('部署失败')
  Logger.error(error.message)
  process.exit(1)
})
