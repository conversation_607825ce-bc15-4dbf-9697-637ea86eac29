import { execSync } from 'child_process'
import { existsSync, rmSync } from 'fs'
import { join } from 'path'
import { Logger } from './logger'
import type { DeployConfig } from '../config'

export class Builder {
  private config: DeployConfig

  constructor(config: DeployConfig) {
    this.config = config
  }

  async build(): Promise<void> {
    const projectType = this.config.type.toUpperCase()
    Logger.step(`🔨 开始构建 ${projectType} 项目...`)

    try {
      // 清理构建目录
      if (this.config.build.clean) {
        await this.cleanBuildDir()
      }

      // 执行构建命令
      Logger.info(`执行构建命令: ${this.config.build.command}`)

      const startTime = Date.now()
      const cwd =
        this.config.type === 'server' ? join(process.cwd(), 'packages/server') : process.cwd()

      execSync(this.config.build.command, {
        stdio: 'inherit',
        encoding: 'utf-8',
        cwd,
      })

      const duration = Date.now() - startTime
      Logger.success(`${projectType} 构建完成，耗时 ${duration}ms`)

      // 验证构建输出
      await this.validateBuildOutput()
    } catch (error) {
      Logger.error(`${projectType} 构建失败`)
      if (error instanceof Error) {
        Logger.error(error.message)
      }
      process.exit(1)
    }
  }

  private async cleanBuildDir(): Promise<void> {
    const buildPath =
      this.config.type === 'server'
        ? join(process.cwd(), 'packages/server/dist')
        : join(process.cwd(), this.config.paths.local)

    if (existsSync(buildPath)) {
      Logger.info(`清理构建目录: ${buildPath}`)
      rmSync(buildPath, { recursive: true, force: true })
      Logger.success('构建目录清理完成')
    }
  }

  private async validateBuildOutput(): Promise<void> {
    const buildPath =
      this.config.type === 'server'
        ? join(process.cwd(), 'packages/server/dist')
        : join(process.cwd(), this.config.paths.local)

    if (!existsSync(buildPath)) {
      throw new Error(`构建输出目录不存在: ${buildPath}`)
    }

    // Server 项目需要额外检查关键文件
    if (this.config.type === 'server') {
      const indexFile = join(buildPath, 'index.js')
      if (!existsSync(indexFile)) {
        throw new Error(`构建输出文件不存在: ${indexFile}`)
      }
    }

    const projectType = this.config.type.toUpperCase()
    Logger.success(`${projectType} 构建输出验证通过: ${buildPath}`)
  }
}
