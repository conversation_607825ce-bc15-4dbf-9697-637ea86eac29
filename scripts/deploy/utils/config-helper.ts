import { deployConfig, type DeployConfig } from '../config'

/**
 * 获取指定类型的所有配置
 * @param type 项目类型 ('web' | 'server')
 * @returns 该类型下的所有环境配置
 */
export function getConfigsByType(type: 'web' | 'server'): Record<string, DeployConfig> {
  return deployConfig[type] || {}
}

/**
 * 获取指定类型和环境的配置
 * @param type 项目类型 ('web' | 'server')
 * @param environment 环境名称
 * @returns 对应的配置对象，如果不存在则返回 null
 */
export function getConfig(type: 'web' | 'server', environment: string): DeployConfig | null {
  return deployConfig[type]?.[environment] || null
}

/**
 * 获取所有可用的环境名称
 * @param type 项目类型 ('web' | 'server')
 * @returns 环境名称数组
 */
export function getEnvironmentNames(type: 'web' | 'server'): string[] {
  return Object.keys(deployConfig[type] || {})
}

/**
 * 检查指定配置是否存在
 * @param type 项目类型 ('web' | 'server')
 * @param environment 环境名称
 * @returns 配置是否存在
 */
export function hasConfig(type: 'web' | 'server', environment: string): boolean {
  return !!deployConfig[type]?.[environment]
}
