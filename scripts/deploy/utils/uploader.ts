import { NodeSSH } from 'node-ssh'
import { execSync } from 'child_process'
import { existsSync } from 'fs'
import { join, resolve } from 'path'
import { homedir } from 'os'
import { Logger } from './logger'
import type { DeployConfig } from '../config'

export class Uploader {
  private config: DeployConfig
  private ssh: NodeSSH

  constructor(config: DeployConfig) {
    this.config = config
    this.ssh = new NodeSSH()
  }

  async deploy(): Promise<void> {
    Logger.step('开始部署文件...')

    try {
      // 连接SSH
      await this.connectSSH()

      if (this.config.type === 'web') {
        await this.deployWeb()
      } else {
        await this.deployServer()
      }

      Logger.success('文件部署完成！')
    } catch (error) {
      Logger.error('部署失败')
      if (error instanceof Error) {
        Logger.error(error.message)
      }
      throw error
    }
  }

  private async deployWeb(): Promise<void> {
    // Web 部署：验证本地文件 + rsync 同步
    await this.validateLocalFiles()
    await this.syncFiles()

    // Web 部署完成后自动断开连接
    this.ssh.dispose()
  }

  private async deployServer(): Promise<void> {
    // Server 部署：确保远程目录 + 上传文件
    await this.ensureRemoteDirectory()
    await this.uploadFiles()

    // Server 部署后保持连接，供后续操作使用
  }

  private async connectSSH(): Promise<void> {
    const authMethod = this.config.ssh.privateKeyPath ? '密钥' : '密码'
    Logger.info(
      `连接服务器 ${this.config.ssh.username}@${this.config.ssh.host}:${this.config.ssh.port} (${authMethod}认证)`,
    )

    try {
      // 构建连接配置
      const connectConfig: any = {
        host: this.config.ssh.host,
        port: this.config.ssh.port,
        username: this.config.ssh.username,
      }

      // 根据配置选择认证方式
      if (this.config.ssh.privateKeyPath) {
        // 使用SSH密钥认证
        const expandedPath = this.expandPath(this.config.ssh.privateKeyPath)

        if (!existsSync(expandedPath)) {
          throw new Error(
            `SSH 私钥文件不存在: ${expandedPath} (原路径: ${this.config.ssh.privateKeyPath})`,
          )
        }

        connectConfig.privateKeyPath = expandedPath
        Logger.debug('使用SSH密钥认证')
      } else if (this.config.ssh.password) {
        // 使用密码认证
        connectConfig.password = this.config.ssh.password
        Logger.debug('使用密码认证')
      } else {
        throw new Error('请配置SSH认证方式：privateKeyPath 或 password')
      }

      await this.ssh.connect(connectConfig)

      Logger.success(`SSH 连接成功`)
    } catch (error) {
      throw new Error(`SSH 连接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  private async validateLocalFiles(): Promise<void> {
    const localPath = join(process.cwd(), this.config.paths.local)

    if (!existsSync(localPath)) {
      throw new Error(`本地构建目录不存在: ${localPath}`)
    }

    Logger.success(`本地文件验证通过: ${localPath}`)
  }

  private async syncFiles(): Promise<void> {
    const localPath = resolve(process.cwd(), this.config.paths.local)
    const remotePath = this.config.paths.remote

    // 构建 rsync 命令
    const excludeFlags = this.config.rsync.excludes
      .map(exclude => `--exclude="${exclude}"`)
      .join(' ')

    const rsyncFlags = [
      '-avz', // 归档模式，详细输出，压缩
      '--progress', // 显示进度
      '--human-readable', // 人类可读的输出
    ]

    // 根据配置决定是否删除远程的额外文件
    if (this.config.rsync.deleteExtraneous) {
      rsyncFlags.push('--delete')
    }

    // 如果是试运行模式
    if (this.config.rsync.dryRun) {
      rsyncFlags.push('--dry-run')
      Logger.warning('启用试运行模式，不会实际传输文件')
    }

    // 构建SSH选项，根据认证方式不同
    let sshOptions = `-p ${this.config.ssh.port} -o StrictHostKeyChecking=no`
    if (this.config.ssh.privateKeyPath) {
      const privateKeyPath = this.expandPath(this.config.ssh.privateKeyPath)
      sshOptions += ` -i ${privateKeyPath}`
    }
    // 注意：密码认证需要使用 sshpass 工具，这里先提醒用户
    if (this.config.ssh.password) {
      Logger.warning('密码认证需要在服务器上安装 sshpass 工具')
      Logger.warning('或者建议使用SSH密钥认证以获得更好的安全性')
    }

    const rsyncCommand = [
      'rsync',
      ...rsyncFlags,
      `-e "ssh ${sshOptions}"`,
      excludeFlags,
      `${localPath}/`, // 注意末尾的斜杠，表示同步目录内容而不是目录本身
      `${this.config.ssh.username}@${this.config.ssh.host}:${remotePath}/`,
    ]
      .filter(Boolean)
      .join(' ')

    Logger.info(`执行 rsync 命令...`)
    Logger.debug(`rsync 命令: ${rsyncCommand}`)

    try {
      const startTime = Date.now()

      execSync(rsyncCommand, {
        stdio: 'inherit',
        encoding: 'utf-8',
      })

      const duration = Date.now() - startTime
      Logger.success(`文件同步完成，耗时 ${duration}ms`)
    } catch (error) {
      throw new Error(`rsync 同步失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  private async ensureRemoteDirectory(): Promise<void> {
    Logger.info(`确保远程目录存在: ${this.config.paths.remote}`)

    try {
      await this.ssh.execCommand(`mkdir -p ${this.config.paths.remote}`)
      Logger.success('远程目录准备完成')
    } catch (error) {
      Logger.error('创建远程目录失败')
      throw error
    }
  }

  private async uploadFiles(): Promise<void> {
    const localServerPath = join(process.cwd(), 'packages/server')

    Logger.info(`开始上传 Server 文件...`)
    Logger.info(`本地路径: ${localServerPath}`)
    Logger.info(`远程路径: ${this.config.paths.remote}`)

    // 需要上传的文件和目录
    const filesToUpload = [
      'dist', // 构建输出
      'package.json', // 依赖配置
      'prisma', // 数据库配置
      'ecosystem.config.cjs', // PM2 配置
      'tsconfig.json', // TypeScript 配置
      '.env.example', // 环境变量示例
    ]

    // 检查并上传每个文件/目录
    for (const file of filesToUpload) {
      const localPath = join(localServerPath, file)

      if (existsSync(localPath)) {
        Logger.info(`上传: ${file}`)

        try {
          await this.ssh.putDirectory(localPath, `${this.config.paths.remote}/${file}`, {
            recursive: true,
            concurrency: 10,
            validate: itemPath => {
              // 排除不需要的文件
              return !this.config.rsync.excludes.some(exclude => itemPath.includes(exclude))
            },
          })
          Logger.success(`✓ ${file} 上传完成`)
        } catch (error) {
          // 如果是文件而不是目录，使用 putFile
          try {
            await this.ssh.putFile(localPath, `${this.config.paths.remote}/${file}`)
            Logger.success(`✓ ${file} 上传完成`)
          } catch (fileError) {
            Logger.error(`✗ ${file} 上传失败`)
            throw fileError
          }
        }
      } else {
        Logger.warning(`⚠ ${file} 不存在，跳过上传`)
      }
    }

    // 如果存在 public 目录，也上传
    const publicPath = join(localServerPath, 'public')
    if (existsSync(publicPath)) {
      Logger.info('上传 public 目录...')
      await this.ssh.putDirectory(publicPath, `${this.config.paths.remote}/public`, {
        recursive: true,
        concurrency: 10,
      })
      Logger.success('✓ public 目录上传完成')
    }
  }

  async runRemoteCommand(command: string): Promise<string> {
    Logger.info(`执行远程命令: ${command}`)

    try {
      // 使用 bash -l -c 来确保加载完整的登录环境（包括 nvm）
      const wrappedCommand = `bash -l -c "${command.replace(/"/g, '\\"')}"`

      const result = await this.ssh.execCommand(wrappedCommand, {
        cwd: this.config.paths.remote,
      })

      if (process.env.DEBUG && result.stdout) {
        Logger.info('命令输出:')
        console.log(result.stdout)
      }

      if (result.stderr && result.code !== 0) {
        Logger.warning('命令错误输出:')
        console.log(result.stderr)
      }

      if (result.code !== 0) {
        throw new Error(`命令执行失败 (退出码: ${result.code}): ${result.stderr || '未知错误'}`)
      }

      Logger.success('命令执行成功')
      return result.stdout || ''
    } catch (error) {
      Logger.error('远程命令执行失败')
      throw error
    }
  }

  async disconnect(): Promise<void> {
    Logger.info('SSH 连接已断开')
    this.ssh.dispose()
  }

  private expandPath(path?: string): string {
    if (!path) {
      throw new Error('路径不能为空')
    }
    // 展开 ~ 为用户主目录
    if (path.startsWith('~/')) {
      return join(homedir(), path.slice(2))
    }
    if (path === '~') {
      return homedir()
    }
    return resolve(path)
  }
}
