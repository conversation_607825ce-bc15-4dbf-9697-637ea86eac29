// 部署配置类型定义
export interface DeployConfig {
  // 环境名称
  environment: string
  // 项目类型
  type: 'web' | 'server'
  // SSH 连接配置
  ssh: {
    host: string
    port: number
    username: string
    // 认证方式：使用私钥或密码（二选一）
    privateKeyPath?: string // SSH 私钥文件路径（可选）
    password?: string // SSH 密码（可选）
  }
  // 部署路径配置
  paths: {
    local: string // 本地构建输出目录
    remote: string // 远程服务器目标目录
  }
  // rsync 配置
  rsync: {
    // 要排除的文件和目录
    excludes: string[]
    // 是否删除远程目录中不存在于本地的文件
    deleteExtraneous: boolean
    // 是否执行试运行（不实际传输文件）
    dryRun: boolean
  }
  // 构建配置
  build: {
    // 构建前是否清理
    clean: boolean
    // 构建命令
    command: string
  }
}

// 部署配置 - 按项目类型分组
export const deployConfig = {
  // Web 项目配置
  web: {
    production: {
      environment: 'production',
      type: 'web' as const,
      ssh: {
        host: 'your-server.com',
        port: 22,
        username: 'root',
        // 推荐使用密钥认证
        privateKeyPath: '~/.ssh/id_rsa',
        // 或使用密码认证
        // password: 'your-password',
      },
      paths: {
        local: './packages/web/dist',
        remote: '/var/www/your-app/',
      },
      rsync: {
        excludes: ['.git', 'node_modules', '*.log', '.env*', '.DS_Store'],
        deleteExtraneous: false, // 不删除远程的额外文件
        dryRun: false,
      },
      build: {
        clean: true,
        command: 'pnpm --filter web build',
      },
    },

    // 可以新增 测试环境配置
  },

  // Server 项目配置
  server: {
    production: {
      environment: 'production',
      type: 'server' as const,
      ssh: {
        host: 'your-server.com',
        port: 22,
        username: 'root',
        // 推荐使用密钥认证
        privateKeyPath: '~/.ssh/id_rsa',
        // 或使用密码认证
        // password: 'your-password',
      },
      paths: {
        local: './packages/server',
        remote: '/opt/your-app/',
      },
      rsync: {
        excludes: [
          '.git',
          'node_modules',
          '*.log',
          '.env', // 排除本地 .env，使用服务器上的配置
          '.DS_Store',
          'coverage',
          '*.test.js',
          '*.test.ts',
          'jest.config.js',
          'dev.db*', // 排除开发数据库
        ],
        deleteExtraneous: false, // 保留服务器上的额外文件（如 .env）
        dryRun: false,
      },
      build: {
        clean: true,
        command: 'pnpm --filter server build',
      },
    },

    // 可以新增 测试环境配置
  },
}
