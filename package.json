{"name": "proto-api", "private": true, "version": "0.1.0", "description": "Proto API Monorepo", "scripts": {"build": "pnpm -r build", "dev": "pnpm -r dev", "server:dev": "pnpm dev --filter server", "clean": "pnpm -r clean", "lint": "pnpm -r lint", "test": "pnpm -r test", "format": "prettier --write \"**/*.{js,ts,vue,json,md}\"", "deploy:web": "tsx scripts/deploy/deploy.ts web", "deploy:server": "tsx scripts/deploy/deploy.ts server"}, "keywords": [], "author": "", "license": "MIT", "engines": {"node": ">=16.0.0", "pnpm": ">=8.0.0"}, "dependencies": {"@mantine/charts": "^7.17.5", "@mantine/code-highlight": "^7.17.5", "@mantine/core": "^7.17.5", "@mantine/dates": "^7.17.5", "@mantine/dropzone": "^7.17.5", "@mantine/form": "^7.17.5", "@mantine/hooks": "^7.17.5", "@mantine/modals": "^7.17.5", "@mantine/notifications": "^7.17.5", "@mantine/spotlight": "^7.17.5", "@monaco-editor/react": "^4.7.0", "@prisma/client": "6.6.0", "@tabler/icons-react": "^3.31.0", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.4", "@tanstack/react-router": "^1.114.3", "@tanstack/react-router-devtools": "^1.114.3", "@tanstack/router-plugin": "^1.114.3", "@tanstack/zod-adapter": "^1.117.1", "@trpc/client": "^11.1.1", "@trpc/server": "^11.1.1", "@trpc/tanstack-react-query": "^11.1.1", "@types/semver": "^7.7.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "eval5": "^1.4.8", "framer-motion": "^12.9.2", "jose": "^6.0.11", "lodash-es": "^4.17.21", "monaco-editor": "^0.52.2", "nanoid": "^5.1.5", "prisma": "^6.6.0", "protobufjs": "^7.5.0", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "2", "semver": "^7.7.1", "tailwindcss": "^4.1.4", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.2", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "chalk": "^5.4.1", "depcheck": "^1.4.7", "jsdom": "^26.0.0", "node-ssh": "^13.2.1", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "tsx": "^4.19.3", "typescript": "^5.8.3", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}, "pnpm": {"onlyBuiltDependencies": ["@prisma/client", "@prisma/engines", "esbuild", "prisma"]}}